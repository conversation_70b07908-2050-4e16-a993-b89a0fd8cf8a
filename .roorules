# Cline Rules for C MCP Driver Implementation

## CRITICAL: Reference Driver Adherence
- NEVER embed logic from one reference method into another method
- Each method in our implementation must correspond EXACTLY to one method in the reference driver
- If reference has method `foo()` and `bar()`, implement them as separate `foo()` and `bar()` - do NOT combine their logic
- Keep method boundaries identical to reference driver
- If you're unsure about method structure, ASK to see the reference method before implementing

## Method Implementation Rules
- One reference method = One implementation method (1:1 mapping)
- Copy the exact function signature from reference (return type, name, parameters)
- Implement ONLY the logic that belongs to that specific method
- If reference method calls other methods, your implementation should also call those methods (don't inline)
- Keep methods focused and single-purpose like the reference

## MCP Integration Guidelines
- Add MCP calls at the SAME points where reference driver would log/report
- MCP calls are ADDITIONS to reference logic, not replacements
- Format: `mcp_log_info("method_name: description", data);`
- Place MCP calls at method entry, key decision points, and method exit
- Don't let MCP calls change the core logic flow

## Code Organization
- Match reference file structure exactly (same headers, same order of methods)
- Use identical variable names where possible
- Preserve reference commenting style and add MCP-specific comments only where needed
- Keep helper methods separate if reference has them separate

## Binary Ninja MCP Usage Rules
- Use Binary Ninja MCP ONLY for querying/decompiling reference driver methods
- MANDATORY: Before implementing any method, use Binary Ninja MCP to get the reference method's decompiled code
- Query format: Use MCP to decompile the specific method you're about to implement
- Do NOT use Binary Ninja MCP for any other purpose (logging, debugging, etc.)
- Always compare your implementation against the Binary Ninja decompiled reference before finalizing

## When Confused or Stuck
- STOP and use Binary Ninja MCP to decompile the specific reference method you're implementing
- Don't guess at method boundaries or logic flow - query the reference with Binary Ninja MCP first
- Ask for clarification on which reference methods should be called vs inlined
- Always get Binary Ninja decompilation before writing any method implementation

## Quality Checks Before Submitting
- Verify each method maps to exactly one reference method
- Confirm no reference logic is embedded in wrong methods
- Check that method call chains match reference driver
- Ensure MCP calls don't disrupt original logic flow