// SPDX-License-Identifier: GPL-2.0+
/*
 * ov9640.c
 * Copyright (C) 2022 Ingenic Semiconductor Co., Ltd.
 *
 * Settings:
 * sboot        resolution      fps       interface              mode
 *   0          1280*720        30        mipi_2lane            linear
 *   0          1280*960        30        mipi_2lane            linear
 *   0          1280*736        30        mipi_2lane            linear
 */

#include <linux/init.h>
#include <linux/module.h>
#include <linux/slab.h>
#include <linux/i2c.h>
#include <linux/delay.h>
#include <linux/gpio.h>
#include <linux/clk.h>
#include <linux/proc_fs.h>
#include <tx-isp-common.h>
#include <sensor-common.h>
#include <sensor-info.h>

#define SENSOR_NAME "ov9640"
#define SENSOR_CHIP_ID_H (0xa6)
#define SENSOR_CHIP_ID_L (0x40)
#define SENSOR_REG_END 0xffff
#define SENSOR_REG_DELAY 0xfffe
#define SENSOR_OUTPUT_MIN_FPS 5
#define SENSOR_VERSION "H20230920a"

static int reset_gpio = -1;
static int pwdn_gpio = -1;

struct regval_list {
    uint16_t reg_num;
    unsigned char value;
};

struct again_lut {
	unsigned int value;
	unsigned int gain;
};

struct again_lut sensor_again_lut[] = {
	{0x0100, 0},
	{0x0110, 5687},
	{0x0120, 11136},
	{0x0130, 16287},
	{0x0140, 21097},
	{0x0150, 25674},
	{0x0160, 30109},
	{0x0170, 34345},
	{0x0180, 38336},
	{0x0190, 42165},
	{0x01a0, 45904},
	{0x01b0, 49500},
	{0x01c0, 52910},
	{0x01d0, 56202},
	{0x01e0, 59433},
	{0x01f0, 62558},
	{0x1100, 65536},
	{0x1110, 71267},
	{0x1120, 76672},
	{0x1130, 81784},
	{0x1140, 86633},
	{0x1150, 91246},
	{0x1160, 95645},
	{0x1170, 99848},
	{0x1180, 103872},
	{0x1190, 107731},
	{0x11a0, 111440},
	{0x11b0, 115008},
	{0x11c0, 118446},
	{0x11d0, 121764},
	{0x11e0, 124969},
	{0x11f0, 128070},
	{0x2100, 131072},
	{0x2110, 136803},
	{0x2120, 142208},
	{0x2130, 147320},
	{0x2140, 152169},
	{0x2150, 156782},
	{0x2160, 161181},
	{0x2170, 165384},
	{0x2180, 169408},
	{0x2190, 173267},
	{0x21a0, 176976},
	{0x21b0, 180544},
	{0x21c0, 183982},
	{0x21d0, 187300},
	{0x21e0, 190505},
	{0x21f0, 193606},
	{0x3100, 196608},
	{0x3110, 202339},
	{0x3120, 207744},
	{0x3130, 212856},
	{0x3140, 217705},
	{0x3150, 222318},
	{0x3160, 226717},
	{0x3170, 230920},
	{0x3180, 234944},
	{0x3190, 238803},
	{0x31a0, 242512},
	{0x31b0, 246080},
	{0x31c0, 249518},
	{0x31d0, 252836},
	{0x31e0, 256041},
	{0x31f0, 259142},
};

struct tx_isp_sensor_attribute sensor_attr;

unsigned int sensor_alloc_again(unsigned int isp_gain, unsigned char shift, unsigned int *sensor_again)
{
	struct again_lut *lut = sensor_again_lut;
	while (lut->gain <= sensor_attr.max_again) {
		if (isp_gain == 0) {
			*sensor_again = lut[0].value;
			return lut[0].gain;
		}
		else if (isp_gain < lut->gain) {
			*sensor_again = (lut - 1)->value;
			return (lut - 1)->gain;
		}
		else {
			if ((lut->gain == sensor_attr.max_again) && (isp_gain >= lut->gain)) {
				*sensor_again = lut->value;
				return lut->gain;
			}
		}

		lut++;
	}

	return isp_gain;
}

unsigned int sensor_alloc_dgain(unsigned int isp_gain, unsigned char shift, unsigned int *sensor_dgain)
{
	return 0;
}

struct tx_isp_mipi_bus sensor_mipi={
	.mode = SENSOR_MIPI_OTHER_MODE,
	.clk = 784,
	.lans = 2,
	.settle_time_apative_en = 0,
	.mipi_sc.sensor_csi_fmt = TX_SENSOR_RAW12,//RAW10
	.mipi_sc.hcrop_diff_en = 0,
	.mipi_sc.mipi_vcomp_en = 0,
	.mipi_sc.mipi_hcomp_en = 0,
	.mipi_sc.line_sync_mode = 0,
	.mipi_sc.work_start_flag = 0,
	.image_twidth = 1280,
	.image_theight = 720,
	.mipi_sc.mipi_crop_start0x = 0,
	.mipi_sc.mipi_crop_start0y = 0,
	.mipi_sc.mipi_crop_start1x = 0,
	.mipi_sc.mipi_crop_start1y = 0,
	.mipi_sc.mipi_crop_start2x = 0,
	.mipi_sc.mipi_crop_start2y = 0,
	.mipi_sc.mipi_crop_start3x = 0,
	.mipi_sc.mipi_crop_start3y = 0,
	.mipi_sc.data_type_en = 0,
	.mipi_sc.data_type_value = RAW12,
	.mipi_sc.del_start = 0,
	.mipi_sc.sensor_frame_mode = TX_SENSOR_DEFAULT_FRAME_MODE,
	.mipi_sc.sensor_fid_mode = 0,
	.mipi_sc.sensor_mode = TX_SENSOR_DEFAULT_MODE,
};

struct tx_isp_mipi_bus sensor_mipi_960={
	.mode = SENSOR_MIPI_OTHER_MODE,
	.clk = 784,
	.lans = 2,
	.settle_time_apative_en = 0,
	.mipi_sc.sensor_csi_fmt = TX_SENSOR_RAW12,//RAW10
	.mipi_sc.hcrop_diff_en = 0,
	.mipi_sc.mipi_vcomp_en = 0,
	.mipi_sc.mipi_hcomp_en = 0,
	.mipi_sc.line_sync_mode = 0,
	.mipi_sc.work_start_flag = 0,
	.image_twidth = 1280,
	.image_theight = 960,
	.mipi_sc.mipi_crop_start0x = 0,
	.mipi_sc.mipi_crop_start0y = 0,
	.mipi_sc.mipi_crop_start1x = 0,
	.mipi_sc.mipi_crop_start1y = 0,
	.mipi_sc.mipi_crop_start2x = 0,
	.mipi_sc.mipi_crop_start2y = 0,
	.mipi_sc.mipi_crop_start3x = 0,
	.mipi_sc.mipi_crop_start3y = 0,
	.mipi_sc.data_type_en = 0,
	.mipi_sc.data_type_value = RAW12,
	.mipi_sc.del_start = 0,
	.mipi_sc.sensor_frame_mode = TX_SENSOR_DEFAULT_FRAME_MODE,
	.mipi_sc.sensor_fid_mode = 0,
	.mipi_sc.sensor_mode = TX_SENSOR_DEFAULT_MODE,
};

struct tx_isp_sensor_attribute sensor_attr={
	.name = SENSOR_NAME,
	.chip_id = 0xa640,
	.cbus_type = TX_SENSOR_CONTROL_INTERFACE_I2C,
	.cbus_mask = TISP_SBUS_MASK_SAMPLE_8BITS | TISP_SBUS_MASK_ADDR_16BITS,
	.cbus_device = 0x30,
	.max_again = 259142,
	.max_dgain = 0,
	.min_integration_time = 1,
	.min_integration_time_native = 1,
	.integration_time_apply_delay = 2,
	.again_apply_delay = 2,
	.dgain_apply_delay = 0,
	.sensor_ctrl.alloc_again = sensor_alloc_again,
	.sensor_ctrl.alloc_dgain = sensor_alloc_dgain,
};

static struct regval_list sensor_init_regs_1280_720_30fps_mipi[] = {
	{0x328a,0x11},
	{0x31be,0x01},
	{0x3133,0xb7},
	{0x3134,0xca},
	{0x3135,0xcc},
	{0x313f,0x80},
	{0x3132,0x24},
	{0x31bf,0x80},
	{0x3000,0x03},
	{0x3001,0x62},
	{0x3002,0x07},
	{0x3004,0x03},
	{0x3005,0x62},
	{0x3006,0x07},
	{0x3007,0x01},
	{0x3014,0x03},
	{0x3023,0x05},
	{0x3032,0x35},
	{0x3033,0x04},
	{0x3054,0x00},
	{0x3055,0x0f},
	{0x3056,0x01},
	{0x3057,0xff},
	{0x3058,0xaf},
	{0x3059,0x44},
	{0x305a,0x02},
	{0x305b,0x00},
	{0x305c,0x30},
	{0x305d,0x9e},
	{0x305e,0x19},
	{0x305f,0x18},
	{0x3060,0xf9},
	{0x3061,0xf0},
	{0x308c,0x03},
	{0x308f,0x10},
	{0x3090,0x00},
	{0x3091,0x00},
	{0x30eb,0x00},
	{0x30a3,0x08},
	{0x30ad,0x03},
	{0x30ae,0x80},
	{0x30af,0x80},
	{0x30b0,0xff},
	{0x30b1,0x3f},
	{0x30b2,0x22},
	{0x30b9,0x22},
	{0x30bb,0x00},
	{0x30bc,0x00},
	{0x30bd,0x00},
	{0x30be,0x00},
	{0x30bf,0x00},
	{0x30c0,0x00},
	{0x30c1,0x00},
	{0x30c2,0x00},
	{0x30c3,0x00},
	{0x30c4,0x80},
	{0x30c5,0x00},
	{0x30c6,0x80},
	{0x30c7,0x00},
	{0x30c8,0x80},
	{0x3119,0x55},
	{0x311a,0x01},
	{0x311b,0x4a},
	{0x3074,0x00},
	{0x3075,0x00},
	{0x3076,0x00},
	{0x3077,0x02},
	{0x3078,0x05},
	{0x3079,0x07},
	{0x307a,0x04},
	{0x307b,0x41},
	{0x307c,0x05},
	{0x307d,0x00},
	{0x307e,0x04},
	{0x307f,0x38},
	{0x3080,0x05},//hts = 0x5be = 1470
	{0x3081,0xbe},//
	{0x3082,0x04},//vts = 0x457 = 1111
	{0x3083,0x57},//
	{0x3084,0x00},
	{0x3085,0x04},
	{0x3086,0x00},
	{0x3087,0x04},
	{0x3088,0x00},
	{0x3089,0x40},
	{0x308d,0x92},
	{0x3094,0xa5},
	{0x30e6,0x04},
	{0x30e7,0x48},
	{0x30e8,0x04},
	{0x30e9,0x48},
	{0x30ea,0x11},
	{0x30ec,0x01},
	{0x30fa,0x06},
	{0x3120,0x00},
	{0x3121,0x01},
	{0x3122,0x00},
	{0x3127,0x63},
	{0x3128,0xc0},
	{0x3129,0x00},
	{0x31be,0x01},
	{0x30a5,0x78},
	{0x30a6,0x40},
	{0x30a7,0x78},
	{0x30a8,0x80},
	{0x30a9,0x79},
	{0x30aa,0x00},
	{0x30ab,0x79},
	{0x30ac,0xf8},
	{0x3440,0x04},
	{0x3444,0x48},
	{0x344e,0x2c},
	{0x3457,0x33},
	{0x345e,0x38},
	{0x3461,0xa8},
	{0x3474,0x3f},
	{0x7002,0xaa},
	{0x7001,0xdf},
	{0x7048,0x00},
	{0x7049,0x02},
	{0x704a,0x02},
	{0x704b,0x00},
	{0x704c,0x01},
	{0x704d,0x00},
	{0x7043,0x04},
	{0x7040,0x3c},
	{0x7047,0x00},
	{0x7044,0x01},
	{0x7000,0x1f},
	{0x7084,0x01},
	{0x7085,0x03},
	{0x7086,0x02},
	{0x7087,0x40},
	{0x7088,0x01},
	{0x7089,0x20},
	{0x707f,0x04},
	{0x707c,0x3c},
	{0x7083,0x00},
	{0x7080,0x01},
	{0x7003,0xdf},
	{0x70c0,0x00},
	{0x70c1,0x02},
	{0x70c2,0x02},
	{0x70c3,0x00},
	{0x70c4,0x01},
	{0x70c5,0x00},
	{0x70b8,0x03},
	{0x70b9,0x98},
	{0x70bc,0x00},
	{0x70bd,0x80},
	{0x7004,0x02},
	{0x7005,0x00},
	{0x7006,0x01},
	{0x7007,0x80},
	{0x7008,0x02},
	{0x7009,0x00},
	{0x700a,0x04},
	{0x700b,0x00},
	{0x700e,0x00},
	{0x700f,0x60},
	{0x701a,0x02},
	{0x701b,0x00},
	{0x701c,0x01},
	{0x701d,0x80},
	{0x701e,0x02},
	{0x701f,0x00},
	{0x7020,0x04},
	{0x7021,0x00},
	{0x7024,0x00},
	{0x7025,0x60},
	{0x70e7,0x00},
	{0x70e4,0x10},
	{0x70e5,0x00},
	{0x70e6,0x00},
	{0x70eb,0x00},
	{0x70e8,0x10},
	{0x70e9,0x00},
	{0x70ea,0x00},
	{0x70ef,0x00},
	{0x70ec,0xfd},
	{0x70ed,0x00},
	{0x70ee,0x00},
	{0x70eb,0x00},
	{0x70f0,0xfd},
	{0x70f1,0x00},
	{0x70f2,0x00},
	{0x30fb,0x06},
	{0x30fc,0x80},
	{0x30fd,0x02},
	{0x30fe,0x93},
	{0x6000,0xc1},
	{0x6001,0xb9},
	{0x6002,0xba},
	{0x6003,0xa4},
	{0x6004,0xa4},
	{0x6005,0xb5},
	{0x6006,0xa0},
	{0x6007,0x82},
	{0x6008,0xa7},
	{0x6009,0xa7},
	{0x600a,0xb7},
	{0x600b,0x5c},
	{0x600c,0x9e},
	{0x600d,0xc0},
	{0x600e,0xd2},
	{0x600f,0x33},
	{0x6010,0xcc},
	{0x6011,0xe2},
	{0x6012,0xc1},
	{0x6013,0xab},
	{0x6014,0xab},
	{0x6015,0xb7},
	{0x6016,0x00},
	{0x6017,0x00},
	{0x6018,0x00},
	{0x6019,0x00},
	{0x601a,0x00},
	{0x601b,0x00},
	{0x601c,0x00},
	{0x601d,0x00},
	{0x601e,0x00},
	{0x601f,0x00},
	{0x6020,0x00},
	{0x6021,0x00},
	{0x6022,0x00},
	{0x6023,0x9c},
	{0x6024,0x94},
	{0x6025,0x90},
	{0x6026,0xc5},
	{0x6027,0x00},
	{0x6028,0x54},
	{0x6029,0x2a},
	{0x602a,0x61},
	{0x602b,0xd2},
	{0x602c,0xcc},
	{0x602d,0x04},
	{0x602e,0x35},
	{0x602f,0xb1},
	{0x6030,0xb2},
	{0x6031,0xb3},
	{0x6032,0xd2},
	{0x6033,0xd3},
	{0x6034,0x11},
	{0x6035,0x31},
	{0x6036,0xcc},
	{0x6037,0x06},
	{0x6038,0xd2},
	{0x6039,0x00},
	{0x603a,0xce},
	{0x603b,0x18},
	{0x603c,0xcf},
	{0x603d,0x1e},
	{0x603e,0xd0},
	{0x603f,0x24},
	{0x6040,0xc5},
	{0x6041,0xd2},
	{0x6042,0xbc},
	{0x6043,0xcc},
	{0x6044,0x52},
	{0x6045,0x2b},
	{0x6046,0xd2},
	{0x6047,0xd3},
	{0x6048,0x01},
	{0x6049,0xcc},
	{0x604a,0x0a},
	{0x604b,0xd2},
	{0x604c,0xd3},
	{0x604d,0x0f},
	{0x604e,0x1a},
	{0x604f,0x2a},
	{0x6050,0xd4},
	{0x6051,0xe3},
	{0x6052,0xba},
	{0x6053,0x56},
	{0x6054,0xd3},
	{0x6055,0x2e},
	{0x6056,0x54},
	{0x6057,0x26},
	{0x6058,0xd2},
	{0x6059,0xcc},
	{0x605a,0x60},
	{0x605b,0xd2},
	{0x605c,0xd3},
	{0x605d,0x27},
	{0x605e,0x27},
	{0x605f,0x08},
	{0x6060,0x1a},
	{0x6061,0xcc},
	{0x6062,0x88},
	{0x6063,0x00},
	{0x6064,0x12},
	{0x6065,0x2c},
	{0x6066,0x60},
	{0x6067,0xc2},
	{0x6068,0xb9},
	{0x6069,0xa5},
	{0x606a,0xa5},
	{0x606b,0xb5},
	{0x606c,0xa0},
	{0x606d,0x82},
	{0x606e,0x5c},
	{0x606f,0xd4},
	{0x6070,0xab},
	{0x6071,0xd4},
	{0x6072,0xab},
	{0x6073,0xd3},
	{0x6074,0x01},
	{0x6075,0x7c},
	{0x6076,0x74},
	{0x6077,0x00},
	{0x6078,0x61},
	{0x6079,0x2a},
	{0x607a,0xd2},
	{0x607b,0xcc},
	{0x607c,0xdf},
	{0x607d,0xc6},
	{0x607e,0x35},
	{0x607f,0xd2},
	{0x6080,0xcc},
	{0x6081,0x06},
	{0x6082,0x31},
	{0x6083,0xd2},
	{0x6084,0x00},
	{0x6085,0xbb},
	{0x6086,0xcc},
	{0x6087,0x18},
	{0x6088,0xc6},
	{0x6089,0xd2},
	{0x608a,0xbd},
	{0x608b,0xcc},
	{0x608c,0x52},
	{0x608d,0x2b},
	{0x608e,0xd2},
	{0x608f,0xd3},
	{0x6090,0x01},
	{0x6091,0xcc},
	{0x6092,0x0a},
	{0x6093,0xd2},
	{0x6094,0xd3},
	{0x6095,0x0f},
	{0x6096,0x1a},
	{0x6097,0x71},
	{0x6098,0x2a},
	{0x6099,0xd4},
	{0x609a,0xe3},
	{0x609b,0xd3},
	{0x609c,0x22},
	{0x609d,0x70},
	{0x609e,0xca},
	{0x609f,0x26},
	{0x60a0,0xd2},
	{0x60a1,0xcc},
	{0x60a2,0x60},
	{0x60a3,0xd2},
	{0x60a4,0xd3},
	{0x60a5,0x27},
	{0x60a6,0x27},
	{0x60a7,0x08},
	{0x60a8,0x1a},
	{0x60a9,0xcc},
	{0x60aa,0x88},
	{0x60ab,0x00},
	{0x60ac,0x12},
	{0x60ad,0x2c},
	{0x60ae,0x60},
	{0x60af,0x00},
	{0x60b0,0x00},
	{0x60b1,0xc0},
	{0x60b2,0xb9},
	{0x60b3,0xa3},
	{0x60b4,0xa3},
	{0x60b5,0xb5},
	{0x60b6,0x00},
	{0x60b7,0xa0},
	{0x60b8,0x82},
	{0x60b9,0x5c},
	{0x60ba,0xd4},
	{0x60bb,0x8b},
	{0x60bc,0x9d},
	{0x60bd,0xd3},
	{0x60be,0x21},
	{0x60bf,0xb0},
	{0x60c0,0xb0},
	{0x60c1,0xb7},
	{0x60c2,0x05},
	{0x60c3,0xd3},
	{0x60c4,0x0a},
	{0x60c5,0xd3},
	{0x60c6,0x10},
	{0x60c7,0x9c},
	{0x60c8,0x94},
	{0x60c9,0x90},
	{0x60ca,0xc8},
	{0x60cb,0xba},
	{0x60cc,0x7c},
	{0x60cd,0x74},
	{0x60ce,0x00},
	{0x60cf,0x61},
	{0x60d0,0x2a},
	{0x60d1,0x00},
	{0x60d2,0xd2},
	{0x60d3,0xcc},
	{0x60d4,0xdf},
	{0x60d5,0xc4},
	{0x60d6,0x35},
	{0x60d7,0xd3},
	{0x60d8,0x13},
	{0x60d9,0xd2},
	{0x60da,0xcc},
	{0x60db,0x06},
	{0x60dc,0x31},
	{0x60dd,0xd2},
	{0x60de,0xcc},
	{0x60df,0x15},
	{0x60e0,0xd2},
	{0x60e1,0xbb},
	{0x60e2,0xcc},
	{0x60e3,0x1a},
	{0x60e4,0xd2},
	{0x60e5,0xbe},
	{0x60e6,0xce},
	{0x60e7,0x52},
	{0x60e8,0xcf},
	{0x60e9,0x56},
	{0x60ea,0xd0},
	{0x60eb,0x5b},
	{0x60ec,0x2b},
	{0x60ed,0xd2},
	{0x60ee,0xd3},
	{0x60ef,0x01},
	{0x60f0,0xcc},
	{0x60f1,0x0a},
	{0x60f2,0xd2},
	{0x60f3,0xd3},
	{0x60f4,0x0f},
	{0x60f5,0xd9},
	{0x60f6,0xb4},
	{0x60f7,0xda},
	{0x60f8,0xbb},
	{0x60f9,0x1a},
	{0x60fa,0xd4},
	{0x60fb,0xe3},
	{0x60fc,0xd4},
	{0x60fd,0x96},
	{0x60fe,0x27},
	{0x60ff,0x00},
	{0x6100,0xd2},
	{0x6101,0xcc},
	{0x6102,0x60},
	{0x6103,0xd2},
	{0x6104,0xd3},
	{0x6105,0x2d},
	{0x6106,0xd9},
	{0x6107,0xcc},
	{0x6108,0xda},
	{0x6109,0xd2},
	{0x610a,0x1a},
	{0x610b,0x12},
	{0x610c,0xcc},
	{0x610d,0x88},
	{0x610e,0xd6},
	{0x610f,0x9e},
	{0x6110,0xb9},
	{0x6111,0xba},
	{0x6112,0xaf},
	{0x6113,0xdc},
	{0x6114,0x00},
	{0x6115,0xd5},
	{0x6116,0xba},
	{0x6117,0x00},
	{0x6118,0x00},
	{0x6119,0x00},
	{0x611a,0x00},
	{0x611b,0x00},
	{0x611c,0x00},
	{0x611d,0x00},
	{0x611e,0x00},
	{0x611f,0xaa},
	{0x6120,0xaa},
	{0x6121,0xb7},
	{0x6122,0x00},
	{0x6123,0x00},
	{0x6124,0x00},
	{0x6125,0x00},
	{0x6126,0x00},
	{0x6127,0xa6},
	{0x6128,0xa6},
	{0x6129,0xb7},
	{0x612a,0x00},
	{0x612b,0xd5},
	{0x612c,0x71},
	{0x612d,0xd3},
	{0x612e,0x30},
	{0x612f,0xba},
	{0x6130,0x00},
	{0x6131,0x00},
	{0x6132,0x00},
	{0x6133,0x00},
	{0x6134,0xd3},
	{0x6135,0x10},
	{0x6136,0x70},
	{0x6137,0x00},
	{0x6138,0x00},
	{0x6139,0x00},
	{0x613a,0x00},
	{0x613b,0xd5},
	{0x613c,0xba},
	{0x613d,0xb0},
	{0x613e,0xb0},
	{0x613f,0xb7},
	{0x6140,0x9d},
	{0x6141,0x02},
	{0x6142,0xd3},
	{0x6143,0x0a},
	{0x6144,0x9d},
	{0x6145,0x9d},
	{0x6146,0xd3},
	{0x6147,0x10},
	{0x6148,0x9c},
	{0x6149,0x94},
	{0x614a,0x90},
	{0x614b,0xc8},
	{0x614c,0xba},
	{0x614d,0xd2},
	{0x614e,0x60},
	{0x614f,0x2c},
	{0x6150,0x50},
	{0x6151,0x11},
	{0x6152,0xcc},
	{0x6153,0x00},
	{0x6154,0x30},
	{0x6155,0xd5},
	{0x6156,0xba},
	{0x6157,0xb0},
	{0x6158,0xb0},
	{0x6159,0xb7},
	{0x615a,0x9d},
	{0x615b,0x02},
	{0x615c,0xd3},
	{0x615d,0x0a},
	{0x615e,0x9d},
	{0x615f,0x9d},
	{0x6160,0xd3},
	{0x6161,0x10},
	{0x6162,0x9c},
	{0x6163,0x94},
	{0x6164,0x90},
	{0x6165,0xc8},
	{0x6166,0xba},
	{0x6167,0xd5},
	{0x6168,0x01},
	{0x6169,0x1a},
	{0x616a,0xcc},
	{0x616b,0x12},
	{0x616c,0x12},
	{0x616d,0x00},
	{0x616e,0xcc},
	{0x616f,0x9c},
	{0x6170,0xd2},
	{0x6171,0xcc},
	{0x6172,0x60},
	{0x6173,0xd2},
	{0x6174,0x04},
	{0x6175,0xd5},
	{0x6176,0x1a},
	{0x6177,0xcc},
	{0x6178,0x12},
	{0x6179,0x00},
	{0x617a,0x12},
	{0x617b,0xcc},
	{0x617c,0x9c},
	{0x617d,0xd2},
	{0x617e,0xcc},
	{0x617f,0x60},
	{0x6180,0xd2},
	{0x6181,0x1a},
	{0x6182,0xcc},
	{0x6183,0x12},
	{0x6184,0x00},
	{0x6185,0x12},
	{0x6186,0xcc},
	{0x6187,0x9c},
	{0x6188,0xd2},
	{0x6189,0xcc},
	{0x618a,0x60},
	{0x618b,0xd2},
	{0x618c,0x1a},
	{0x618d,0xcc},
	{0x618e,0x12},
	{0x618f,0x00},
	{0x6190,0x12},
	{0x6191,0xcc},
	{0x6192,0x9c},
	{0x6193,0xd2},
	{0x6194,0xcc},
	{0x6195,0x60},
	{0x6196,0xd2},
	{0x6197,0xd5},
	{0x6198,0x1a},
	{0x6199,0xcc},
	{0x619a,0x12},
	{0x619b,0x12},
	{0x619c,0x00},
	{0x619d,0xcc},
	{0x619e,0x8a},
	{0x619f,0xd2},
	{0x61a0,0xcc},
	{0x61a1,0x74},
	{0x61a2,0xd2},
	{0x61a3,0xd5},
	{0x61a4,0x1a},
	{0x61a5,0xcc},
	{0x61a6,0x12},
	{0x61a7,0x00},
	{0x61a8,0x12},
	{0x61a9,0xcc},
	{0x61aa,0x8a},
	{0x61ab,0xd2},
	{0x61ac,0xcc},
	{0x61ad,0x74},
	{0x61ae,0xd2},
	{0x61af,0x1a},
	{0x61b0,0xcc},
	{0x61b1,0x12},
	{0x61b2,0x00},
	{0x61b3,0x12},
	{0x61b4,0xcc},
	{0x61b5,0x8a},
	{0x61b6,0xd2},
	{0x61b7,0xcc},
	{0x61b8,0x74},
	{0x61b9,0xd2},
	{0x61ba,0x1a},
	{0x61bb,0xcc},
	{0x61bc,0x12},
	{0x61bd,0x00},
	{0x61be,0x12},
	{0x61bf,0xcc},
	{0x61c0,0x8a},
	{0x61c1,0xd2},
	{0x61c2,0xcc},
	{0x61c3,0x74},
	{0x61c4,0xd2},
	{0x61c5,0xd5},
	{0x61c6,0xcc},
	{0x61c7,0x12},
	{0x61c8,0x00},
	{0x61c9,0x12},
	{0x61ca,0xcc},
	{0x61cb,0x9c},
	{0x61cc,0xd5},
	{0x6400,0x04},
	{0x6401,0x04},
	{0x6402,0x00},
	{0x6403,0xff},
	{0x6404,0x00},
	{0x6405,0x08},
	{0x6406,0x00},
	{0x6407,0xff},
	{0x6408,0x04},
	{0x6409,0x70},
	{0x640a,0x00},
	{0x640b,0xff},
	{0x640c,0x05},
	{0x640d,0x14},
	{0x640e,0x04},
	{0x640f,0x71},
	{0x6410,0x05},
	{0x6411,0x74},
	{0x6412,0x00},
	{0x6413,0xff},
	{0x6414,0x05},
	{0x6415,0x54},
	{0x6416,0x05},
	{0x6417,0x44},
	{0x6418,0x04},
	{0x6419,0x30},
	{0x641a,0x05},
	{0x641b,0x46},
	{0x641c,0x00},
	{0x641d,0xff},
	{0x641e,0x04},
	{0x641f,0x31},
	{0x6420,0x04},
	{0x6421,0x30},
	{0x6422,0x00},
	{0x6423,0xff},
	{0x6424,0x04},
	{0x6425,0x20},
	{0x6426,0x05},
	{0x6427,0x06},
	{0x6428,0x00},
	{0x6429,0xff},
	{0x642a,0x08},
	{0x642b,0x2a},
	{0x642c,0x08},
	{0x642d,0x31},
	{0x642e,0x00},
	{0x642f,0xff},
	{0x6430,0x08},
	{0x6431,0x2a},
	{0x6432,0x08},
	{0x6433,0x31},
	{0x6434,0x06},
	{0x6435,0x20},
	{0x6436,0x07},
	{0x6437,0x00},
	{0x6438,0x08},
	{0x6439,0x40},
	{0x643a,0x00},
	{0x643b,0xff},
	{0x643c,0x08},
	{0x643d,0x2a},
	{0x643e,0x08},
	{0x643f,0x36},
	{0x6440,0x06},
	{0x6441,0x10},
	{0x6442,0x07},
	{0x6443,0x00},
	{0x6444,0x08},
	{0x6445,0x40},
	{0x6446,0x00},
	{0x6447,0xff},
	{0x6448,0x08},
	{0x6449,0x2a},
	{0x644a,0x08},
	{0x644b,0x3b},
	{0x644c,0x06},
	{0x644d,0x00},
	{0x644e,0x07},
	{0x644f,0x00},
	{0x6450,0x08},
	{0x6451,0x40},
	{0x6452,0x00},
	{0x6453,0xff},
	{0x6454,0x06},
	{0x6455,0x00},
	{0x6456,0x07},
	{0x6457,0x05},
	{0x6458,0x01},
	{0x6459,0xaf},
	{0x645a,0x01},
	{0x645b,0x0f},
	{0x645c,0x01},
	{0x645d,0x90},
	{0x645e,0x01},
	{0x645f,0xc8},
	{0x6460,0x00},
	{0x6461,0xff},
	{0x6462,0x01},
	{0x6463,0xac},
	{0x6464,0x01},
	{0x6465,0x0c},
	{0x6466,0x01},
	{0x6467,0x90},
	{0x6468,0x01},
	{0x6469,0xe8},
	{0x646a,0x00},
	{0x646b,0xff},
	{0x646c,0x01},
	{0x646d,0xad},
	{0x646e,0x01},
	{0x646f,0x0d},
	{0x6470,0x01},
	{0x6471,0x90},
	{0x6472,0x01},
	{0x6473,0xe8},
	{0x6474,0x00},
	{0x6475,0xff},
	{0x6476,0x01},
	{0x6477,0xae},
	{0x6478,0x01},
	{0x6479,0x0e},
	{0x647a,0x01},
	{0x647b,0x90},
	{0x647c,0x01},
	{0x647d,0xe8},
	{0x647e,0x00},
	{0x647f,0xff},
	{0x6480,0x01},
	{0x6481,0xb0},
	{0x6482,0x01},
	{0x6483,0xb1},
	{0x6484,0x01},
	{0x6485,0xb2},
	{0x6486,0x01},
	{0x6487,0xb3},
	{0x6488,0x01},
	{0x6489,0xb4},
	{0x648a,0x01},
	{0x648b,0xb5},
	{0x648c,0x01},
	{0x648d,0xb6},
	{0x648e,0x01},
	{0x648f,0xb7},
	{0x6490,0x01},
	{0x6491,0xb8},
	{0x6492,0x01},
	{0x6493,0xb9},
	{0x6494,0x01},
	{0x6495,0xba},
	{0x6496,0x01},
	{0x6497,0xbb},
	{0x6498,0x01},
	{0x6499,0xbc},
	{0x649a,0x01},
	{0x649b,0xbd},
	{0x649c,0x01},
	{0x649d,0xbe},
	{0x649e,0x01},
	{0x649f,0xbf},
	{0x64a0,0x01},
	{0x64a1,0xc0},
	{0x64a2,0x00},
	{0x64a3,0xff},
	{0x64a4,0x06},
	{0x64a5,0x00},
	{0x64a6,0x01},
	{0x64a7,0xf6},
	{0x64a8,0x04},
	{0x64a9,0x30},
	{0x64aa,0x00},
	{0x64ab,0xff},
	{0x64ac,0x06},
	{0x64ad,0x10},
	{0x64ae,0x01},
	{0x64af,0xf6},
	{0x64b0,0x04},
	{0x64b1,0x30},
	{0x64b2,0x06},
	{0x64b3,0x00},
	{0x64b4,0x00},
	{0x64b5,0xff},
	{0x64b6,0x06},
	{0x64b7,0x20},
	{0x64b8,0x01},
	{0x64b9,0xf6},
	{0x64ba,0x04},
	{0x64bb,0x30},
	{0x64bc,0x06},
	{0x64bd,0x00},
	{0x64be,0x00},
	{0x64bf,0xff},
	{0x64c0,0x04},
	{0x64c1,0x31},
	{0x64c2,0x04},
	{0x64c3,0x30},
	{0x64c4,0x01},
	{0x64c5,0x20},
	{0x64c6,0x01},
	{0x64c7,0x31},
	{0x64c8,0x01},
	{0x64c9,0x32},
	{0x64ca,0x01},
	{0x64cb,0x33},
	{0x64cc,0x01},
	{0x64cd,0x34},
	{0x64ce,0x01},
	{0x64cf,0x35},
	{0x64d0,0x01},
	{0x64d1,0x36},
	{0x64d2,0x01},
	{0x64d3,0x37},
	{0x64d4,0x01},
	{0x64d5,0x38},
	{0x64d6,0x01},
	{0x64d7,0x39},
	{0x64d8,0x01},
	{0x64d9,0x3a},
	{0x64da,0x01},
	{0x64db,0x3b},
	{0x64dc,0x01},
	{0x64dd,0x3c},
	{0x64de,0x01},
	{0x64df,0x3d},
	{0x64e0,0x01},
	{0x64e1,0x3e},
	{0x64e2,0x01},
	{0x64e3,0x3f},
	{0x64e4,0x02},
	{0x64e5,0xa0},
	{0x64e6,0x00},
	{0x64e7,0xff},
	{0x64e8,0x04},
	{0x64e9,0x31},
	{0x64ea,0x04},
	{0x64eb,0x30},
	{0x64ec,0x01},
	{0x64ed,0x00},
	{0x64ee,0x01},
	{0x64ef,0x11},
	{0x64f0,0x01},
	{0x64f1,0x12},
	{0x64f2,0x01},
	{0x64f3,0x13},
	{0x64f4,0x01},
	{0x64f5,0x14},
	{0x64f6,0x01},
	{0x64f7,0x15},
	{0x64f8,0x01},
	{0x64f9,0x16},
	{0x64fa,0x01},
	{0x64fb,0x17},
	{0x64fc,0x01},
	{0x64fd,0x18},
	{0x64fe,0x01},
	{0x64ff,0x19},
	{0x6500,0x01},
	{0x6501,0x1a},
	{0x6502,0x01},
	{0x6503,0x1b},
	{0x6504,0x01},
	{0x6505,0x1c},
	{0x6506,0x01},
	{0x6507,0x1d},
	{0x6508,0x01},
	{0x6509,0x1e},
	{0x650a,0x01},
	{0x650b,0x1f},
	{0x650c,0x02},
	{0x650d,0xa0},
	{0x650e,0x00},
	{0x650f,0xff},
	{0x6510,0x04},
	{0x6511,0x20},
	{0x6512,0x05},
	{0x6513,0x86},
	{0x6514,0x03},
	{0x6515,0x0b},
	{0x6516,0x05},
	{0x6517,0x86},
	{0x6518,0x00},
	{0x6519,0x00},
	{0x651a,0x05},
	{0x651b,0x06},
	{0x651c,0x00},
	{0x651d,0x04},
	{0x651e,0x05},
	{0x651f,0x04},
	{0x6520,0x00},
	{0x6521,0x04},
	{0x6522,0x05},
	{0x6523,0x00},
	{0x6524,0x05},
	{0x6525,0x0a},
	{0x6526,0x03},
	{0x6527,0x9a},
	{0x6528,0x05},
	{0x6529,0x86},
	{0x652a,0x00},
	{0x652b,0x00},
	{0x652c,0x05},
	{0x652d,0x06},
	{0x652e,0x00},
	{0x652f,0x01},
	{0x6530,0x05},
	{0x6531,0x04},
	{0x6532,0x00},
	{0x6533,0x04},
	{0x6534,0x05},
	{0x6535,0x00},
	{0x6536,0x05},
	{0x6537,0x0a},
	{0x6538,0x03},
	{0x6539,0x99},
	{0x653a,0x05},
	{0x653b,0x06},
	{0x653c,0x00},
	{0x653d,0x00},
	{0x653e,0x05},
	{0x653f,0x04},
	{0x6540,0x00},
	{0x6541,0x04},
	{0x6542,0x05},
	{0x6543,0x00},
	{0x6544,0x05},
	{0x6545,0x0a},
	{0x6546,0x03},
	{0x6547,0x98},
	{0x6548,0x05},
	{0x6549,0x06},
	{0x654a,0x00},
	{0x654b,0x00},
	{0x654c,0x05},
	{0x654d,0x04},
	{0x654e,0x00},
	{0x654f,0x04},
	{0x6550,0x05},
	{0x6551,0x00},
	{0x6552,0x05},
	{0x6553,0x0a},
	{0x6554,0x03},
	{0x6555,0x97},
	{0x6556,0x05},
	{0x6557,0x06},
	{0x6558,0x05},
	{0x6559,0x04},
	{0x655a,0x00},
	{0x655b,0x04},
	{0x655c,0x05},
	{0x655d,0x00},
	{0x655e,0x05},
	{0x655f,0x0a},
	{0x6560,0x03},
	{0x6561,0x96},
	{0x6562,0x05},
	{0x6563,0x06},
	{0x6564,0x05},
	{0x6565,0x04},
	{0x6566,0x00},
	{0x6567,0x04},
	{0x6568,0x05},
	{0x6569,0x00},
	{0x656a,0x05},
	{0x656b,0x0a},
	{0x656c,0x03},
	{0x656d,0x95},
	{0x656e,0x05},
	{0x656f,0x06},
	{0x6570,0x05},
	{0x6571,0x04},
	{0x6572,0x00},
	{0x6573,0x04},
	{0x6574,0x05},
	{0x6575,0x00},
	{0x6576,0x05},
	{0x6577,0x0a},
	{0x6578,0x03},
	{0x6579,0x94},
	{0x657a,0x05},
	{0x657b,0x06},
	{0x657c,0x00},
	{0x657d,0x00},
	{0x657e,0x05},
	{0x657f,0x04},
	{0x6580,0x00},
	{0x6581,0x04},
	{0x6582,0x05},
	{0x6583,0x00},
	{0x6584,0x05},
	{0x6585,0x0a},
	{0x6586,0x03},
	{0x6587,0x93},
	{0x6588,0x05},
	{0x6589,0x06},
	{0x658a,0x00},
	{0x658b,0x00},
	{0x658c,0x05},
	{0x658d,0x04},
	{0x658e,0x00},
	{0x658f,0x04},
	{0x6590,0x05},
	{0x6591,0x00},
	{0x6592,0x05},
	{0x6593,0x0a},
	{0x6594,0x03},
	{0x6595,0x92},
	{0x6596,0x05},
	{0x6597,0x06},
	{0x6598,0x05},
	{0x6599,0x04},
	{0x659a,0x00},
	{0x659b,0x04},
	{0x659c,0x05},
	{0x659d,0x00},
	{0x659e,0x05},
	{0x659f,0x0a},
	{0x65a0,0x03},
	{0x65a1,0x91},
	{0x65a2,0x05},
	{0x65a3,0x06},
	{0x65a4,0x05},
	{0x65a5,0x04},
	{0x65a6,0x00},
	{0x65a7,0x04},
	{0x65a8,0x05},
	{0x65a9,0x00},
	{0x65aa,0x05},
	{0x65ab,0x0a},
	{0x65ac,0x03},
	{0x65ad,0x90},
	{0x65ae,0x05},
	{0x65af,0x06},
	{0x65b0,0x05},
	{0x65b1,0x04},
	{0x65b2,0x00},
	{0x65b3,0x04},
	{0x65b4,0x05},
	{0x65b5,0x00},
	{0x65b6,0x05},
	{0x65b7,0x0a},
	{0x65b8,0x02},
	{0x65b9,0x90},
	{0x65ba,0x05},
	{0x65bb,0x06},
	{0x65bc,0x00},
	{0x65bd,0xff},
	{0x65be,0x04},
	{0x65bf,0x70},
	{0x65c0,0x08},
	{0x65c1,0x76},
	{0x65c2,0x00},
	{0x65c3,0xff},
	{0x65c4,0x08},
	{0x65c5,0x76},
	{0x65c6,0x04},
	{0x65c7,0x0c},
	{0x65c8,0x05},
	{0x65c9,0x07},
	{0x65ca,0x04},
	{0x65cb,0x04},
	{0x65cc,0x00},
	{0x65cd,0xff},
	{0x65ce,0x00},
	{0x65cf,0xff},
	{0x65d0,0x00},
	{0x65d1,0xff},
	{0x30eb,0x04},
	{0x30ed,0x5a},
	{0x30ee,0x01},
	{0x30ef,0x80},
	{0x30f1,0x5a},
	{0x303a,0x04},
	{0x303b,0x7f},
	{0x303c,0xfe},
	{0x303d,0x19},
	{0x303e,0xd7},
	{0x303f,0x09},
	{0x3040,0x78},
	{0x3042,0x05},
	{0x328a,0x00},
	{0x3012,0x01},
	{0x3012,0x00},
	{0x3000,0x03},
	{0x3001,0x62},
	{0x3002,0x07},
	{0x3004,0x03},
	{0x3005,0x62},
	{0x3006,0x07},
	{0x308f,0x10},
	{0x3127,0x63},
	{0x3074,0x00},
	{0x3075,0x00},
	{0x3076,0x00},
	{0x3077,0xB6},
	{0x3078,0x05},
	{0x3079,0x07},
	{0x307a,0x03},
	{0x307b,0x8D},
	{0x307c,0x05},
	{0x307d,0x00},
	{0x307e,0x02},
	{0x307f,0xD0},
	{0x3080,0x05},//hts = 0x5be = 1470
	{0x3081,0xBE},//
	{0x3082,0x08},//vts = 0x8ae = 2222
	{0x3083,0xAE},//
	{0x3084,0x00},
	{0x3085,0x04},
	{0x3086,0x00},
	{0x3087,0x04},
	{0x346d,0x14},
	{0x3444,0x28},
	{0x3091,0x00},
	{0x3119,0x55},
	{0x3012,0x01},
	{SENSOR_REG_END, 0x00},/* END MARKER */
};

static struct regval_list sensor_init_regs_1280_960_30fps_mipi[] = {
	{0x328a,0x11},
	{0x31be,0x01},
	{0x3133,0xb7},
	{0x3134,0xca},
	{0x3135,0xcc},
	{0x313f,0x80},
	{0x3132,0x24},
	{0x31bf,0x80},
	{0x3000,0x03},
	{0x3001,0x62},
	{0x3002,0x07},
	{0x3004,0x03},
	{0x3005,0x62},
	{0x3006,0x07},
	{0x3007,0x01},
	{0x3014,0x03},
	{0x3023,0x05},
	{0x3032,0x35},
	{0x3033,0x04},
	{0x3054,0x00},
	{0x3055,0x0f},
	{0x3056,0x01},
	{0x3057,0xff},
	{0x3058,0xaf},
	{0x3059,0x44},
	{0x305a,0x02},
	{0x305b,0x00},
	{0x305c,0x30},
	{0x305d,0x9e},
	{0x305e,0x19},
	{0x305f,0x18},
	{0x3060,0xf9},
	{0x3061,0xf0},
	{0x308c,0x03},
	{0x308f,0x10},
	{0x3090,0x00},
	{0x3091,0x00},
	{0x30eb,0x00},
	{0x30a3,0x08},
	{0x30ad,0x03},
	{0x30ae,0x80},
	{0x30af,0x80},
	{0x30b0,0xff},
	{0x30b1,0x3f},
	{0x30b2,0x22},
	{0x30b9,0x22},
	{0x30bb,0x00},
	{0x30bc,0x00},
	{0x30bd,0x00},
	{0x30be,0x00},
	{0x30bf,0x00},
	{0x30c0,0x00},
	{0x30c1,0x00},
	{0x30c2,0x00},
	{0x30c3,0x00},
	{0x30c4,0x80},
	{0x30c5,0x00},
	{0x30c6,0x80},
	{0x30c7,0x00},
	{0x30c8,0x80},
	{0x3119,0x55},
	{0x311a,0x01},
	{0x311b,0x4a},
	{0x3074,0x00},
	{0x3075,0x00},
	{0x3076,0x00},
	{0x3077,0x02},
	{0x3078,0x05},
	{0x3079,0x07},
	{0x307a,0x04},
	{0x307b,0x41},
	{0x307c,0x05},
	{0x307d,0x00},
	{0x307e,0x04},
	{0x307f,0x38},
	{0x3080,0x05},
	{0x3081,0xbe},
	{0x3082,0x04},
	{0x3083,0x57},
	{0x3084,0x00},
	{0x3085,0x04},
	{0x3086,0x00},
	{0x3087,0x04},
	{0x3088,0x00},
	{0x3089,0x40},
	{0x308d,0x92},
	{0x3094,0xa5},
	{0x30e6,0x04},
	{0x30e7,0x48},
	{0x30e8,0x04},
	{0x30e9,0x48},
	{0x30ea,0x11},
	{0x30ec,0x01},
	{0x30fa,0x06},
	{0x3120,0x00},
	{0x3121,0x01},
	{0x3122,0x00},
	{0x3127,0x63},
	{0x3128,0xc0},
	{0x3129,0x00},
	{0x31be,0x01},
	{0x30a5,0x78},
	{0x30a6,0x40},
	{0x30a7,0x78},
	{0x30a8,0x80},
	{0x30a9,0x79},
	{0x30aa,0x00},
	{0x30ab,0x79},
	{0x30ac,0xf8},
	{0x3440,0x04},
	{0x3444,0x48},
	{0x344e,0x2c},
	{0x3457,0x33},
	{0x345e,0x38},
	{0x3461,0xa8},
	{0x3474,0x3f},
	{0x7002,0xaa},
	{0x7001,0xdf},
	{0x7048,0x00},
	{0x7049,0x02},
	{0x704a,0x02},
	{0x704b,0x00},
	{0x704c,0x01},
	{0x704d,0x00},
	{0x7043,0x04},
	{0x7040,0x3c},
	{0x7047,0x00},
	{0x7044,0x01},
	{0x7000,0x1f},
	{0x7084,0x01},
	{0x7085,0x03},
	{0x7086,0x02},
	{0x7087,0x40},
	{0x7088,0x01},
	{0x7089,0x20},
	{0x707f,0x04},
	{0x707c,0x3c},
	{0x7083,0x00},
	{0x7080,0x01},
	{0x7003,0xdf},
	{0x70c0,0x00},
	{0x70c1,0x02},
	{0x70c2,0x02},
	{0x70c3,0x00},
	{0x70c4,0x01},
	{0x70c5,0x00},
	{0x70b8,0x03},
	{0x70b9,0x98},
	{0x70bc,0x00},
	{0x70bd,0x80},
	{0x7004,0x02},
	{0x7005,0x00},
	{0x7006,0x01},
	{0x7007,0x80},
	{0x7008,0x02},
	{0x7009,0x00},
	{0x700a,0x04},
	{0x700b,0x00},
	{0x700e,0x00},
	{0x700f,0x60},
	{0x701a,0x02},
	{0x701b,0x00},
	{0x701c,0x01},
	{0x701d,0x80},
	{0x701e,0x02},
	{0x701f,0x00},
	{0x7020,0x04},
	{0x7021,0x00},
	{0x7024,0x00},
	{0x7025,0x60},
	{0x70e7,0x00},
	{0x70e4,0x10},
	{0x70e5,0x00},
	{0x70e6,0x00},
	{0x70eb,0x00},
	{0x70e8,0x10},
	{0x70e9,0x00},
	{0x70ea,0x00},
	{0x70ef,0x00},
	{0x70ec,0xfd},
	{0x70ed,0x00},
	{0x70ee,0x00},
	{0x70eb,0x00},
	{0x70f0,0xfd},
	{0x70f1,0x00},
	{0x70f2,0x00},
	{0x30fb,0x06},
	{0x30fc,0x80},
	{0x30fd,0x02},
	{0x30fe,0x93},
	{0x6000,0xc1},
	{0x6001,0xb9},
	{0x6002,0xba},
	{0x6003,0xa4},
	{0x6004,0xa4},
	{0x6005,0xb5},
	{0x6006,0xa0},
	{0x6007,0x82},
	{0x6008,0xa7},
	{0x6009,0xa7},
	{0x600a,0xb7},
	{0x600b,0x5c},
	{0x600c,0x9e},
	{0x600d,0xc0},
	{0x600e,0xd2},
	{0x600f,0x33},
	{0x6010,0xcc},
	{0x6011,0xe2},
	{0x6012,0xc1},
	{0x6013,0xab},
	{0x6014,0xab},
	{0x6015,0xb7},
	{0x6016,0x00},
	{0x6017,0x00},
	{0x6018,0x00},
	{0x6019,0x00},
	{0x601a,0x00},
	{0x601b,0x00},
	{0x601c,0x00},
	{0x601d,0x00},
	{0x601e,0x00},
	{0x601f,0x00},
	{0x6020,0x00},
	{0x6021,0x00},
	{0x6022,0x00},
	{0x6023,0x9c},
	{0x6024,0x94},
	{0x6025,0x90},
	{0x6026,0xc5},
	{0x6027,0x00},
	{0x6028,0x54},
	{0x6029,0x2a},
	{0x602a,0x61},
	{0x602b,0xd2},
	{0x602c,0xcc},
	{0x602d,0x04},
	{0x602e,0x35},
	{0x602f,0xb1},
	{0x6030,0xb2},
	{0x6031,0xb3},
	{0x6032,0xd2},
	{0x6033,0xd3},
	{0x6034,0x11},
	{0x6035,0x31},
	{0x6036,0xcc},
	{0x6037,0x06},
	{0x6038,0xd2},
	{0x6039,0x00},
	{0x603a,0xce},
	{0x603b,0x18},
	{0x603c,0xcf},
	{0x603d,0x1e},
	{0x603e,0xd0},
	{0x603f,0x24},
	{0x6040,0xc5},
	{0x6041,0xd2},
	{0x6042,0xbc},
	{0x6043,0xcc},
	{0x6044,0x52},
	{0x6045,0x2b},
	{0x6046,0xd2},
	{0x6047,0xd3},
	{0x6048,0x01},
	{0x6049,0xcc},
	{0x604a,0x0a},
	{0x604b,0xd2},
	{0x604c,0xd3},
	{0x604d,0x0f},
	{0x604e,0x1a},
	{0x604f,0x2a},
	{0x6050,0xd4},
	{0x6051,0xe3},
	{0x6052,0xba},
	{0x6053,0x56},
	{0x6054,0xd3},
	{0x6055,0x2e},
	{0x6056,0x54},
	{0x6057,0x26},
	{0x6058,0xd2},
	{0x6059,0xcc},
	{0x605a,0x60},
	{0x605b,0xd2},
	{0x605c,0xd3},
	{0x605d,0x27},
	{0x605e,0x27},
	{0x605f,0x08},
	{0x6060,0x1a},
	{0x6061,0xcc},
	{0x6062,0x88},
	{0x6063,0x00},
	{0x6064,0x12},
	{0x6065,0x2c},
	{0x6066,0x60},
	{0x6067,0xc2},
	{0x6068,0xb9},
	{0x6069,0xa5},
	{0x606a,0xa5},
	{0x606b,0xb5},
	{0x606c,0xa0},
	{0x606d,0x82},
	{0x606e,0x5c},
	{0x606f,0xd4},
	{0x6070,0xab},
	{0x6071,0xd4},
	{0x6072,0xab},
	{0x6073,0xd3},
	{0x6074,0x01},
	{0x6075,0x7c},
	{0x6076,0x74},
	{0x6077,0x00},
	{0x6078,0x61},
	{0x6079,0x2a},
	{0x607a,0xd2},
	{0x607b,0xcc},
	{0x607c,0xdf},
	{0x607d,0xc6},
	{0x607e,0x35},
	{0x607f,0xd2},
	{0x6080,0xcc},
	{0x6081,0x06},
	{0x6082,0x31},
	{0x6083,0xd2},
	{0x6084,0x00},
	{0x6085,0xbb},
	{0x6086,0xcc},
	{0x6087,0x18},
	{0x6088,0xc6},
	{0x6089,0xd2},
	{0x608a,0xbd},
	{0x608b,0xcc},
	{0x608c,0x52},
	{0x608d,0x2b},
	{0x608e,0xd2},
	{0x608f,0xd3},
	{0x6090,0x01},
	{0x6091,0xcc},
	{0x6092,0x0a},
	{0x6093,0xd2},
	{0x6094,0xd3},
	{0x6095,0x0f},
	{0x6096,0x1a},
	{0x6097,0x71},
	{0x6098,0x2a},
	{0x6099,0xd4},
	{0x609a,0xe3},
	{0x609b,0xd3},
	{0x609c,0x22},
	{0x609d,0x70},
	{0x609e,0xca},
	{0x609f,0x26},
	{0x60a0,0xd2},
	{0x60a1,0xcc},
	{0x60a2,0x60},
	{0x60a3,0xd2},
	{0x60a4,0xd3},
	{0x60a5,0x27},
	{0x60a6,0x27},
	{0x60a7,0x08},
	{0x60a8,0x1a},
	{0x60a9,0xcc},
	{0x60aa,0x88},
	{0x60ab,0x00},
	{0x60ac,0x12},
	{0x60ad,0x2c},
	{0x60ae,0x60},
	{0x60af,0x00},
	{0x60b0,0x00},
	{0x60b1,0xc0},
	{0x60b2,0xb9},
	{0x60b3,0xa3},
	{0x60b4,0xa3},
	{0x60b5,0xb5},
	{0x60b6,0x00},
	{0x60b7,0xa0},
	{0x60b8,0x82},
	{0x60b9,0x5c},
	{0x60ba,0xd4},
	{0x60bb,0x8b},
	{0x60bc,0x9d},
	{0x60bd,0xd3},
	{0x60be,0x21},
	{0x60bf,0xb0},
	{0x60c0,0xb0},
	{0x60c1,0xb7},
	{0x60c2,0x05},
	{0x60c3,0xd3},
	{0x60c4,0x0a},
	{0x60c5,0xd3},
	{0x60c6,0x10},
	{0x60c7,0x9c},
	{0x60c8,0x94},
	{0x60c9,0x90},
	{0x60ca,0xc8},
	{0x60cb,0xba},
	{0x60cc,0x7c},
	{0x60cd,0x74},
	{0x60ce,0x00},
	{0x60cf,0x61},
	{0x60d0,0x2a},
	{0x60d1,0x00},
	{0x60d2,0xd2},
	{0x60d3,0xcc},
	{0x60d4,0xdf},
	{0x60d5,0xc4},
	{0x60d6,0x35},
	{0x60d7,0xd3},
	{0x60d8,0x13},
	{0x60d9,0xd2},
	{0x60da,0xcc},
	{0x60db,0x06},
	{0x60dc,0x31},
	{0x60dd,0xd2},
	{0x60de,0xcc},
	{0x60df,0x15},
	{0x60e0,0xd2},
	{0x60e1,0xbb},
	{0x60e2,0xcc},
	{0x60e3,0x1a},
	{0x60e4,0xd2},
	{0x60e5,0xbe},
	{0x60e6,0xce},
	{0x60e7,0x52},
	{0x60e8,0xcf},
	{0x60e9,0x56},
	{0x60ea,0xd0},
	{0x60eb,0x5b},
	{0x60ec,0x2b},
	{0x60ed,0xd2},
	{0x60ee,0xd3},
	{0x60ef,0x01},
	{0x60f0,0xcc},
	{0x60f1,0x0a},
	{0x60f2,0xd2},
	{0x60f3,0xd3},
	{0x60f4,0x0f},
	{0x60f5,0xd9},
	{0x60f6,0xb4},
	{0x60f7,0xda},
	{0x60f8,0xbb},
	{0x60f9,0x1a},
	{0x60fa,0xd4},
	{0x60fb,0xe3},
	{0x60fc,0xd4},
	{0x60fd,0x96},
	{0x60fe,0x27},
	{0x60ff,0x00},
	{0x6100,0xd2},
	{0x6101,0xcc},
	{0x6102,0x60},
	{0x6103,0xd2},
	{0x6104,0xd3},
	{0x6105,0x2d},
	{0x6106,0xd9},
	{0x6107,0xcc},
	{0x6108,0xda},
	{0x6109,0xd2},
	{0x610a,0x1a},
	{0x610b,0x12},
	{0x610c,0xcc},
	{0x610d,0x88},
	{0x610e,0xd6},
	{0x610f,0x9e},
	{0x6110,0xb9},
	{0x6111,0xba},
	{0x6112,0xaf},
	{0x6113,0xdc},
	{0x6114,0x00},
	{0x6115,0xd5},
	{0x6116,0xba},
	{0x6117,0x00},
	{0x6118,0x00},
	{0x6119,0x00},
	{0x611a,0x00},
	{0x611b,0x00},
	{0x611c,0x00},
	{0x611d,0x00},
	{0x611e,0x00},
	{0x611f,0xaa},
	{0x6120,0xaa},
	{0x6121,0xb7},
	{0x6122,0x00},
	{0x6123,0x00},
	{0x6124,0x00},
	{0x6125,0x00},
	{0x6126,0x00},
	{0x6127,0xa6},
	{0x6128,0xa6},
	{0x6129,0xb7},
	{0x612a,0x00},
	{0x612b,0xd5},
	{0x612c,0x71},
	{0x612d,0xd3},
	{0x612e,0x30},
	{0x612f,0xba},
	{0x6130,0x00},
	{0x6131,0x00},
	{0x6132,0x00},
	{0x6133,0x00},
	{0x6134,0xd3},
	{0x6135,0x10},
	{0x6136,0x70},
	{0x6137,0x00},
	{0x6138,0x00},
	{0x6139,0x00},
	{0x613a,0x00},
	{0x613b,0xd5},
	{0x613c,0xba},
	{0x613d,0xb0},
	{0x613e,0xb0},
	{0x613f,0xb7},
	{0x6140,0x9d},
	{0x6141,0x02},
	{0x6142,0xd3},
	{0x6143,0x0a},
	{0x6144,0x9d},
	{0x6145,0x9d},
	{0x6146,0xd3},
	{0x6147,0x10},
	{0x6148,0x9c},
	{0x6149,0x94},
	{0x614a,0x90},
	{0x614b,0xc8},
	{0x614c,0xba},
	{0x614d,0xd2},
	{0x614e,0x60},
	{0x614f,0x2c},
	{0x6150,0x50},
	{0x6151,0x11},
	{0x6152,0xcc},
	{0x6153,0x00},
	{0x6154,0x30},
	{0x6155,0xd5},
	{0x6156,0xba},
	{0x6157,0xb0},
	{0x6158,0xb0},
	{0x6159,0xb7},
	{0x615a,0x9d},
	{0x615b,0x02},
	{0x615c,0xd3},
	{0x615d,0x0a},
	{0x615e,0x9d},
	{0x615f,0x9d},
	{0x6160,0xd3},
	{0x6161,0x10},
	{0x6162,0x9c},
	{0x6163,0x94},
	{0x6164,0x90},
	{0x6165,0xc8},
	{0x6166,0xba},
	{0x6167,0xd5},
	{0x6168,0x01},
	{0x6169,0x1a},
	{0x616a,0xcc},
	{0x616b,0x12},
	{0x616c,0x12},
	{0x616d,0x00},
	{0x616e,0xcc},
	{0x616f,0x9c},
	{0x6170,0xd2},
	{0x6171,0xcc},
	{0x6172,0x60},
	{0x6173,0xd2},
	{0x6174,0x04},
	{0x6175,0xd5},
	{0x6176,0x1a},
	{0x6177,0xcc},
	{0x6178,0x12},
	{0x6179,0x00},
	{0x617a,0x12},
	{0x617b,0xcc},
	{0x617c,0x9c},
	{0x617d,0xd2},
	{0x617e,0xcc},
	{0x617f,0x60},
	{0x6180,0xd2},
	{0x6181,0x1a},
	{0x6182,0xcc},
	{0x6183,0x12},
	{0x6184,0x00},
	{0x6185,0x12},
	{0x6186,0xcc},
	{0x6187,0x9c},
	{0x6188,0xd2},
	{0x6189,0xcc},
	{0x618a,0x60},
	{0x618b,0xd2},
	{0x618c,0x1a},
	{0x618d,0xcc},
	{0x618e,0x12},
	{0x618f,0x00},
	{0x6190,0x12},
	{0x6191,0xcc},
	{0x6192,0x9c},
	{0x6193,0xd2},
	{0x6194,0xcc},
	{0x6195,0x60},
	{0x6196,0xd2},
	{0x6197,0xd5},
	{0x6198,0x1a},
	{0x6199,0xcc},
	{0x619a,0x12},
	{0x619b,0x12},
	{0x619c,0x00},
	{0x619d,0xcc},
	{0x619e,0x8a},
	{0x619f,0xd2},
	{0x61a0,0xcc},
	{0x61a1,0x74},
	{0x61a2,0xd2},
	{0x61a3,0xd5},
	{0x61a4,0x1a},
	{0x61a5,0xcc},
	{0x61a6,0x12},
	{0x61a7,0x00},
	{0x61a8,0x12},
	{0x61a9,0xcc},
	{0x61aa,0x8a},
	{0x61ab,0xd2},
	{0x61ac,0xcc},
	{0x61ad,0x74},
	{0x61ae,0xd2},
	{0x61af,0x1a},
	{0x61b0,0xcc},
	{0x61b1,0x12},
	{0x61b2,0x00},
	{0x61b3,0x12},
	{0x61b4,0xcc},
	{0x61b5,0x8a},
	{0x61b6,0xd2},
	{0x61b7,0xcc},
	{0x61b8,0x74},
	{0x61b9,0xd2},
	{0x61ba,0x1a},
	{0x61bb,0xcc},
	{0x61bc,0x12},
	{0x61bd,0x00},
	{0x61be,0x12},
	{0x61bf,0xcc},
	{0x61c0,0x8a},
	{0x61c1,0xd2},
	{0x61c2,0xcc},
	{0x61c3,0x74},
	{0x61c4,0xd2},
	{0x61c5,0xd5},
	{0x61c6,0xcc},
	{0x61c7,0x12},
	{0x61c8,0x00},
	{0x61c9,0x12},
	{0x61ca,0xcc},
	{0x61cb,0x9c},
	{0x61cc,0xd5},
	{0x6400,0x04},
	{0x6401,0x04},
	{0x6402,0x00},
	{0x6403,0xff},
	{0x6404,0x00},
	{0x6405,0x08},
	{0x6406,0x00},
	{0x6407,0xff},
	{0x6408,0x04},
	{0x6409,0x70},
	{0x640a,0x00},
	{0x640b,0xff},
	{0x640c,0x05},
	{0x640d,0x14},
	{0x640e,0x04},
	{0x640f,0x71},
	{0x6410,0x05},
	{0x6411,0x74},
	{0x6412,0x00},
	{0x6413,0xff},
	{0x6414,0x05},
	{0x6415,0x54},
	{0x6416,0x05},
	{0x6417,0x44},
	{0x6418,0x04},
	{0x6419,0x30},
	{0x641a,0x05},
	{0x641b,0x46},
	{0x641c,0x00},
	{0x641d,0xff},
	{0x641e,0x04},
	{0x641f,0x31},
	{0x6420,0x04},
	{0x6421,0x30},
	{0x6422,0x00},
	{0x6423,0xff},
	{0x6424,0x04},
	{0x6425,0x20},
	{0x6426,0x05},
	{0x6427,0x06},
	{0x6428,0x00},
	{0x6429,0xff},
	{0x642a,0x08},
	{0x642b,0x2a},
	{0x642c,0x08},
	{0x642d,0x31},
	{0x642e,0x00},
	{0x642f,0xff},
	{0x6430,0x08},
	{0x6431,0x2a},
	{0x6432,0x08},
	{0x6433,0x31},
	{0x6434,0x06},
	{0x6435,0x20},
	{0x6436,0x07},
	{0x6437,0x00},
	{0x6438,0x08},
	{0x6439,0x40},
	{0x643a,0x00},
	{0x643b,0xff},
	{0x643c,0x08},
	{0x643d,0x2a},
	{0x643e,0x08},
	{0x643f,0x36},
	{0x6440,0x06},
	{0x6441,0x10},
	{0x6442,0x07},
	{0x6443,0x00},
	{0x6444,0x08},
	{0x6445,0x40},
	{0x6446,0x00},
	{0x6447,0xff},
	{0x6448,0x08},
	{0x6449,0x2a},
	{0x644a,0x08},
	{0x644b,0x3b},
	{0x644c,0x06},
	{0x644d,0x00},
	{0x644e,0x07},
	{0x644f,0x00},
	{0x6450,0x08},
	{0x6451,0x40},
	{0x6452,0x00},
	{0x6453,0xff},
	{0x6454,0x06},
	{0x6455,0x00},
	{0x6456,0x07},
	{0x6457,0x05},
	{0x6458,0x01},
	{0x6459,0xaf},
	{0x645a,0x01},
	{0x645b,0x0f},
	{0x645c,0x01},
	{0x645d,0x90},
	{0x645e,0x01},
	{0x645f,0xc8},
	{0x6460,0x00},
	{0x6461,0xff},
	{0x6462,0x01},
	{0x6463,0xac},
	{0x6464,0x01},
	{0x6465,0x0c},
	{0x6466,0x01},
	{0x6467,0x90},
	{0x6468,0x01},
	{0x6469,0xe8},
	{0x646a,0x00},
	{0x646b,0xff},
	{0x646c,0x01},
	{0x646d,0xad},
	{0x646e,0x01},
	{0x646f,0x0d},
	{0x6470,0x01},
	{0x6471,0x90},
	{0x6472,0x01},
	{0x6473,0xe8},
	{0x6474,0x00},
	{0x6475,0xff},
	{0x6476,0x01},
	{0x6477,0xae},
	{0x6478,0x01},
	{0x6479,0x0e},
	{0x647a,0x01},
	{0x647b,0x90},
	{0x647c,0x01},
	{0x647d,0xe8},
	{0x647e,0x00},
	{0x647f,0xff},
	{0x6480,0x01},
	{0x6481,0xb0},
	{0x6482,0x01},
	{0x6483,0xb1},
	{0x6484,0x01},
	{0x6485,0xb2},
	{0x6486,0x01},
	{0x6487,0xb3},
	{0x6488,0x01},
	{0x6489,0xb4},
	{0x648a,0x01},
	{0x648b,0xb5},
	{0x648c,0x01},
	{0x648d,0xb6},
	{0x648e,0x01},
	{0x648f,0xb7},
	{0x6490,0x01},
	{0x6491,0xb8},
	{0x6492,0x01},
	{0x6493,0xb9},
	{0x6494,0x01},
	{0x6495,0xba},
	{0x6496,0x01},
	{0x6497,0xbb},
	{0x6498,0x01},
	{0x6499,0xbc},
	{0x649a,0x01},
	{0x649b,0xbd},
	{0x649c,0x01},
	{0x649d,0xbe},
	{0x649e,0x01},
	{0x649f,0xbf},
	{0x64a0,0x01},
	{0x64a1,0xc0},
	{0x64a2,0x00},
	{0x64a3,0xff},
	{0x64a4,0x06},
	{0x64a5,0x00},
	{0x64a6,0x01},
	{0x64a7,0xf6},
	{0x64a8,0x04},
	{0x64a9,0x30},
	{0x64aa,0x00},
	{0x64ab,0xff},
	{0x64ac,0x06},
	{0x64ad,0x10},
	{0x64ae,0x01},
	{0x64af,0xf6},
	{0x64b0,0x04},
	{0x64b1,0x30},
	{0x64b2,0x06},
	{0x64b3,0x00},
	{0x64b4,0x00},
	{0x64b5,0xff},
	{0x64b6,0x06},
	{0x64b7,0x20},
	{0x64b8,0x01},
	{0x64b9,0xf6},
	{0x64ba,0x04},
	{0x64bb,0x30},
	{0x64bc,0x06},
	{0x64bd,0x00},
	{0x64be,0x00},
	{0x64bf,0xff},
	{0x64c0,0x04},
	{0x64c1,0x31},
	{0x64c2,0x04},
	{0x64c3,0x30},
	{0x64c4,0x01},
	{0x64c5,0x20},
	{0x64c6,0x01},
	{0x64c7,0x31},
	{0x64c8,0x01},
	{0x64c9,0x32},
	{0x64ca,0x01},
	{0x64cb,0x33},
	{0x64cc,0x01},
	{0x64cd,0x34},
	{0x64ce,0x01},
	{0x64cf,0x35},
	{0x64d0,0x01},
	{0x64d1,0x36},
	{0x64d2,0x01},
	{0x64d3,0x37},
	{0x64d4,0x01},
	{0x64d5,0x38},
	{0x64d6,0x01},
	{0x64d7,0x39},
	{0x64d8,0x01},
	{0x64d9,0x3a},
	{0x64da,0x01},
	{0x64db,0x3b},
	{0x64dc,0x01},
	{0x64dd,0x3c},
	{0x64de,0x01},
	{0x64df,0x3d},
	{0x64e0,0x01},
	{0x64e1,0x3e},
	{0x64e2,0x01},
	{0x64e3,0x3f},
	{0x64e4,0x02},
	{0x64e5,0xa0},
	{0x64e6,0x00},
	{0x64e7,0xff},
	{0x64e8,0x04},
	{0x64e9,0x31},
	{0x64ea,0x04},
	{0x64eb,0x30},
	{0x64ec,0x01},
	{0x64ed,0x00},
	{0x64ee,0x01},
	{0x64ef,0x11},
	{0x64f0,0x01},
	{0x64f1,0x12},
	{0x64f2,0x01},
	{0x64f3,0x13},
	{0x64f4,0x01},
	{0x64f5,0x14},
	{0x64f6,0x01},
	{0x64f7,0x15},
	{0x64f8,0x01},
	{0x64f9,0x16},
	{0x64fa,0x01},
	{0x64fb,0x17},
	{0x64fc,0x01},
	{0x64fd,0x18},
	{0x64fe,0x01},
	{0x64ff,0x19},
	{0x6500,0x01},
	{0x6501,0x1a},
	{0x6502,0x01},
	{0x6503,0x1b},
	{0x6504,0x01},
	{0x6505,0x1c},
	{0x6506,0x01},
	{0x6507,0x1d},
	{0x6508,0x01},
	{0x6509,0x1e},
	{0x650a,0x01},
	{0x650b,0x1f},
	{0x650c,0x02},
	{0x650d,0xa0},
	{0x650e,0x00},
	{0x650f,0xff},
	{0x6510,0x04},
	{0x6511,0x20},
	{0x6512,0x05},
	{0x6513,0x86},
	{0x6514,0x03},
	{0x6515,0x0b},
	{0x6516,0x05},
	{0x6517,0x86},
	{0x6518,0x00},
	{0x6519,0x00},
	{0x651a,0x05},
	{0x651b,0x06},
	{0x651c,0x00},
	{0x651d,0x04},
	{0x651e,0x05},
	{0x651f,0x04},
	{0x6520,0x00},
	{0x6521,0x04},
	{0x6522,0x05},
	{0x6523,0x00},
	{0x6524,0x05},
	{0x6525,0x0a},
	{0x6526,0x03},
	{0x6527,0x9a},
	{0x6528,0x05},
	{0x6529,0x86},
	{0x652a,0x00},
	{0x652b,0x00},
	{0x652c,0x05},
	{0x652d,0x06},
	{0x652e,0x00},
	{0x652f,0x01},
	{0x6530,0x05},
	{0x6531,0x04},
	{0x6532,0x00},
	{0x6533,0x04},
	{0x6534,0x05},
	{0x6535,0x00},
	{0x6536,0x05},
	{0x6537,0x0a},
	{0x6538,0x03},
	{0x6539,0x99},
	{0x653a,0x05},
	{0x653b,0x06},
	{0x653c,0x00},
	{0x653d,0x00},
	{0x653e,0x05},
	{0x653f,0x04},
	{0x6540,0x00},
	{0x6541,0x04},
	{0x6542,0x05},
	{0x6543,0x00},
	{0x6544,0x05},
	{0x6545,0x0a},
	{0x6546,0x03},
	{0x6547,0x98},
	{0x6548,0x05},
	{0x6549,0x06},
	{0x654a,0x00},
	{0x654b,0x00},
	{0x654c,0x05},
	{0x654d,0x04},
	{0x654e,0x00},
	{0x654f,0x04},
	{0x6550,0x05},
	{0x6551,0x00},
	{0x6552,0x05},
	{0x6553,0x0a},
	{0x6554,0x03},
	{0x6555,0x97},
	{0x6556,0x05},
	{0x6557,0x06},
	{0x6558,0x05},
	{0x6559,0x04},
	{0x655a,0x00},
	{0x655b,0x04},
	{0x655c,0x05},
	{0x655d,0x00},
	{0x655e,0x05},
	{0x655f,0x0a},
	{0x6560,0x03},
	{0x6561,0x96},
	{0x6562,0x05},
	{0x6563,0x06},
	{0x6564,0x05},
	{0x6565,0x04},
	{0x6566,0x00},
	{0x6567,0x04},
	{0x6568,0x05},
	{0x6569,0x00},
	{0x656a,0x05},
	{0x656b,0x0a},
	{0x656c,0x03},
	{0x656d,0x95},
	{0x656e,0x05},
	{0x656f,0x06},
	{0x6570,0x05},
	{0x6571,0x04},
	{0x6572,0x00},
	{0x6573,0x04},
	{0x6574,0x05},
	{0x6575,0x00},
	{0x6576,0x05},
	{0x6577,0x0a},
	{0x6578,0x03},
	{0x6579,0x94},
	{0x657a,0x05},
	{0x657b,0x06},
	{0x657c,0x00},
	{0x657d,0x00},
	{0x657e,0x05},
	{0x657f,0x04},
	{0x6580,0x00},
	{0x6581,0x04},
	{0x6582,0x05},
	{0x6583,0x00},
	{0x6584,0x05},
	{0x6585,0x0a},
	{0x6586,0x03},
	{0x6587,0x93},
	{0x6588,0x05},
	{0x6589,0x06},
	{0x658a,0x00},
	{0x658b,0x00},
	{0x658c,0x05},
	{0x658d,0x04},
	{0x658e,0x00},
	{0x658f,0x04},
	{0x6590,0x05},
	{0x6591,0x00},
	{0x6592,0x05},
	{0x6593,0x0a},
	{0x6594,0x03},
	{0x6595,0x92},
	{0x6596,0x05},
	{0x6597,0x06},
	{0x6598,0x05},
	{0x6599,0x04},
	{0x659a,0x00},
	{0x659b,0x04},
	{0x659c,0x05},
	{0x659d,0x00},
	{0x659e,0x05},
	{0x659f,0x0a},
	{0x65a0,0x03},
	{0x65a1,0x91},
	{0x65a2,0x05},
	{0x65a3,0x06},
	{0x65a4,0x05},
	{0x65a5,0x04},
	{0x65a6,0x00},
	{0x65a7,0x04},
	{0x65a8,0x05},
	{0x65a9,0x00},
	{0x65aa,0x05},
	{0x65ab,0x0a},
	{0x65ac,0x03},
	{0x65ad,0x90},
	{0x65ae,0x05},
	{0x65af,0x06},
	{0x65b0,0x05},
	{0x65b1,0x04},
	{0x65b2,0x00},
	{0x65b3,0x04},
	{0x65b4,0x05},
	{0x65b5,0x00},
	{0x65b6,0x05},
	{0x65b7,0x0a},
	{0x65b8,0x02},
	{0x65b9,0x90},
	{0x65ba,0x05},
	{0x65bb,0x06},
	{0x65bc,0x00},
	{0x65bd,0xff},
	{0x65be,0x04},
	{0x65bf,0x70},
	{0x65c0,0x08},
	{0x65c1,0x76},
	{0x65c2,0x00},
	{0x65c3,0xff},
	{0x65c4,0x08},
	{0x65c5,0x76},
	{0x65c6,0x04},
	{0x65c7,0x0c},
	{0x65c8,0x05},
	{0x65c9,0x07},
	{0x65ca,0x04},
	{0x65cb,0x04},
	{0x65cc,0x00},
	{0x65cd,0xff},
	{0x65ce,0x00},
	{0x65cf,0xff},
	{0x65d0,0x00},
	{0x65d1,0xff},
	{0x30eb,0x04},
	{0x30ed,0x5a},
	{0x30ee,0x01},
	{0x30ef,0x80},
	{0x30f1,0x5a},
	{0x303a,0x04},
	{0x303b,0x7f},
	{0x303c,0xfe},
	{0x303d,0x19},
	{0x303e,0xd7},
	{0x303f,0x09},
	{0x3040,0x78},
	{0x3042,0x05},
	{0x328a,0x00},
	{0x3012,0x01},
	{0x3012,0x00},
	{0x3000,0x03},
	{0x3001,0x62},
	{0x3002,0x07},
	{0x3004,0x03},
	{0x3005,0x62},
	{0x3006,0x07},
	{0x308f,0x10},
	{0x3127,0x63},
	{0x3074,0x00},
	{0x3075,0x00},
	{0x3076,0x00},
	{0x3077,0x3E},
	{0x3078,0x05},
	{0x3079,0x07},
	{0x307a,0x04},
	{0x307b,0x05},
	{0x307c,0x05},
	{0x307d,0x00},
	{0x307e,0x03},
	{0x307f,0xC0},
	{0x3080,0x05},
	{0x3081,0xBE},
	{0x3082,0x08},
	{0x3083,0xAE},
	{0x3084,0x00},
	{0x3085,0x04},
	{0x3086,0x00},
	{0x3087,0x04},
	{0x346d,0x14},
	{0x3444,0x28},
	{0x3091,0x00},
	{0x3119,0x55},
	{0x3012,0x01},
	{SENSOR_REG_END, 0x00},/* END MARKER */
};

static struct tx_isp_sensor_win_setting sensor_win_sizes[] = {
	{
		.width = 1280,
		.height = 720,
		.fps = 30 << 16 | 1,
		.mbus_code = TISP_VI_FMT_SBGGR12_1X12,
		.colorspace = TISP_COLORSPACE_SRGB,
		.regs = sensor_init_regs_1280_720_30fps_mipi,
	},
	{
		.width = 1280,
		.height = 960,
		.fps = 30 << 16 | 1,
		.mbus_code = TISP_VI_FMT_SBGGR12_1X12,
		.colorspace = TISP_COLORSPACE_SRGB,
		.regs = sensor_init_regs_1280_960_30fps_mipi,
	},
	{
		.width = 1280,
		.height = 736,
		.fps = 30 << 16 | 1,
		.mbus_code = TISP_VI_FMT_SBGGR12_1X12,
		.colorspace = TISP_COLORSPACE_SRGB,
		.regs = sensor_init_regs_1280_960_30fps_mipi,
	},
};
struct tx_isp_sensor_win_setting *wsize = &sensor_win_sizes[0];

static struct regval_list sensor_stream_on_mipi[] = {
	{0x3012,0x01},
	{SENSOR_REG_END, 0x00},
};

static struct regval_list sensor_stream_off_mipi[] = {
	{0x3012,0x00},
	{SENSOR_REG_END, 0x00},
};

int sensor_read(struct tx_isp_subdev *sd, uint16_t reg,
		unsigned char *value)
{
	struct i2c_client *client = tx_isp_get_subdevdata(sd);
	uint8_t buf[2] = {(reg >> 8) & 0xff, reg & 0xff};
	struct i2c_msg msg[2] = {
		[0] = {
			.addr = client->addr,
			.flags = 0,
			.len = 2,
			.buf = buf,
		},
		[1] = {
			.addr = client->addr,
			.flags = I2C_M_RD,
			.len = 1,
			.buf = value,
		}
	};
	int ret;
	ret = private_i2c_transfer(client->adapter, msg, 2);
	if (ret > 0)
		ret = 0;

	return ret;
}

int sensor_write(struct tx_isp_subdev *sd, uint16_t reg,
		 unsigned char value)
{
	struct i2c_client *client = tx_isp_get_subdevdata(sd);
	uint8_t buf[3] = {(reg >> 8) & 0xff, reg & 0xff, value};
	struct i2c_msg msg = {
		.addr = client->addr,
		.flags = 0,
		.len = 3,
		.buf = buf,
	};
	int ret;
	ret = private_i2c_transfer(client->adapter, &msg, 1);
	if (ret > 0)
		ret = 0;

	return ret;
}

#if 0
static int sensor_read_array(struct tx_isp_subdev *sd, struct regval_list *vals)
{
	int ret;
	unsigned char val;
	while (vals->reg_num != SENSOR_REG_END) {
		if (vals->reg_num == SENSOR_REG_DELAY) {
			msleep(vals->value);
		} else {
			ret = sensor_read(sd, vals->reg_num, &val);
			if (ret < 0)
				return ret;
		}
		vals++;
	}

	return 0;
}
#endif

static int sensor_write_array(struct tx_isp_subdev *sd, struct regval_list *vals)
{
	int ret;

	while (vals->reg_num != SENSOR_REG_END) {
		if (vals->reg_num == SENSOR_REG_DELAY) {
			msleep(vals->value);
		} else {
			ret = sensor_write(sd, vals->reg_num, vals->value);
			if (ret < 0)
				return ret;
		}
		vals++;
	}

	return 0;
}

static int sensor_reset(struct tx_isp_subdev *sd, struct tx_isp_initarg *init)
{
	return 0;
}

static int sensor_detect(struct tx_isp_subdev *sd, unsigned int *ident)
{
	int ret;
	unsigned char v;

	ret = sensor_read(sd, 0x300a, &v);
	ISP_WARNING("-----%s: %d ret = %d, v = 0x%02x\n", __func__, __LINE__, ret,v);
	if (ret < 0)
		return ret;
	if (v != SENSOR_CHIP_ID_H)
		return -ENODEV;
	*ident = v;

	ret = sensor_read(sd, 0x300b, &v);
	ISP_WARNING("-----%s: %d ret = %d, v = 0x%02x\n", __func__, __LINE__, ret,v);
	if (ret < 0)
		return ret;
	if (v != SENSOR_CHIP_ID_L)
		return -ENODEV;
	*ident = (*ident << 8) | v;

	return 0;
}

static int sensor_set_expo(struct tx_isp_subdev *sd, int value)
{
	int ret = -1;
	int it = value & 0xffff;
	int again = (value & 0xffff0000) >> 16;

	ret += sensor_write(sd, 0x30e6, (unsigned char)((it >> 8) & 0xff));
	ret += sensor_write(sd, 0x30e7, (unsigned char)(it & 0xff));

	ret += sensor_write(sd, 0x30eb, (unsigned char)((again >> 12) & 0x0f));
	ret += sensor_write(sd, 0x30ec, (unsigned char)((again >> 8) & 0x0f));
	ret += sensor_write(sd, 0x30ed, (unsigned char)(again & 0xff));

	return 0;
}

#if 0
static int sensor_set_integration_time(struct tx_isp_subdev *sd, int value)
{
	int ret = 0;
	unsigned int expo = value;

	ret += sensor_write(sd, 0x30e7, (unsigned char)(expo & 0xff));
	ret += sensor_write(sd, 0x30e6, (unsigned char)((expo >> 8) & 0xff));
	if (ret < 0)
		return ret;

	return 0;
}

static int sensor_set_analog_gain(struct tx_isp_subdev *sd, int value)
{
	int ret = 0;

	ret += sensor_write(sd, 0x30eb, (unsigned char)((value >> 12) & 0x0f));
	ret += sensor_write(sd, 0x30ec, (unsigned char)((value >> 8) & 0x0f));
	ret += sensor_write(sd, 0x30ed, (unsigned char)(value & 0xff));
	if (ret < 0)
		return ret;

	return 0;
}
#endif

static int sensor_set_digital_gain(struct tx_isp_subdev *sd, int value)
{
	return 0;
}

static int sensor_get_black_pedestal(struct tx_isp_subdev *sd, int value)
{
	return 0;
}

static int sensor_set_attr(struct tx_isp_subdev *sd, struct tx_isp_sensor_win_setting *wise)
{
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);

	sensor->video.vi_max_width = wsize->width;
	sensor->video.vi_max_height = wsize->height;
	sensor->video.mbus.width = wsize->width;
	sensor->video.mbus.height = wsize->height;
	sensor->video.mbus.code = wsize->mbus_code;
	sensor->video.mbus.field = TISP_FIELD_NONE;
	sensor->video.mbus.colorspace = wsize->colorspace;
	sensor->video.fps = wsize->fps;
	sensor->video.max_fps = wsize->fps;
	sensor->video.min_fps = SENSOR_OUTPUT_MIN_FPS << 16 | 1;

	return 0;
}

static int sensor_init(struct tx_isp_subdev *sd, struct tx_isp_initarg *init)
{
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);
	int ret = 0;

	if (!init->enable)
		return ISP_SUCCESS;

	sensor_set_attr(sd, wsize);
	sensor->video.state = TX_ISP_MODULE_INIT;
	ret = tx_isp_call_subdev_notify(sd, TX_ISP_EVENT_SYNC_SENSOR_ATTR, &sensor->video);
	sensor->priv = wsize;

	return 0;
}

static int sensor_s_stream(struct tx_isp_subdev *sd, struct tx_isp_initarg *init)
{
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);
	int ret = 0;

	if (init->enable) {
		if (sensor->video.state == TX_ISP_MODULE_INIT) {
			ret = sensor_write_array(sd, wsize->regs);
			if (ret)
				return ret;
			sensor->video.state = TX_ISP_MODULE_RUNNING;
		}
		if (sensor->video.state == TX_ISP_MODULE_RUNNING) {

			ret = sensor_write_array(sd, sensor_stream_on_mipi);
			ISP_WARNING("%s stream on\n", SENSOR_NAME);
		}
	}
	else {
		ret = sensor_write_array(sd, sensor_stream_off_mipi);
		ISP_WARNING("%s stream off\n", SENSOR_NAME);
	}

	return ret;
}

static int sensor_set_fps(struct tx_isp_subdev *sd, int fps)
{
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);
	unsigned int sclk = 0;
	unsigned int hts = 0;
	unsigned int vts = 0;
	unsigned int max_fps;
	unsigned char val = 0;
	unsigned int newformat = 0; //the format is 24.8
	int ret = 0;

	switch(sensor->info.default_boot) {
	case 0:
		sclk = 1470 * 2222 * 30;
		max_fps = TX_SENSOR_MAX_FPS_30;
		break;
	case 1:
		sclk = 1470 * 2222 * 30;
		max_fps = TX_SENSOR_MAX_FPS_30;
		break;
	case 2:
		sclk = 1470 * 2222 * 30;
		max_fps = TX_SENSOR_MAX_FPS_30;
		break;
	default:
		ISP_ERROR("Now we do not support this framerate!!!\n");
	}

	newformat = (((fps >> 16) / (fps & 0xffff)) << 8) + ((((fps >> 16) % (fps & 0xffff)) << 8) / (fps & 0xffff));
	if (newformat > (max_fps<< 8) || newformat < (SENSOR_OUTPUT_MIN_FPS << 8)) {
		ISP_ERROR("warn: fps(%x) not in range\n", fps);
		return -1;
	}

	ret += sensor_read(sd, 0x3080, &val);
	hts = val << 8;
	ret += sensor_read(sd, 0x3081, &val);
	hts = (hts | val);

	if (0 != ret) {
		ISP_ERROR("Error: %s read error\n", SENSOR_NAME);
		return -1;
	}

	vts = sclk * (fps & 0xffff) / hts / ((fps & 0xffff0000) >> 16);

	ret += sensor_write(sd, 0x3082, (unsigned char)(vts >> 8));
	ret += sensor_write(sd, 0x3083, (unsigned char)(vts & 0xff));
	if (0 != ret) {
		ISP_ERROR("Error: %s write error\n", SENSOR_NAME);
		return ret;
	}
	sensor->video.fps = fps;
	sensor->video.attr->max_integration_time_native = vts - 8;
	sensor->video.attr->integration_time_limit = vts - 8;
	sensor->video.attr->total_height = vts;
	sensor->video.attr->max_integration_time = vts - 8;
	ret = tx_isp_call_subdev_notify(sd, TX_ISP_EVENT_SYNC_SENSOR_ATTR, &sensor->video);

	return ret;
}

static int sensor_set_mode(struct tx_isp_subdev *sd, int value)
{
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);
	int ret = ISP_SUCCESS;

	if (wsize) {
		sensor_set_attr(sd, wsize);
		ret = tx_isp_call_subdev_notify(sd, TX_ISP_EVENT_SYNC_SENSOR_ATTR, &sensor->video);
	}

	return ret;
}

#if 1
static int sensor_set_hvflip(struct tx_isp_subdev *sd, int enable)
{
	int ret = 0;
	uint8_t val = 0;
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);

	/* 2'b01:mirror,2'b10:filp */
	sensor_read(sd, 0x3090, &val);

	switch(enable) {
	case 0:
                val &= 0xF3;
		sensor->video.mbus.code = TISP_VI_FMT_SBGGR12_1X12;
		break;
	case 1:
                val = ((val & 0xF7) | 0x04);
		sensor->video.mbus.code = TISP_VI_FMT_SGBRG12_1X12;
		break;
	case 2:
                val = ((val & 0xFB) | 0x08);
		sensor->video.mbus.code = TISP_VI_FMT_SGRBG12_1X12;
		break;
	case 3:
                val |= 0x0C;
		sensor->video.mbus.code = TISP_VI_FMT_SRGGB12_1X12;
		break;
	}
	sensor->video.mbus_change = 1;
	sensor_write(sd, 0x3090, val);

	if (!ret)
		ret = tx_isp_call_subdev_notify(sd, TX_ISP_EVENT_SYNC_SENSOR_ATTR, &sensor->video);

	return ret;
}
#endif

static int sensor_attr_check(struct tx_isp_subdev *sd)
{
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);
	struct tx_isp_sensor_register_info *info = &sensor->info;
	struct i2c_client *client = tx_isp_get_subdevdata(sd);
	struct clk *sclka;
	unsigned long rate;
	int ret;

	switch(info->default_boot) {
	case 0:
		wsize = &sensor_win_sizes[0];
		memcpy(&(sensor_attr.mipi), &sensor_mipi, sizeof(sensor_mipi));
		sensor_attr.data_type = TX_SENSOR_DATA_TYPE_LINEAR;
		sensor_attr.dbus_type = TX_SENSOR_DATA_INTERFACE_MIPI;
		sensor_attr.max_integration_time_native = 2222 - 8;
		sensor_attr.integration_time_limit = 2222 - 8;
		sensor_attr.total_width = 1470;
		sensor_attr.total_height = 2222;
		sensor_attr.max_integration_time = 2222 - 8;
		sensor_attr.integration_time = 0x448;
		break;
	case 1:
		wsize = &sensor_win_sizes[1];
		memcpy(&(sensor_attr.mipi), &sensor_mipi_960, sizeof(sensor_mipi_960));
		sensor_attr.data_type = TX_SENSOR_DATA_TYPE_LINEAR;
		sensor_attr.dbus_type = TX_SENSOR_DATA_INTERFACE_MIPI;
		sensor_attr.max_integration_time_native = 2222 - 8;
		sensor_attr.integration_time_limit = 2222 - 8;
		sensor_attr.total_width = 1470;
		sensor_attr.total_height = 2222;
		sensor_attr.max_integration_time = 2222 - 8;
		sensor_attr.integration_time = 0x448;
		break;
	case 2:
		wsize = &sensor_win_sizes[2];
		memcpy(&(sensor_attr.mipi), &sensor_mipi_960, sizeof(sensor_mipi_960));
		sensor_attr.data_type = TX_SENSOR_DATA_TYPE_LINEAR;
		sensor_attr.dbus_type = TX_SENSOR_DATA_INTERFACE_MIPI;
		sensor_attr.max_integration_time_native = 2222 - 8;
		sensor_attr.integration_time_limit = 2222 - 8;
		sensor_attr.total_width = 1470;
		sensor_attr.total_height = 2222;
		sensor_attr.max_integration_time = 2222 - 8;
		sensor_attr.integration_time = 0x448;
		break;
	default:
		ISP_ERROR("Have no this Setting Source!!!\n");
	}

	switch(info->video_interface) {
	case TISP_SENSOR_VI_MIPI_CSI0:
		sensor_attr.dbus_type = TX_SENSOR_DATA_INTERFACE_MIPI;
		sensor_attr.mipi.index = 0;
		break;
	case TISP_SENSOR_VI_DVP:
		sensor_attr.dbus_type = TX_SENSOR_DATA_INTERFACE_DVP;
		break;
	default:
		ISP_ERROR("Have no this Interface Source!!!\n");
	}

	switch(info->mclk) {
	case TISP_SENSOR_MCLK0:
	case TISP_SENSOR_MCLK1:
	case TISP_SENSOR_MCLK2:
                sclka = private_devm_clk_get(&client->dev, SEN_MCLK);
                sensor->mclk = private_devm_clk_get(sensor->dev, SEN_BCLK);
		set_sensor_mclk_function(0);
		break;
	default:
		ISP_ERROR("Have no this MCLK Source!!!\n");
	}

	rate = private_clk_get_rate(sensor->mclk);
	switch(info->default_boot) {
	case 0:
	case 1:
	case 2:
                if (((rate / 1000) % 24000) != 0) {
                        ret = clk_set_parent(sclka, clk_get(NULL, SEN_TCLK));
                        sclka = private_devm_clk_get(&client->dev, SEN_TCLK);
                        if (IS_ERR(sclka)) {
                                pr_err("get sclka failed\n");
                        } else {
                                rate = private_clk_get_rate(sclka);
                                if (((rate / 1000) % 24000) != 0) {
                                        private_clk_set_rate(sclka, 1200000000);
                                }
                        }
                }
                private_clk_set_rate(sensor->mclk, 24000000);
                private_clk_prepare_enable(sensor->mclk);
                break;
	}

	ISP_WARNING("\n====>[default_boot=%d] [resolution=%dx%d] [video_interface=%d] [MCLK=%d] \n", info->default_boot, wsize->width, wsize->height, info->video_interface, info->mclk);
	reset_gpio = info->rst_gpio;
	pwdn_gpio = info->pwdn_gpio;

	sensor_set_attr(sd, wsize);
	sensor->priv = wsize;
        sensor->video.max_fps = wsize->fps;
	sensor->video.min_fps = SENSOR_OUTPUT_MIN_FPS << 16 | 1;

	return 0;

}

static int sensor_g_chip_ident(struct tx_isp_subdev *sd,
			       struct tx_isp_chip_ident *chip)
{
	struct i2c_client *client = tx_isp_get_subdevdata(sd);
	unsigned int ident = 0;
	int ret = ISP_SUCCESS;

	sensor_attr_check(sd);
	if (reset_gpio != -1) {
		ret = private_gpio_request(reset_gpio,"sensor_reset");
		if (!ret) {
			private_gpio_direction_output(reset_gpio, 1);
			private_msleep(5);
			private_gpio_direction_output(reset_gpio, 0);
			private_msleep(5);
			private_gpio_direction_output(reset_gpio, 1);
			private_msleep(5);
		} else {
			ISP_ERROR("gpio request fail %d\n",reset_gpio);
		}
	}
	if (pwdn_gpio != -1) {
		ret = private_gpio_request(pwdn_gpio,"sensor_pwdn");
		if (!ret) {
			private_gpio_direction_output(pwdn_gpio, 0);
			private_msleep(5);
			private_gpio_direction_output(pwdn_gpio, 1);
			private_msleep(5);
		} else {
			ISP_ERROR("gpio request fail %d\n",pwdn_gpio);
		}
	}
	ret = sensor_detect(sd, &ident);
	if (ret) {
		ISP_ERROR("chip found @ 0x%x (%s) is not an %s chip.\n",
			  client->addr, client->adapter->name, SENSOR_NAME);
		return ret;
	}
	ISP_WARNING("%s chip found @ 0x%02x (%s)\n",
		    SENSOR_NAME, client->addr, client->adapter->name);
	ISP_WARNING("sensor driver version %s\n",SENSOR_VERSION);
	if (chip) {
		memcpy(chip->name, SENSOR_NAME, sizeof(SENSOR_NAME));
		chip->ident = ident;
		chip->revision = SENSOR_VERSION;
	}

	return 0;
}

static int sensor_sensor_ops_ioctl(struct tx_isp_subdev *sd, unsigned int cmd, void *arg)
{
	long ret = 0;
	struct tx_isp_sensor_value *sensor_val = arg;

	if (IS_ERR_OR_NULL(sd)) {
		ISP_ERROR("[%d]The pointer is invalid!\n", __LINE__);
		return -EINVAL;
	}
	switch(cmd) {
	case TX_ISP_EVENT_SENSOR_EXPO:
		if (arg)
			ret = sensor_set_expo(sd, sensor_val->value);
		break;
	case TX_ISP_EVENT_SENSOR_INT_TIME:
		//if (arg)
		//	ret = sensor_set_integration_time(sd, sensor_val->value);
		break;
	case TX_ISP_EVENT_SENSOR_AGAIN:
		//if (arg)
		//	ret = sensor_set_analog_gain(sd, sensor_val->value);
		break;
	case TX_ISP_EVENT_SENSOR_DGAIN:
		if (arg)
			ret = sensor_set_digital_gain(sd, sensor_val->value);
		break;
	case TX_ISP_EVENT_SENSOR_BLACK_LEVEL:
		if (arg)
			ret = sensor_get_black_pedestal(sd, sensor_val->value);
		break;
	case TX_ISP_EVENT_SENSOR_RESIZE:
		if (arg)
			ret = sensor_set_mode(sd, sensor_val->value);
		break;
	case TX_ISP_EVENT_SENSOR_PREPARE_CHANGE:
		ret = sensor_write_array(sd, sensor_stream_off_mipi);
		break;
	case TX_ISP_EVENT_SENSOR_FINISH_CHANGE:
		ret = sensor_write_array(sd, sensor_stream_on_mipi);
		break;
	case TX_ISP_EVENT_SENSOR_FPS:
		if (arg)
			ret = sensor_set_fps(sd, sensor_val->value);
		break;
	case TX_ISP_EVENT_SENSOR_VFLIP:
		if (arg)
			ret = sensor_set_hvflip(sd, sensor_val->value);
		break;
	default:
		break;
	}

	return ret;
}

static int sensor_g_register(struct tx_isp_subdev *sd, struct tx_isp_dbg_register *reg)
{
	unsigned char val = 0;
	int len = 0;
	int ret = 0;

	len = strlen(sd->chip.name);
	if (len && strncmp(sd->chip.name, reg->name, len)) {
		return -EINVAL;
	}
	if (!private_capable(CAP_SYS_ADMIN))
		return -EPERM;
	ret = sensor_read(sd, reg->reg & 0xffff, &val);
	reg->val = val;
	reg->size = 2;

	return ret;
}

static int sensor_s_register(struct tx_isp_subdev *sd, const struct tx_isp_dbg_register *reg)
{
	int len = 0;

	len = strlen(sd->chip.name);
	if (len && strncmp(sd->chip.name, reg->name, len)) {
		return -EINVAL;
	}
	if (!private_capable(CAP_SYS_ADMIN))
		return -EPERM;
	sensor_write(sd, reg->reg & 0xffff, reg->val & 0xff);

	return 0;
}

static struct tx_isp_subdev_core_ops sensor_core_ops = {
	.g_chip_ident = sensor_g_chip_ident,
	.reset = sensor_reset,
	.init = sensor_init,
	.g_register = sensor_g_register,
	.s_register = sensor_s_register,
};

static struct tx_isp_subdev_video_ops sensor_video_ops = {
	.s_stream = sensor_s_stream,
};

static struct tx_isp_subdev_sensor_ops sensor_sensor_ops = {
	.ioctl = sensor_sensor_ops_ioctl,
};

static struct tx_isp_subdev_ops sensor_ops = {
	.core = &sensor_core_ops,
	.video = &sensor_video_ops,
	.sensor = &sensor_sensor_ops,
};

/* It's the sensor device */
static u64 tx_isp_module_dma_mask = ~(u64)0;
struct platform_device sensor_platform_device = {
	.name = SENSOR_NAME,
	.id = -1,
	.dev = {
		.dma_mask = &tx_isp_module_dma_mask,
		.coherent_dma_mask = 0xffffffff,
		.platform_data = NULL,
	},
	.num_resources = 0,
};

static int sensor_probe(struct i2c_client *client,
			const struct i2c_device_id *id)
{
	struct tx_isp_subdev *sd;
	struct tx_isp_video_in *video;
	struct tx_isp_sensor *sensor;

	sensor = (struct tx_isp_sensor *)kzalloc(sizeof(*sensor), GFP_KERNEL);
	if (!sensor) {
		ISP_ERROR("Failed to allocate sensor subdev.\n");
		return -ENOMEM;
	}
	memset(sensor, 0 ,sizeof(*sensor));

	sd = &sensor->sd;
	video = &sensor->video;
	sensor->dev = &client->dev;
	sensor_attr.expo_fs = 1;
	sensor->video.shvflip = 1;
	sensor->video.attr = &sensor_attr;
	tx_isp_subdev_init(&sensor_platform_device, sd, &sensor_ops);
	tx_isp_set_subdevdata(sd, client);
	tx_isp_set_subdev_hostdata(sd, sensor);
	private_i2c_set_clientdata(client, sd);

	pr_debug("probe ok ------->%s\n", SENSOR_NAME);

	return 0;
}

static int sensor_remove(struct i2c_client *client)
{
	struct tx_isp_subdev *sd = private_i2c_get_clientdata(client);
	struct tx_isp_sensor *sensor = tx_isp_get_subdev_hostdata(sd);

	if (reset_gpio != -1)
		private_gpio_free(reset_gpio);
	if (pwdn_gpio != -1)
		private_gpio_free(pwdn_gpio);

	private_clk_disable_unprepare(sensor->mclk);
	private_devm_clk_put(&client->dev, sensor->mclk);
	tx_isp_subdev_deinit(sd);
	kfree(sensor);

	return 0;
}

static const struct i2c_device_id sensor_id[] = {
	{ SENSOR_NAME, 0 },
	{ }
};
MODULE_DEVICE_TABLE(i2c, sensor_id);

static struct i2c_driver sensor_driver = {
	.driver = {
		.owner = THIS_MODULE,
		.name = SENSOR_NAME,
	},
	.probe = sensor_probe,
	.remove = sensor_remove,
	.id_table = sensor_id,
};

static __init int init_sensor(void)
{
	return private_i2c_add_driver(&sensor_driver);
}

static __exit void exit_sensor(void)
{
	private_i2c_del_driver(&sensor_driver);
}

module_init(init_sensor);
module_exit(exit_sensor);

MODULE_DESCRIPTION("A low-level driver for "SENSOR_NAME" sensor");
MODULE_LICENSE("GPL");
