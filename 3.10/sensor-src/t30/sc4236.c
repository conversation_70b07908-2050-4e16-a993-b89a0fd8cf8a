// SPDX-License-Identifier: GPL-2.0+
/*
 * sc4236.c
 * Copyright (C) 2012 Ingenic Semiconductor Co., Ltd.
 */

#include <linux/init.h>
#include <linux/module.h>
#include <linux/slab.h>
#include <linux/i2c.h>
#include <linux/delay.h>
#include <linux/gpio.h>
#include <linux/clk.h>
#include <linux/proc_fs.h>
#include <tx-isp-common.h>
#include <sensor-common.h>
#include <sensor-info.h>

#define SENSOR_NAME "sc4236"
#define SENSOR_BUS_TYPE TX_SENSOR_CONTROL_INTERFACE_I2C
#define SENSOR_I2C_ADDRESS 0x30
#define SENSOR_MAX_WIDTH 2048
#define SENSOR_MAX_HEIGHT 1536
#define SENSOR_CHIP_ID 0x3235
#define SENSOR_CHIP_ID_H (0x32)
#define SENSOR_CHIP_ID_L (0x35)
#define SENSOR_REG_END 0xffff
#define SENSOR_REG_DELAY 0xfffe
#define SENSOR_SUPPORT_SCLK_4M (120000000)
#define SENSOR_SUPPORT_SCLK_3M (78000000)
#define SENSOR_OUTPUT_MAX_FPS 25
#define SENSOR_OUTPUT_MIN_FPS 5
#define DRIVE_CAPABILITY_1
#define SENSOR_VERSION "H20180827a"

typedef enum {
	SENSOR_RES_500 = 500,
	SENSOR_RES_400 = 400,
	SENSOR_RES_330 = 330,
	SENSOR_RES_300 = 300,
	SENSOR_RES_200 = 200,
	SENSOR_RES_130 = 130,
	SENSOR_RES_100 = 100,
} sensor_res_mode;

static int reset_gpio = GPIO_PA(18);
module_param(reset_gpio, int, S_IRUGO);
MODULE_PARM_DESC(reset_gpio, "Reset GPIO NUM");

static int pwdn_gpio = -1;
module_param(pwdn_gpio, int, S_IRUGO);
MODULE_PARM_DESC(pwdn_gpio, "Power down GPIO NUM");

static int sensor_resolution = SENSOR_RES_400;
module_param(sensor_resolution, int, S_IRUGO);
MODULE_PARM_DESC(sensor_resolution, "Sensor Resolution set interface");

static struct sensor_info sensor_info = {
	.name = SENSOR_NAME,
	.chip_id = SENSOR_CHIP_ID,
	.version = SENSOR_VERSION,
	.min_fps = SENSOR_OUTPUT_MIN_FPS,
	.max_fps = SENSOR_OUTPUT_MAX_FPS,
	.chip_i2c_addr = SENSOR_I2C_ADDRESS,
	.width = SENSOR_MAX_WIDTH,
	.height = SENSOR_MAX_HEIGHT,
};

struct regval_list {
    uint16_t reg_num;
    unsigned char value;
};

struct again_lut {
	unsigned int value;
	unsigned int gain;
};
struct again_lut sensor_again_lut[] = {
	{0x10, 0},
	{0x11, 5731},
	{0x12, 11136},
	{0x13, 16248},
	{0x14, 21097},
	{0x15, 25710},
	{0x16, 30109},
	{0x17, 34312},
	{0x18, 38336},
	{0x19, 42195},
	{0x1a, 45904},
	{0x1b, 49472},
	{0x1c, 52910},
	{0x1d, 56228},
	{0x1e, 59433},
	{0x1f, 62534},
	{0x110,	65536},
	{0x111,	71267},
	{0x112,	76672},
	{0x113,	81784},
	{0x114,	86633},
	{0x115,	91246},
	{0x116,	95645},
	{0x117,	99848},
	{0x118, 103872},
	{0x119,	107731},
	{0x11a,	111440},
	{0x11b,	115008},
	{0x11c,	118446},
	{0x11d,	121764},
	{0x11e,	124969},
	{0x11f,	128070},
	{0x310, 131072},
	{0x311,	136803},
	{0x312,	142208},
	{0x313,	147320},
	{0x314,	152169},
	{0x315,	156782},
	{0x316,	161181},
	{0x317,	165384},
	{0x318,	169408},
	{0x319,	173267},
	{0x31a,	176976},
	{0x31b,	180544},
	{0x31c,	183982},
	{0x31d,	187300},
	{0x31e,	190505},
	{0x31f,	193606},
	{0x710, 196608},
	{0x711,	202339},
	{0x712,	207744},
	{0x713,	212856},
	{0x714,	217705},
	{0x715,	222318},
	{0x716,	226717},
	{0x717,	230920},
	{0x718,	234944},
	{0x719,	238803},
	{0x71a,	242512},
	{0x71b,	246080},
	{0x71c,	249518},
	{0x71d,	252836},
	{0x71e,	256041},
	/* {0x71f, 259142}, */
};

struct tx_isp_sensor_attribute sensor_attr;

unsigned int sensor_alloc_again(unsigned int isp_gain, unsigned char shift, unsigned int *sensor_again)
{
	struct again_lut *lut = sensor_again_lut;
	while (lut->gain <= sensor_attr.max_again) {
		if (isp_gain == 0) {
			*sensor_again = lut[0].value;
			return lut[0].gain;
		}
		else if (isp_gain < lut->gain) {
			*sensor_again = (lut - 1)->value;
			return (lut - 1)->gain;
		}
		else {
			if ((lut->gain == sensor_attr.max_again) && (isp_gain >= lut->gain)) {
				*sensor_again = lut->value;
				return lut->gain;
			}
		}

		lut++;
	}

	return isp_gain;
}

unsigned int sensor_alloc_dgain(unsigned int isp_gain, unsigned char shift, unsigned int *sensor_dgain)
{
	return isp_gain;
}

struct tx_isp_sensor_attribute sensor_attr={
	.name = SENSOR_NAME,
	.chip_id = 0x3235,
	.cbus_type = SENSOR_BUS_TYPE,
	.cbus_mask = V4L2_SBUS_MASK_SAMPLE_8BITS | V4L2_SBUS_MASK_ADDR_16BITS,
	.cbus_device = SENSOR_I2C_ADDRESS,
	.dbus_type = TX_SENSOR_DATA_INTERFACE_MIPI,
	.mipi = {
		.clk = 600,
		.lans = 2,
	},
	.max_again = 256041,
	.max_dgain = 0,
	.min_integration_time = 3,
	.min_integration_time_native = 3,
	.max_integration_time_native = 1916,
	.integration_time_limit = 1916,
	.total_width = 2500,
	.total_height = 1920,
	.max_integration_time = 1916,
	.integration_time_apply_delay = 2,
	.again_apply_delay = 2,
	.dgain_apply_delay = 0,
	.sensor_ctrl.alloc_again = sensor_alloc_again,
	.sensor_ctrl.alloc_dgain = sensor_alloc_dgain,
	// void priv; /* point to struct tx_isp_sensor_board_info */
};

struct tx_isp_sensor_attribute sensor_attr_3m={
	.name = SENSOR_NAME,
	.chip_id = 0x3235,
	.cbus_type = TX_SENSOR_CONTROL_INTERFACE_I2C,
	.cbus_mask = V4L2_SBUS_MASK_SAMPLE_8BITS | V4L2_SBUS_MASK_ADDR_16BITS,
	.cbus_device = 0x30,
	.dbus_type = TX_SENSOR_DATA_INTERFACE_MIPI,
	.mipi = {
		.clk = 400,
		.lans = 2,
	},
	.max_again = 256041,
	.max_dgain = 0,
	.min_integration_time = 3,
	.min_integration_time_native = 3,
	.max_integration_time_native = 1996,
	.integration_time_limit = 1996,
	.total_width = 2600,
	.total_height = 2000,
	.max_integration_time = 1996,
	.integration_time_apply_delay = 2,
	.again_apply_delay = 2,
	.dgain_apply_delay = 0,
	.sensor_ctrl.alloc_again = sensor_alloc_again,
	.sensor_ctrl.alloc_dgain = sensor_alloc_dgain,
	// void priv; /* point to struct tx_isp_sensor_board_info */
};


static struct regval_list sensor_init_regs_2048_1536_30fps_mipi_3m[] = {
	{0x0103, 0x01},
	{0x0100, 0x00},
	{0x301c, 0x78},
	{0x3640, 0x02},
	{0x3641, 0x02},
	{0x3018, 0x33},
	{0x3031, 0x0a},
	{0x3037, 0x20},
	{0x3001, 0xfe},
	{0x4603, 0x00},
	{0x303f, 0x01},
	{0x337f, 0x03},
	{0x3368, 0x04},
	{0x3369, 0x00},
	{0x336a, 0x00},
	{0x336b, 0x00},
	{0x3367, 0x08},
	{0x3307, 0x18},
	{0x3632, 0x18},
	{0x3038, 0xcc},
	{0x363c, 0x05},
	{0x363d, 0x05},
	{0x3639, 0x09},
	{0x363a, 0x1f},
	{0x363b, 0x0c},
	{0x3908, 0x15},
	{0x3620, 0x28},
	{0x3631, 0x88},
	{0x3636, 0x24},
	{0x3638, 0x18},
	{0x3625, 0x03},
	{0x4837, 0x20},
	{0x3333, 0x20},
	{0x330a, 0x01},
	{0x366e, 0x08},
	{0x366f, 0x2f},
	{0x3306, 0x60},
	{0x330b, 0x10},
	{0x3f00, 0x0f},
	{0x3320, 0x06},
	{0x3326, 0x00},
	{0x331e, 0x21},
	{0x331f, 0x71},
	{0x3308, 0x18},
	{0x3303, 0x30},
	{0x3309, 0x80},
	{0x3933, 0x0a},
	{0x3942, 0x02},
	{0x3941, 0x14},
	{0x394c, 0x08},
	{0x394d, 0x14},
	{0x3954, 0x18},
	{0x3955, 0x18},
	{0x3958, 0x10},
	{0x3959, 0x20},
	{0x395a, 0x38},
	{0x395b, 0x38},
	{0x395c, 0x20},
	{0x395d, 0x10},
	{0x395e, 0x24},
	{0x395f, 0x00},
	{0x3960, 0xc4},
	{0x3961, 0xb1},
	{0x3637, 0x63},
	{0x3366, 0x78},
	{0x33aa, 0x00},
	{0x320c, 0x09},
	{0x320d, 0xc4},
	{0x3f04, 0x04},
	{0x3f05, 0xbe},
	{0x320e, 0x07},
	{0x320f, 0x80},
	{0x3235, 0x0c},
	{0x3236, 0x7e},
	{0x36e9, 0x02},
	{0x36ea, 0x36},
	{0x36eb, 0x06},
	{0x36ec, 0x0e},
	{0x36ed, 0x03},
	{0x36f9, 0x01},
	{0x36fa, 0x8a},
	{0x36fb, 0x00},
	{0x3222, 0x29},
	{0x3901, 0x02},
	{0x3635, 0xe2},
	{0x3963, 0x80},
	{0x3e1e, 0x34},
	{0x3211, 0x04},
	{0x3213, 0x04},
	{0x320a, 0x06},
	{0x320b, 0x00},
	{0x3202, 0x00},
	{0x3203, 0x00},
	{0x3206, 0x06},
	{0x3207, 0x0b},
	{0x330f, 0x04},
	{0x3310, 0x20},
	{0x3314, 0x04},
	{0x330e, 0x50},
	{0x4827, 0x46},
	{0x3650, 0x42},
	{0x5000, 0x06},
	{0x5780, 0x7f},
	{0x5781, 0x04},
	{0x5782, 0x03},
	{0x5783, 0x02},
	{0x5784, 0x01},
	{0x5785, 0x18},
	{0x5786, 0x10},
	{0x5787, 0x08},
	{0x5788, 0x02},
	{0x5789, 0x20},
	{0x578a, 0x30},
	{0x3962, 0x09},
	{0x3946, 0x48},
	{0x3947, 0x20},
	{0x3948, 0x0a},
	{0x3949, 0x10},
	{0x394a, 0x28},
	{0x394b, 0x48},
	{0x394e, 0x28},
	{0x3950, 0x20},
	{0x3951, 0x10},
	{0x3940, 0x15},
	{0x3934, 0x16},
	{0x3943, 0x20},
	{0x3952, 0x68},
	{0x3953, 0x38},
	{0x3956, 0x38},
	{0x3957, 0x78},
	{0x394f, 0x40},
	{0x3200, 0x00},
	{0x3201, 0x84},
	{0x3204, 0x08},
	{0x3205, 0x8b},
	{0x3208, 0x08},
	{0x3209, 0x00},
	{0x3802, 0x00},
	{0x3e00, 0x00},
	{0x3e01, 0xc7},
	{0x3e02, 0xc0},
	{0x3e03, 0x0b},
	{0x3e06, 0x00},
	{0x3e07, 0x80},
	{0x3e08, 0x03},
	{0x3e09, 0x10},
	{0x3301, 0x1e},
	{0x3633, 0x23},
	{0x3630, 0x80},
	{0x3622, 0xf6},

	{SENSOR_REG_END, 0x00},
};

static struct regval_list sensor_init_regs_2304_1536_25fps_mipi[] = {
	{0x0103, 0x01},
	{0x0100, 0x00},
	{0x301c, 0x78},
	{0x3640, 0x02},
	{0x3641, 0x02},
	{0x3018, 0x33},
	{0x3031, 0x0a},
	{0x3037, 0x20},
	{0x3001, 0xfe},
	{0x4603, 0x00},
	{0x303f, 0x01},
	{0x337f, 0x03},
	{0x3368, 0x04},
	{0x3369, 0x00},
	{0x336a, 0x00},
	{0x336b, 0x00},
	{0x3367, 0x08},
	{0x3307, 0x18},
	{0x3632, 0x18},
	{0x3038, 0xcc},
	{0x363c, 0x05},
	{0x363d, 0x05},
	{0x3639, 0x09},
	{0x363a, 0x1f},
	{0x363b, 0x0c},
	{0x3908, 0x15},
	{0x3620, 0x28},
	{0x3631, 0x88},
	{0x3636, 0x24},
	{0x3638, 0x18},
	{0x3625, 0x03},
	{0x4837, 0x20},
	{0x3333, 0x20},
	{0x330a, 0x01},
	{0x366e, 0x08},
	{0x366f, 0x2f},
	{0x3306, 0x60},
	{0x330b, 0x10},
	{0x3f00, 0x0f},
	{0x3320, 0x06},
	{0x3326, 0x00},
	{0x331e, 0x21},
	{0x331f, 0x71},
	{0x3308, 0x18},
	{0x3303, 0x30},
	{0x3309, 0x80},
	{0x3933, 0x0a},
	{0x3942, 0x02},
	{0x3941, 0x14},
	{0x394c, 0x08},
	{0x394d, 0x14},
	{0x3954, 0x18},
	{0x3955, 0x18},
	{0x3958, 0x10},
	{0x3959, 0x20},
	{0x395a, 0x38},
	{0x395b, 0x38},
	{0x395c, 0x20},
	{0x395d, 0x10},
	{0x395e, 0x24},
	{0x395f, 0x00},
	{0x3960, 0xc4},
	{0x3961, 0xb1},
	{0x3637, 0x63},
	{0x3366, 0x78},
	{0x33aa, 0x00},
	{0x320c, 0x09},
	{0x320d, 0xc4},
	{0x3f04, 0x04},
	{0x3f05, 0xbe},
	{0x3235, 0x0c},
	{0x3236, 0x7e},
	{0x36e9, 0x02},
	{0x36ea, 0x36},
	{0x36eb, 0x06},
	{0x36ec, 0x0e},
	{0x36ed, 0x03},
	{0x36f9, 0x01},
	{0x36fa, 0x8a},
	{0x36fb, 0x00},
	{0x3222, 0x29},
	{0x3901, 0x02},
	{0x3635, 0xe2},
	{0x3963, 0x80},
	{0x3e1e, 0x34},
	{0x3211, 0x04},
	{0x3213, 0x04},
	{0x3208, 0x09},
	{0x3209, 0x00},
	{0x320a, 0x06},
	{0x320b, 0x00},
	{0x3200, 0x00},
	{0x3201, 0x04},
	{0x3202, 0x00},
	{0x3203, 0x00},
	{0x3204, 0x09},
	{0x3205, 0x0b},
	{0x3206, 0x06},
	{0x3207, 0x0b},
	{0x330f, 0x04},
	{0x3310, 0x20},
	{0x3314, 0x04},
	{0x330e, 0x50},
	{0x4827, 0x46},
	{0x3650, 0x42},
	{0x5000, 0x06},
	{0x5780, 0x7f},
	{0x5781, 0x04},
	{0x5782, 0x03},
	{0x5783, 0x02},
	{0x5784, 0x01},
	{0x5785, 0x18},
	{0x5786, 0x10},
	{0x5787, 0x08},
	{0x5788, 0x02},
	{0x5789, 0x20},
	{0x578a, 0x30},
	{0x3962, 0x09},
	{0x3946, 0x48},
	{0x3947, 0x20},
	{0x3948, 0x0a},
	{0x3949, 0x10},
	{0x394a, 0x28},
	{0x394b, 0x48},
	{0x394e, 0x28},
	{0x3950, 0x20},
	{0x3951, 0x10},
	{0x3940, 0x15},
	{0x3934, 0x16},
	{0x3943, 0x20},
	{0x3952, 0x68},
	{0x3953, 0x38},
	{0x3956, 0x38},
	{0x3957, 0x78},
	{0x394f, 0x40},
	{0x320e, 0x07},
	{0x320f, 0x80},
	{0x3802, 0x00},
	{0x3e00, 0x00},
	{0x3e01, 0xef},
	{0x3e02, 0xc0},
	{0x3e03, 0x0b},
	{0x3e06, 0x00},
	{0x3e07, 0x80},
	{0x3e08, 0x03},
	{0x3e09, 0x10},
	{0x3301, 0x1e},
	{0x3633, 0x23},
	{0x3630, 0x80},
	{0x3622, 0xf6},

	{SENSOR_REG_END, 0x00},
};

static struct regval_list sensor_init_regs_2304_1440_15fps_mipi[] = {
	{0x0103, 0x01},
	{0x0100, 0x00},
	{0x36e9, 0xd6},
	{0x301c, 0x78},
	{0x3641, 0x02},
	{0x3640, 0x00},
	{0x3d08, 0x00},
	{0x3018, 0x33},
	{0x3031, 0x0a},
	{0x3037, 0x20},
	{0x3001, 0xfe},
	{0x4603, 0x00},
	{0x303f, 0x01},
	{0x337f, 0x03},
	{0x3368, 0x04},
	{0x3369, 0x00},
	{0x336a, 0x00},
	{0x336b, 0x00},
	{0x3367, 0x08},
	{0x3307, 0x18},
	{0x3632, 0x18},
	{0x3038, 0xcc},
	{0x363c, 0x05},
	{0x363d, 0x05},
	{0x3639, 0x09},
	{0x363a, 0x1f},
	{0x363b, 0x0c},
	{0x3908, 0x15},
	{0x3620, 0x28},
	{0x3631, 0x88},
	{0x3636, 0x24},
	{0x3638, 0x18},
	{0x3625, 0x03},
	{0x3333, 0x20},
	{0x3e06, 0x00},
	{0x3e08, 0x03},
	{0x3e09, 0x10},
	{0x366e, 0x08},
	{0x366f, 0x2f},
	{0x3f00, 0x0f},
	{0x3320, 0x06},
	{0x3326, 0x00},
	{0x331e, 0x21},
	{0x331f, 0x71},
	{0x3308, 0x18},
	{0x3303, 0x30},
	{0x3309, 0x80},
	{0x3933, 0x0a},
	{0x3942, 0x02},
	{0x3941, 0x14},
	{0x394c, 0x08},
	{0x394d, 0x14},
	{0x3954, 0x18},
	{0x3955, 0x18},
	{0x3958, 0x10},
	{0x3959, 0x20},
	{0x395a, 0x38},
	{0x395b, 0x38},
	{0x395c, 0x20},
	{0x395d, 0x10},
	{0x395e, 0x24},
	{0x395f, 0x00},
	{0x3960, 0xc4},
	{0x3961, 0xb1},
	{0x3637, 0x63},
	{0x3802, 0x01},
	{0x3366, 0x78},
	{0x33aa, 0x00},
	{0x3222, 0x29},
	{0x3901, 0x02},
	{0x3635, 0xe2},
	{0x3963, 0x80},
	{0x3e1e, 0x34},
	{0x330f, 0x04},
	{0x3310, 0x20},
	{0x3314, 0x04},
	{0x330e, 0x50},
	{0x330a, 0x00},
	{0x4827, 0x46},
	{0x3650, 0x42},
	{0x3306, 0x42},
	{0x330b, 0xd2},
	{0x3208, 0x09},
	{0x3209, 0x00},
	{0x320a, 0x05},
	{0x320b, 0xa0},
	{0x3200, 0x00},
	{0x3201, 0x00},
	{0x3202, 0x00},
	{0x3203, 0x30},
	{0x3204, 0x09},
	{0x3205, 0x0f},
	{0x3206, 0x05},
	{0x3207, 0xdf},
	{0x3211, 0x08},
	{0x3213, 0x08},
	{0x320c, 0x0a},
	{0x320d, 0x28},
	{0x3f04, 0x04},
	{0x3f05, 0xf0},
	{0x3e01, 0xbb},
	{0x3e02, 0x40},
	{0x36e9, 0x56},
	{0x36ea, 0x33},
	{0x36eb, 0x06},
	{0x36ec, 0x0e},
	{0x36ed, 0x03},
	{0x36f9, 0x01},
	{0x36fa, 0x9e},
	{0x36fb, 0x00},
	{0x4837, 0x33},
	{0x5000, 0x06},
	{0x5780, 0x7f},
	{0x5781, 0x04},
	{0x5782, 0x03},
	{0x5783, 0x02},
	{0x5784, 0x01},
	{0x5785, 0x18},
	{0x5786, 0x10},
	{0x5787, 0x08},
	{0x5788, 0x02},
	{0x5789, 0x20},
	{0x578a, 0x30},
	{0x3962, 0x09},
	{0x3946, 0x48},
	{0x3947, 0x20},
	{0x3948, 0x0a},
	{0x3949, 0x10},
	{0x394a, 0x28},
	{0x394b, 0x48},
	{0x394e, 0x28},
	{0x3950, 0x20},
	{0x3951, 0x10},
	{0x3940, 0x15},
	{0x3934, 0x16},
	{0x3943, 0x20},
	{0x3952, 0x68},
	{0x3953, 0x38},
	{0x3956, 0x38},
	{0x3957, 0x78},
	{0x394f, 0x40},
	{0x320e, 0x07},
	{0x320f, 0xd0},
	{0x3235, 0x0f},
	{0x3236, 0x9e},
	{0x3301, 0x14},
	{0x3633, 0x23},
	{0x3630, 0x80},
	{0x3622, 0xf6},
	//{0x0100, 0x01},

	{SENSOR_REG_END, 0x00},
};
/*
 * the order of the sensor_win_sizes is [full_resolution, preview_resolution].
 */
static struct tx_isp_sensor_win_setting sensor_win_sizes[] = {
	/* 2304*1536 @25fps */
	{
		.width = 2304,
		.height = 1536,
		.fps = 25 << 16 | 1,
		.mbus_code = V4L2_MBUS_FMT_SBGGR10_1X10,
		.colorspace = V4L2_COLORSPACE_SRGB,
		.regs = sensor_init_regs_2304_1536_25fps_mipi,
	},
	/* 2048*1536 @25fps */
	{
		.width = 2048,
		.height = 1536,
		.fps = 25 << 16 | 1,
		.mbus_code = V4L2_MBUS_FMT_SBGGR10_1X10,
		.colorspace = V4L2_COLORSPACE_SRGB,
		.regs = sensor_init_regs_2048_1536_30fps_mipi_3m,
	},
	/* 2304*1440 @15fps */
	{
		.width = 2304,
		.height = 1440,
		.fps = 15 << 16 | 1,
		.mbus_code = V4L2_MBUS_FMT_SBGGR10_1X10,
		.colorspace = V4L2_COLORSPACE_SRGB,
		.regs = sensor_init_regs_2304_1440_15fps_mipi,
	},
};

static enum v4l2_mbus_pixelcode sensor_mbus_code[] = {
	V4L2_MBUS_FMT_SBGGR10_1X10,
};


static struct regval_list sensor_stream_on_mipi[] = {
	{0x0100, 0x01},
	{SENSOR_REG_END, 0x00},
};

static struct regval_list sensor_stream_off_mipi[] = {
	{0x0100, 0x00},
	{SENSOR_REG_END, 0x00},
};

int sensor_read(struct tx_isp_subdev *sd, uint16_t reg,
		unsigned char *value)
{
	struct i2c_client *client = tx_isp_get_subdevdata(sd);
	uint8_t buf[2] = {(reg >> 8) & 0xff, reg & 0xff};
	struct i2c_msg msg[2] = {
		[0] = {
			.addr = client->addr,
			.flags = 0,
			.len = 2,
			.buf = buf,
		},
		[1] = {
			.addr = client->addr,
			.flags = I2C_M_RD,
			.len = 1,
			.buf = value,
		}
	};
	int ret;
	ret = private_i2c_transfer(client->adapter, msg, 2);
	if (ret > 0)
		ret = 0;

	return ret;
}

int sensor_write(struct tx_isp_subdev *sd, uint16_t reg,
		 unsigned char value)
{
	struct i2c_client *client = tx_isp_get_subdevdata(sd);
	uint8_t buf[3] = {(reg >> 8) & 0xff, reg & 0xff, value};
	struct i2c_msg msg = {
		.addr = client->addr,
		.flags = 0,
		.len = 3,
		.buf = buf,
	};
	int ret;
	ret = private_i2c_transfer(client->adapter, &msg, 1);
	if (ret > 0)
		ret = 0;

	return ret;
}

static int sensor_read_array(struct tx_isp_subdev *sd, struct regval_list *vals)
{
	int ret;
	unsigned char val;
	while (vals->reg_num != SENSOR_REG_END) {
		if (vals->reg_num == SENSOR_REG_DELAY) {
			msleep(vals->value);
		} else {
			ret = sensor_read(sd, vals->reg_num, &val);
			if (ret < 0)
				return ret;
		}
		vals++;
	}

	return 0;
}

static int sensor_write_array(struct tx_isp_subdev *sd, struct regval_list *vals)
{
	int ret;
	while (vals->reg_num != SENSOR_REG_END) {
		if (vals->reg_num == SENSOR_REG_DELAY) {
			msleep(vals->value);
		} else {
			ret = sensor_write(sd, vals->reg_num, vals->value);
			if (ret < 0)
				return ret;
		}
		vals++;
	}

	return 0;
}

static int sensor_reset(struct tx_isp_subdev *sd, int val)
{
	return 0;
}

static int sensor_detect(struct tx_isp_subdev *sd, unsigned int *ident)
{
	unsigned char v;
	int ret;

	ret = sensor_read(sd, 0x3107, &v);
	pr_debug("-----%s: %d ret = %d, v = 0x%02x\n", __func__, __LINE__, ret,v);
	if (ret < 0)
		return ret;
	if (v != SENSOR_CHIP_ID_H)
		return -ENODEV;
	*ident = v;

	ret = sensor_read(sd, 0x3108, &v);
	pr_debug("-----%s: %d ret = %d, v = 0x%02x\n", __func__, __LINE__, ret,v);
	if (ret < 0)
		return ret;
	if (v != SENSOR_CHIP_ID_L)
		return -ENODEV;
	*ident = (*ident << 8) | v;

	return 0;
}

static int sensor_set_integration_time(struct tx_isp_subdev *sd, int value)
{
	int ret = 0;

	value *= 2;
	ret = sensor_write(sd, 0x3e00, (unsigned char)((value >> 12) & 0x0f));
	ret += sensor_write(sd, 0x3e01, (unsigned char)((value >> 4) & 0xff));
	ret += sensor_write(sd, 0x3e02, (unsigned char)((value & 0x0f) << 4));

	if (value < 160) {
		ret += sensor_write(sd, 0x3314, 0x14);
	}
	else if (value > 320) {
		ret += sensor_write(sd, 0x3314, 0x04);
	}

	if (ret < 0)
		return ret;

	return 0;
}

static int sensor_set_analog_gain(struct tx_isp_subdev *sd, int value)
{
	int ret = 0;

	ret += sensor_write(sd, 0x3e09, (unsigned char)(value & 0xff));
	ret += sensor_write(sd, 0x3e08, (unsigned char)((value >> 8 << 2) | 0x03));
	if (ret < 0)
		return ret;
	/* denoise logic */
	if (value < 0x110) {
		sensor_write(sd,0x3812,0x00);
		sensor_write(sd,0x3301,0x1e);
		sensor_write(sd,0x3633,0x23);
		sensor_write(sd,0x3630,0x80);
		sensor_write(sd,0x3622,0xf6);
		sensor_write(sd,0x3812,0x30);
	}
	else if (value>=0x110&&value<0x310) {
		sensor_write(sd,0x3812,0x00);
		sensor_write(sd,0x3301,0x50);
		sensor_write(sd,0x3633,0x23);
		sensor_write(sd,0x3630,0x80);
		sensor_write(sd,0x3622,0xf6);
		sensor_write(sd,0x3812,0x30);
	}
	else if (value>=0x310&&value<0x710) {
		sensor_write(sd,0x3812,0x00);
		sensor_write(sd,0x3301,0x50);
		sensor_write(sd,0x3633,0x23);
		sensor_write(sd,0x3630,0x80);
		sensor_write(sd,0x3622,0xf6);
		sensor_write(sd,0x3812,0x30);
	}
	else if (value>=0x710&&value<=0x71e) {
		sensor_write(sd,0x3812,0x00);
		sensor_write(sd,0x3301,0x50);
		sensor_write(sd,0x3633,0x23);
		sensor_write(sd,0x3630,0x80);
		sensor_write(sd,0x3622,0xf6);
		sensor_write(sd,0x3812,0x30);
	}
	else { //may be flick
		sensor_write(sd,0x3812,0x00);
		sensor_write(sd,0x3301,0x50);
		sensor_write(sd,0x3633,0x43);
		sensor_write(sd,0x3630,0x82);
		sensor_write(sd,0x3622,0x16);
		sensor_write(sd,0x3812,0x30);
	}

	return 0;
}

static int sensor_set_digital_gain(struct tx_isp_subdev *sd, int value)
{
	return 0;
}

static int sensor_get_black_pedestal(struct tx_isp_subdev *sd, int value)
{
	return 0;
}

static int sensor_init(struct tx_isp_subdev *sd, int enable)
{
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);
	struct tx_isp_sensor_win_setting *wsize;
	int ret = 0;

	if (!enable)
		return ISP_SUCCESS;
	switch(sensor_resolution) {
	case SENSOR_RES_400:
		wsize = &sensor_win_sizes[0];
		break;
	case SENSOR_RES_300:
		wsize = &sensor_win_sizes[1];
		break;
	case SENSOR_RES_330:
		wsize = &sensor_win_sizes[2];
		break;
	default:
		printk("Do not support this resolution.\n");
		break;
	}

	sensor->video.mbus.width = wsize->width;
	sensor->video.mbus.height = wsize->height;
	sensor->video.mbus.code = wsize->mbus_code;
	sensor->video.mbus.field = V4L2_FIELD_NONE;
	sensor->video.mbus.colorspace = wsize->colorspace;
	sensor->video.fps = wsize->fps;
	ret = sensor_write_array(sd, wsize->regs);
	if (ret)
		return ret;
	ret = tx_isp_call_subdev_notify(sd, TX_ISP_EVENT_SYNC_SENSOR_ATTR, &sensor->video);
	sensor->priv = wsize;

	return 0;
}

static int sensor_s_stream(struct tx_isp_subdev *sd, int enable)
{
	int ret = 0;

	if (enable) {
		ret = sensor_write_array(sd, sensor_stream_on_mipi);
		pr_debug("%s stream on\n", SENSOR_NAME);

	}
	else {
		ret = sensor_write_array(sd, sensor_stream_off_mipi);
		pr_debug("%s stream off\n", SENSOR_NAME);
	}

	return ret;
}

static int sensor_set_fps(struct tx_isp_subdev *sd, int fps)
{
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);
	int ret = 0;
	unsigned int sclk = 0;
	unsigned int hts = 0;
	unsigned int vts = 0;
	unsigned int max_fps = 0;
	unsigned char val = 0;
	unsigned int newformat = 0; //the format is 24.8

	switch(sensor_resolution) {
	case SENSOR_RES_400:
		sclk = SENSOR_SUPPORT_SCLK_4M;
		max_fps = TX_SENSOR_MAX_FPS_25;
		break;
	case SENSOR_RES_300:
		sclk = SENSOR_SUPPORT_SCLK_4M;
		max_fps = TX_SENSOR_MAX_FPS_30;
		break;
	case SENSOR_RES_330:
		sclk = SENSOR_SUPPORT_SCLK_3M;
		max_fps = TX_SENSOR_MAX_FPS_15;
		break;
	default:
		printk("Do not support this resolution.\n");
		ret = -1;
		break;
	}
	newformat = (((fps >> 16) / (fps & 0xffff)) << 8) + ((((fps >> 16) % (fps & 0xffff)) << 8) / (fps & 0xffff));
	if (newformat > (max_fps << 8) || newformat < (SENSOR_OUTPUT_MIN_FPS << 8)) {
		printk("warn: fps(%d) not in range\n", fps);
		return -1;
	}

	val = 0;
	ret += sensor_read(sd, 0x320c, &val);
	hts = val<<8;
	val = 0;
	ret += sensor_read(sd, 0x320d, &val);
	hts |= val;
	if (0 != ret) {
		printk("Error: %s read error\n", SENSOR_NAME);
		return ret;
	}

	vts = sclk * (fps & 0xffff) / hts / ((fps & 0xffff0000) >> 16);

	ret = sensor_write(sd, 0x320f, (unsigned char)(vts & 0xff));
	ret += sensor_write(sd, 0x320e, (unsigned char)(vts >> 8));
	if (0 != ret) {
		printk("err: sensor_write err\n");
		return ret;
	}
	sensor->video.fps = fps;
	sensor->video.attr->max_integration_time_native = vts - 4;
	sensor->video.attr->integration_time_limit = vts - 4;
	sensor->video.attr->total_height = vts;
	sensor->video.attr->max_integration_time = vts - 4;
	ret = tx_isp_call_subdev_notify(sd, TX_ISP_EVENT_SYNC_SENSOR_ATTR, &sensor->video);

	return ret;
}

static int sensor_set_mode(struct tx_isp_subdev *sd, int value)
{
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);
	struct tx_isp_sensor_win_setting *wsize = NULL;
	int ret = ISP_SUCCESS;

	if (value == TX_ISP_SENSOR_FULL_RES_MAX_FPS) {
		if (sensor_resolution == SENSOR_RES_400)
			wsize = &sensor_win_sizes[0];
		else if (sensor_resolution == SENSOR_RES_300)
			wsize = &sensor_win_sizes[1];
		else if (sensor_resolution == SENSOR_RES_330)
			wsize = &sensor_win_sizes[2];
		else
			printk("Do not support this resolution.\n");
	} else if (value == TX_ISP_SENSOR_PREVIEW_RES_MAX_FPS) {
		if (sensor_resolution == SENSOR_RES_400)
			wsize = &sensor_win_sizes[0];
		else if (sensor_resolution == SENSOR_RES_300)
			wsize = &sensor_win_sizes[1];
		else if (sensor_resolution == SENSOR_RES_330)
			wsize = &sensor_win_sizes[2];
		else
			printk("Do not support this resolution.\n");
	}

	if (wsize) {
		sensor->video.mbus.width = wsize->width;
		sensor->video.mbus.height = wsize->height;
		sensor->video.mbus.code = wsize->mbus_code;
		sensor->video.mbus.field = V4L2_FIELD_NONE;
		sensor->video.mbus.colorspace = wsize->colorspace;
		sensor->video.fps = wsize->fps;
		ret = tx_isp_call_subdev_notify(sd, TX_ISP_EVENT_SYNC_SENSOR_ATTR, &sensor->video);
	}

	return ret;
}

static int sensor_set_vflip(struct tx_isp_subdev *sd, int enable)
{
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);
	int ret = 0;
	unsigned char val = 0;

	val = enable ? 0x60 : 0;
	ret += sensor_write(sd, 0x3221, val);
	sensor->video.mbus_change = 0;
	if (!ret)
		ret = tx_isp_call_subdev_notify(sd, TX_ISP_EVENT_SYNC_SENSOR_ATTR, &sensor->video);

	return ret;
}

static int sensor_g_chip_ident(struct tx_isp_subdev *sd,
			       struct tx_isp_chip_ident *chip)
{
	struct i2c_client *client = tx_isp_get_subdevdata(sd);
	unsigned int ident = 0;
	int ret = ISP_SUCCESS;

	if (reset_gpio != -1) {
		ret = private_gpio_request(reset_gpio,"sensor_reset");
		if (!ret) {
			private_gpio_direction_output(reset_gpio, 1);
			private_msleep(5);
			private_gpio_direction_output(reset_gpio, 0);
			private_msleep(5);
			private_gpio_direction_output(reset_gpio, 1);
			private_msleep(5);
		} else {
			printk("gpio request fail %d\n",reset_gpio);
		}
	}
	if (pwdn_gpio != -1) {
		ret = private_gpio_request(pwdn_gpio,"sensor_pwdn");
		if (!ret) {
			private_gpio_direction_output(pwdn_gpio, 0);
			private_msleep(5);
			private_gpio_direction_output(pwdn_gpio, 1);
			private_msleep(5);
		} else {
			printk("gpio request fail %d\n",pwdn_gpio);
		}
	}
	ret = sensor_detect(sd, &ident);
	if (ret) {
		printk("chip found @ 0x%x (%s) is not an %s chip.\n",
		       client->addr, client->adapter->name, SENSOR_NAME);
		return ret;
	}
	printk("%s chip found @ 0x%02x (%s)\n",
	       SENSOR_NAME, client->addr, client->adapter->name);
	if (chip) {
		memcpy(chip->name, SENSOR_NAME, sizeof(SENSOR_NAME));
		chip->ident = ident;
		chip->revision = SENSOR_VERSION;
	}

	return 0;
}

static int sensor_sensor_ops_ioctl(struct tx_isp_subdev *sd, unsigned int cmd, void *arg)
{
	long ret = 0;

	if (IS_ERR_OR_NULL(sd)) {
		printk("[%d]The pointer is invalid!\n", __LINE__);
		return -EINVAL;
	}
	switch(cmd) {
	case TX_ISP_EVENT_SENSOR_INT_TIME:
		if (arg)
			ret = sensor_set_integration_time(sd, *(int*)arg);
		break;
	case TX_ISP_EVENT_SENSOR_AGAIN:
		if (arg)
			ret = sensor_set_analog_gain(sd, *(int*)arg);
		break;
	case TX_ISP_EVENT_SENSOR_DGAIN:
		if (arg)
			ret = sensor_set_digital_gain(sd, *(int*)arg);
		break;
	case TX_ISP_EVENT_SENSOR_BLACK_LEVEL:
		if (arg)
			ret = sensor_get_black_pedestal(sd, *(int*)arg);
		break;
	case TX_ISP_EVENT_SENSOR_RESIZE:
		if (arg)
			ret = sensor_set_mode(sd, *(int*)arg);
		break;
	case TX_ISP_EVENT_SENSOR_PREPARE_CHANGE:
		ret = sensor_write_array(sd, sensor_stream_off_mipi);
		break;
	case TX_ISP_EVENT_SENSOR_FINISH_CHANGE:
		ret = sensor_write_array(sd, sensor_stream_on_mipi);
		break;
	case TX_ISP_EVENT_SENSOR_FPS:
		if (arg)
			ret = sensor_set_fps(sd, *(int*)arg);
		break;
	case TX_ISP_EVENT_SENSOR_VFLIP:
		if (arg)
			ret = sensor_set_vflip(sd, *(int*)arg);
		break;
	default:
		break;
	}

	return ret;
}

static int sensor_g_register(struct tx_isp_subdev *sd, struct tx_isp_dbg_register *reg)
{
	unsigned char val = 0;
	int len = 0;
	int ret = 0;

	len = strlen(sd->chip.name);
	if (len && strncmp(sd->chip.name, reg->name, len)) {
		return -EINVAL;
	}
	if (!private_capable(CAP_SYS_ADMIN))
		return -EPERM;
	ret = sensor_read(sd, reg->reg & 0xffff, &val);
	reg->val = val;
	reg->size = 2;

	return ret;
}

static int sensor_s_register(struct tx_isp_subdev *sd, const struct tx_isp_dbg_register *reg)
{
	int len = 0;

	len = strlen(sd->chip.name);
	if (len && strncmp(sd->chip.name, reg->name, len)) {
		return -EINVAL;
	}
	if (!private_capable(CAP_SYS_ADMIN))
		return -EPERM;
	sensor_write(sd, reg->reg & 0xffff, reg->val & 0xff);

	return 0;
}

static struct tx_isp_subdev_core_ops sensor_core_ops = {
	.g_chip_ident = sensor_g_chip_ident,
	.reset = sensor_reset,
	.init = sensor_init,
	.g_register = sensor_g_register,
	.s_register = sensor_s_register,
};

static struct tx_isp_subdev_video_ops sensor_video_ops = {
	.s_stream = sensor_s_stream,
};

static struct tx_isp_subdev_sensor_ops sensor_sensor_ops = {
	.ioctl = sensor_sensor_ops_ioctl,
};

static struct tx_isp_subdev_ops sensor_ops = {
	.core = &sensor_core_ops,
	.video = &sensor_video_ops,
	.sensor = &sensor_sensor_ops,
};

/* It's the sensor device */
static u64 tx_isp_module_dma_mask = ~(u64)0;
struct platform_device sensor_platform_device = {
	.name = SENSOR_NAME,
	.id = -1,
	.dev = {
		.dma_mask = &tx_isp_module_dma_mask,
		.coherent_dma_mask = 0xffffffff,
		.platform_data = NULL,
	},
	.num_resources = 0,
};


static int sensor_probe(struct i2c_client *client,
			const struct i2c_device_id *id)
{
	struct tx_isp_subdev *sd;
	struct tx_isp_video_in *video;
	struct tx_isp_sensor *sensor;
	struct tx_isp_sensor_win_setting *wsize;

	sensor = (struct tx_isp_sensor *)kzalloc(sizeof(*sensor), GFP_KERNEL);
	if (!sensor) {
		printk("Failed to allocate sensor subdev.\n");
		return -ENOMEM;
	}
	memset(sensor, 0 ,sizeof(*sensor));
	/* request mclk of sensor */
	sensor->mclk = clk_get(NULL, "cgu_cim");
	if (IS_ERR(sensor->mclk)) {
		printk("Cannot get sensor input clock cgu_cim\n");
		goto err_get_mclk;
	}
	clk_set_rate(sensor->mclk, 24000000);
	clk_enable(sensor->mclk);
	/*
	  convert sensor-gain into isp-gain,
	*/
	//sensor_attr.max_again = 256041;
	//sensor_attr.max_dgain = 0;
	sd = &sensor->sd;
	video = &sensor->video;
	switch(sensor_resolution) {
	case SENSOR_RES_400:
		wsize = &sensor_win_sizes[0];
		sensor->video.attr = &sensor_attr;
		break;
	case SENSOR_RES_300:
		wsize = &sensor_win_sizes[1];
		sensor->video.attr = &sensor_attr;
		break;
	case SENSOR_RES_330:
		wsize = &sensor_win_sizes[2];
		sensor->video.attr = &sensor_attr_3m;
		break;
	default:
		printk("Do not support this resolution.\n");
		break;
	}
	sensor->video.mbus_change = 0;
	sensor->video.vi_max_width = wsize->width;
	sensor->video.vi_max_height = wsize->height;
	sensor->video.mbus.width = wsize->width;
	sensor->video.mbus.height = wsize->height;
	sensor->video.mbus.code = wsize->mbus_code;
	sensor->video.mbus.field = V4L2_FIELD_NONE;
	sensor->video.mbus.colorspace = wsize->colorspace;
	sensor->video.fps = wsize->fps;
	tx_isp_subdev_init(&sensor_platform_device, sd, &sensor_ops);
	tx_isp_set_subdevdata(sd, client);
	tx_isp_set_subdev_hostdata(sd, sensor);
	private_i2c_set_clientdata(client, sd);

	pr_debug("probe ok ------->%s\n", SENSOR_NAME);

	return 0;

err_get_mclk:
	kfree(sensor);

	return -1;
}

static int sensor_remove(struct i2c_client *client)
{
	struct tx_isp_subdev *sd = private_i2c_get_clientdata(client);
	struct tx_isp_sensor *sensor = tx_isp_get_subdev_hostdata(sd);

	if (reset_gpio != -1)
		private_gpio_free(reset_gpio);
	if (pwdn_gpio != -1)
		private_gpio_free(pwdn_gpio);

	private_clk_disable(sensor->mclk);
	private_clk_put(sensor->mclk);
	tx_isp_subdev_deinit(sd);
	kfree(sensor);

	return 0;
}

static const struct i2c_device_id sensor_id[] = {
	{ SENSOR_NAME, 0 },
	{ }
};
MODULE_DEVICE_TABLE(i2c, sensor_id);

static struct i2c_driver sensor_driver = {
	.driver = {
		.owner = THIS_MODULE,
		.name = SENSOR_NAME,
	},
	.probe = sensor_probe,
	.remove = sensor_remove,
	.id_table = sensor_id,
};

static __init int init_sensor(void)
{
	int ret = 0;
	sensor_common_init(&sensor_info);

	ret = private_driver_get_interface();
	if (ret) {
		printk("Failed to init %s driver.\n", SENSOR_NAME);
		return -1;
	}

	return private_i2c_add_driver(&sensor_driver);
}

static __exit void exit_sensor(void)
{
	private_i2c_del_driver(&sensor_driver);
	sensor_common_exit();
}

module_init(init_sensor);
module_exit(exit_sensor);

MODULE_DESCRIPTION("A low-level driver for "SENSOR_NAME" sensor");
MODULE_LICENSE("GPL");
