ccflags-y += -I$(src)/$(KERNEL_VERSION)/isp/include
ccflags-y += -I$(src)/$(KERNEL_VERSION)/sensor-src/include
DIR=$(KERNEL_VERSION)/sensor-src/$(SOC_FAMILY)

ifneq ($(SENSOR_MODEL),)
	MODULE_NAME := sensor_$(SENSOR_MODEL)_$(SOC_FAMILY)
	OUT := $(MODULE_NAME)
	SRCS := \
		$(DIR)/$(SENSOR_MODEL).c \
		$(KERNEL_VERSION)/sensor-src/common/sensor-info.c
	OBJS := $(SRCS:%.c=%.o) \
		$(ASM_SRCS:%.S=%.o)
	$(OUT)-objs := $(OBJS)
	obj-m += $(OUT).o
endif

# Build additional sensor modules if defined
ifneq ($(SENSOR_MODEL_1),)
	MODULE_NAME_1 := sensor_$(SENSOR_MODEL_1)_$(SOC_FAMILY)
	OUT_1 := $(MODULE_NAME_1)
	SRCS_1 := \
		$(DIR)/$(SENSOR_MODEL_1).c \
		$(KERNEL_VERSION)/sensor-src/common/sensor-info.c
	OBJS_1 := $(SRCS_1:%.c=%.o) \
			$(ASM_SRCS:%.S=%.o)
	$(OUT_1)-objs := $(OBJS_1)
	obj-m += $(OUT_1).o
endif

ifneq ($(SENSOR_MODEL_2),)
	MODULE_NAME_2 := sensor_$(SENSOR_MODEL_2)_$(SOC_FAMILY)
	OUT_2 := $(MODULE_NAME_2)
	SRCS_2 := \
		$(DIR)/$(SENSOR_MODEL_2).c \
		$(KERNEL_VERSION)/sensor-src/common/sensor-info.c
	OBJS_2 := $(SRCS_2:%.c=%.o) \
			$(ASM_SRCS:%.S=%.o)
	$(OUT_2)-objs := $(OBJS_2)
	obj-m += $(OUT_2).o
endif
