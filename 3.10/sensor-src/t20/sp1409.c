// SPDX-License-Identifier: GPL-2.0+
/*
 * sp1409.c
 * Copyright (C) 2012 Ingenic Semiconductor Co., Ltd.
 */

#include <linux/init.h>
#include <linux/module.h>
#include <linux/slab.h>
#include <linux/i2c.h>
#include <linux/delay.h>
#include <linux/gpio.h>
#include <linux/clk.h>
#include <sensor-common.h>
#include <sensor-info.h>
#include <apical-isp/apical_math.h>

#define SENSOR_NAME "sp1409"
#define SENSOR_CHIP_ID 0x1409
#define SENSOR_BUS_TYPE TX_SENSOR_CONTROL_INTERFACE_I2C
#define SENSOR_I2C_ADDRESS 0x34
#define SENSOR_MAX_WIDTH 1280
#define SENSOR_MAX_HEIGHT 720
#define SENSOR_CHIP_ID_H (0x14)
#define SENSOR_CHIP_ID_L (0x09)
#define SENSOR_REG_END 0xff
#define SENSOR_REG_DELAY 0xfefe
#define SENSOR_SUPPORT_PCLK (20000*1000)
#define SENSOR_OUTPUT_MAX_FPS 30
#define SENSOR_OUTPUT_MIN_FPS 5
#define SENSOR_VERSION "20180320"
#define DRIVE_CAPABILITY_1

static struct sensor_info sensor_info = {
	.name = SENSOR_NAME,
	.chip_id = SENSOR_CHIP_ID,
	.version = SENSOR_VERSION,
	.min_fps = SENSOR_OUTPUT_MIN_FPS,
	.max_fps = SENSOR_OUTPUT_MAX_FPS,
	.chip_i2c_addr = SENSOR_I2C_ADDRESS,
	.width = SENSOR_MAX_WIDTH,
	.height = SENSOR_MAX_HEIGHT,
};

static int reset_gpio = -1;
module_param(reset_gpio, int, S_IRUGO);
MODULE_PARM_DESC(reset_gpio, "Reset GPIO NUM");

static int pwdn_gpio = -1;
module_param(pwdn_gpio, int, S_IRUGO);
MODULE_PARM_DESC(pwdn_gpio, "Power down GPIO NUM");

static int sensor_gpio_func = DVP_PA_LOW_10BIT;
module_param(sensor_gpio_func, int, S_IRUGO);
MODULE_PARM_DESC(sensor_gpio_func, "Sensor GPIO function");

struct regval_list {
    uint16_t reg_num;
    unsigned char value;
};

struct again_lut {
	unsigned int rpc;
	unsigned char vrefl;
	unsigned int gain;
};
unsigned int final_vrefl = 0;
struct again_lut sensor_again_lut[] = {
	{0x10, 0x0, 0},
	{0x11, 0x05, 573},
	{0x12, 0x0a, 11136},
	{0x13, 0x12, 16248},
	{0x14, 0x1a, 21097},
	{0x15, 0x21, 25710},
	{0x16, 0x27, 30109},
	{0x17, 0x2c, 34312},
	{0x18, 0x31, 38336},
	{0x19, 0x36, 42195},
	{0x1a, 0x3a, 45904},
	{0x1b, 0x3e, 49472},
	{0x1c, 0x42, 52910},
	{0x1d, 0x45, 56228},
	{0x1e, 0x48, 59433},
	{0x1f, 0x4b, 62534},
	{0x20, 0x4E, 65536},
	{0x22, 0x53, 71267},
	{0x24, 0x58, 76672},
	{0x26, 0x5c, 81784},
	{0x28, 0x60, 86633},
	{0x2a, 0x63, 91246},
	{0x2c, 0x66, 95645},
	{0x2e, 0x69, 99848},
	{0x30, 0x6B, 103872},
	{0x32, 0x6D, 107731},
	{0x34, 0x6F, 111440},
	{0x36, 0x71, 115008},
	{0x38, 0x73, 118446},
	{0x3a, 0x75, 121764},
	{0x3c, 0x77, 124969},
	{0x3e, 0x78, 128070},
	{0x40, 0x7a, 131072},
	{0x44, 0x7c, 136803},
	{0x48, 0x7e, 142208},
	{0x4c, 0x80, 147320},
	{0x50, 0x82, 152169},
	{0x54, 0x84, 156782},
	{0x58, 0x86, 161181},
	{0x5c, 0x87, 165384},
	{0x60, 0x88, 169408},
	{0x64, 0x89, 173267},
	{0x68, 0x8a, 176976},
	{0x6c, 0x8b, 180544},
	{0x70, 0x8c, 183982},
	{0x74, 0x8d, 187300},
	{0x78, 0x8e, 190505},
	{0x7c, 0x8f, 193606},
	{0x80, 0x90, 196608},
	{0x88, 0x91, 202339},
	{0x90, 0x92, 207744},
	{0x98, 0x93, 212856},
	{0xa0, 0x94, 217705},
	{0xa8, 0x95, 222318},
	{0xb0, 0x95, 226717},
	{0xb9, 0x96, 231432},
	{0xc0, 0x96, 234944},
	{0xc8, 0x97, 238803},
	{0xd0, 0x97, 242512},
	{0xd8, 0x98, 246080},
	{0xe0, 0x98, 249518},
	{0xe8, 0x99, 252836},
	{0xf0, 0x99, 256041},
	{0xf8, 0x99, 259142},
	{0x100, 0x9a, 262144},
};

struct tx_isp_sensor_attribute sensor_attr;

unsigned int sensor_alloc_again(unsigned int isp_gain, unsigned char shift, unsigned int *sensor_again) {
	struct again_lut *lut = sensor_again_lut;
	while (lut->gain <= sensor_attr.max_again) {
		if (isp_gain == 0) {
			*sensor_again = 0x10;
			final_vrefl = 0;
			return 0;
		} else if (isp_gain < lut->gain) {
			*sensor_again = (lut - 1)->rpc;
			final_vrefl = (lut - 1)->vrefl;
			return (lut - 1)->gain;
		} else {
			if ((lut->gain == sensor_attr.max_again) && (isp_gain >= lut->gain)) {
				*sensor_again = 0x100;
				final_vrefl = 0x9a;
				return lut->gain;
			}
		}

		lut++;
	}
	/* printk("lut->rpc ======0x%x,final_vrefl ===========0x%x isp_gain==========%d\n",lut->rpc,final_vrefl,isp_gain); */
	return isp_gain;
}

unsigned int sensor_alloc_dgain(unsigned int isp_gain, unsigned char shift, unsigned int *sensor_dgain) {
	return isp_gain;
}

struct tx_isp_sensor_attribute sensor_attr = {
	.name = SENSOR_NAME,
	.chip_id = 0x1409,
	.cbus_type = TX_SENSOR_CONTROL_INTERFACE_I2C,
	.cbus_mask = V4L2_SBUS_MASK_SAMPLE_8BITS | V4L2_SBUS_MASK_ADDR_16BITS,
	.cbus_device = SENSOR_I2C_ADDRESS,
	.dbus_type = TX_SENSOR_DATA_INTERFACE_DVP,
	.dvp = {
		.mode = SENSOR_DVP_HREF_MODE,
		.blanking = {
			.vblanking = 0,
			.hblanking = 0,
		},
	},
	.max_again = 262144,
	.max_dgain = 0,
	.min_integration_time = 1,
	.min_integration_time_native = 1,
	.max_integration_time_native = 895,
	.integration_time_limit = 895,
	.total_width = 1328,
	.total_height = 899,
	.max_integration_time = 895,
	.integration_time_apply_delay = 2,
	.again_apply_delay = 2,
	.dgain_apply_delay = 0,
	.sensor_ctrl.alloc_again = sensor_alloc_again,
	.sensor_ctrl.alloc_dgain = sensor_alloc_dgain,
	.one_line_expr_in_us = 44,
};

static struct regval_list sensor_init_regs_1280_720_25fps[] = {
	{0xfd, 0x00},
	{0x24, 0x01},
	{0x27, 0x36},
	{0x26, 0x02},
	{0x28, 0x00},
	{0x40, 0x00},
	{0x25, 0x00},
	{0x41, 0x01},
	{0x2e, 0x01}, //
	{0x89, 0x02},
	{0x90, 0x01},
	{0x69, 0x05},
	{0x6a, 0x00},
	{0x6b, 0x02},
	{0x6c, 0xD0},
	{0xfd, 0x01},
	{0x1f, 0x00},
	{0x1e, 0x00},

	{0xfd, 0x00},
	{0x3d, 0x00},
	{0x43, 0x03},
	{0x45, 0x05},
	{0x46, 0x02},
	{0x47, 0x02},
	{0x49, 0x00},
	{0x3f, 0x08},
	{0x1d, 0x55},
	{0x1e, 0x55},
	//{0x38, 0x00},
	//{0x55, 0x00},


	{0xfd, 0x01},
	{0x15, 0x00},
	{0x0a, 0x00},
	{0x2e, 0x0a},
	{0x30, 0x20},
	{0x31, 0x02},
	{0x33, 0x02},
	{0x34, 0x40},
	{0x36, 0x0A},
	{0x39, 0x01},
	{0x3a, 0x67},
	{0x3c, 0x07},
	{0x3d, 0x03},
	{0x3e, 0x00},
	{0x3f, 0x0B},
	{0x40, 0x00},
	{0x41, 0x3B},
	{0x4a, 0x49},
	{0x4b, 0x03},
	{0x4c, 0x90},
	{0x4f, 0x00},
	{0x50, 0x10},
	{0x4d, 0x02},
	{0x4e, 0xD8},
	{0x5c, 0x03},
	{0x64, 0x01},
	{0x65, 0xD0},
	{0x75, 0x05},
	{0x7b, 0x01},
	{0x7c, 0x55},
	{0x20, 0x30},
	{0x7e, 0x01},
	{0x1a, 0x01},
	{0x1b, 0x87},
	{0xfe, 0x01},

	{0xfd, 0x01},
	{0x77, 0x00},
	{0x78, 0x07},
	{0xfd, 0x02},
	{0x20, 0x00},
	{0x50, 0x03},
	{0x53, 0x03},
	{0x51, 0x03},
	{0x52, 0x04},
	{0x31, 0x01},
	{0x32, 0x81},
	{0x33, 0x03},
	{0x34, 0x80},
	{0x35, 0x00},
	{0x30, 0x00},//auto blc 0x09
	{0x80, 0x0f},//bad pixel
	{0x81, 0x1b},
	{0x92, 0x20},

	{0xfd, 0x01},
	{0x16, 0x00},
	{0x17, 0x9c},
	{0xfd, 0x01},

	{0x06, 0x05},
	{0x07, 0x00},
	{0x08, 0x02},
	{0x09, 0xD0},

	{0xfd, 0x01},
	{0x02, 0x00},
	{0x03, 0x18},
	{0x04, 0x00},
	{0x05, 0x10},

	{0xfd, 0x01},
	{0x0c, 0x02},
	{0x0d, 0x9A},
	{0xfd, 0x01},
	{0x24, 0x20},
	{0x22, 0x00},
	{0x23, 0x10},
	{0xfd, 0x02},
	{0x70, 0x10},
	{0x60, 0x00},
	{0x61, 0x80},
	{0x62, 0x00},
	{0x63, 0x80},
	{0x64, 0x00},
	{0x65, 0x80},
	{0x66, 0x00},
	{0x67, 0x80},
	{0x68, 0x00},
	{0x69, 0x40},
	{0x6a, 0x00},
	{0x6b, 0x40},
	{0x6c, 0x00},
	{0x6d, 0x40},
	{0x6e, 0x00},
	{0x6f, 0x40},
	/* {0xfd, 0x02}, */
	/* {0x20, 0x01},//color */
	{0xfe, 0x01},
	{0xfe, 0x01},
	{0xfd, 0x01},
	{SENSOR_REG_END, 0x00},
};

/*
 * the order of the sensor_win_sizes is [full_resolution, preview_resolution].
 */
static struct tx_isp_sensor_win_setting sensor_win_sizes[] = {
	/* 1280*960 */
	{
		.width = 1280,
		.height = 720,
		.fps = 25 << 16 | 1, /* 25 fps */
		.mbus_code = V4L2_MBUS_FMT_SRGGB10_1X10,
		.colorspace = V4L2_COLORSPACE_SRGB,
		.regs = sensor_init_regs_1280_720_25fps,
	}
};


static struct regval_list sensor_stream_on[] = {
	{0xfd, 0x01},
	{0x1e, 0x00},
	{0xfe, 0x02},
	{SENSOR_REG_END, 0x00},
};

static struct regval_list sensor_stream_off[] = {
	{0xfd, 0x01},
	{0x1e, 0xff},
	{0xfd, 0x00},
	{0xfe, 0x02},
	{SENSOR_REG_END, 0x00},
};

int sensor_read(struct v4l2_subdev *sd, unsigned short reg, unsigned char *value) {
	struct i2c_client *client = v4l2_get_subdevdata(sd);
	struct i2c_msg msg[2] = {
		[0] = {
			.addr = client->addr,
			.flags = 0,
			.len = 1,
			.buf = &reg,
		},
		[1] = {
			.addr = client->addr,
			.flags = I2C_M_RD,
			.len = 1,
			.buf = value,
		}
	};
	int ret;
	ret = i2c_transfer(client->adapter, msg, 2);
	if (ret > 0)
		ret = 0;

	return ret;

}

int sensor_write(struct v4l2_subdev *sd, unsigned char reg, unsigned char value) {
	struct i2c_client *client = v4l2_get_subdevdata(sd);
	unsigned char buf[2] = {reg, value};
	struct i2c_msg msg = {
		.addr = client->addr,
		.flags = 0,
		.len = 2,
		.buf = buf,
	};
	int ret;
	ret = i2c_transfer(client->adapter, &msg, 1);
	if (ret > 0)
		ret = 0;

	return ret;
}

static int sensor_read_array(struct v4l2_subdev *sd, struct regval_list *vals) {
	int ret;
	unsigned char val;
	while (vals->reg_num != SENSOR_REG_END) {
		if (vals->reg_num == SENSOR_REG_DELAY) {
			if (vals->value >= (1000 / HZ))
				msleep(vals->value);
			else
				mdelay(vals->value);
		} else {
			ret = sensor_read(sd, vals->reg_num, &val);
			if (ret < 0)
				return ret;
		}
		/* printk("read vals->reg_num:0x%02x, vals->value:0x%02x\n",vals->reg_num, val); */

		vals++;
	}

	return 0;
}

static int sensor_write_array(struct v4l2_subdev *sd, struct regval_list *vals) {
	int ret;
	while (vals->reg_num != SENSOR_REG_END) {
		if (vals->reg_num == SENSOR_REG_DELAY) {
			if (vals->value >= (1000 / HZ))
				msleep(vals->value);
			else
				mdelay(vals->value);
		} else {
			ret = sensor_write(sd, vals->reg_num, vals->value);
			if (ret < 0)
				return ret;
		}

		vals++;
	}

	return 0;
}

static int sensor_reset(struct v4l2_subdev *sd, u32 val) {
	return 0;
}

static int sensor_detect(struct v4l2_subdev *sd, unsigned int *ident) {
	int ret;
	unsigned char v;
	unsigned char page = 0;
	sensor_read(sd, 0xfd, &page);
	if (page != 0) {
		sensor_write(sd, 0xfd, 0x0);
		sensor_write(sd, 0xfe, 0x2);
	}

	ret = sensor_read(sd, 0x04, &v);
	/* printk("-----%s: %d ret = %d, v = 0x%02x\n", __func__, __LINE__, ret,v); */
	if (ret < 0)
		return ret;

	if (v != SENSOR_CHIP_ID_H)
		return -ENODEV;
	*ident = v;
	ret = sensor_read(sd, 0x05, &v);
	/* printk("-----%s: %d ret = %d, v = 0x%02x\n", __func__, __LINE__, ret,v); */
	if (ret < 0)
		return ret;

	if (v != SENSOR_CHIP_ID_L)
		return -ENODEV;
	*ident = (*ident << 8) | v;

	return 0;
}

static int sensor_set_integration_time(struct v4l2_subdev *sd, int value) {
	unsigned int expo = value;
	int ret = 0;
	unsigned char page = 0;
	sensor_read(sd, 0xfd, &page);
	if (page != 1) {
		sensor_write(sd, 0xfd, 0x01);
		sensor_write(sd, 0xfe, 0x02);
	}
	ret += sensor_write(sd, 0x0c, (unsigned char) ((expo >> 8) & 0xff));
	ret += sensor_write(sd, 0x0d, (unsigned char) (expo & 0xff));
	sensor_write(sd, 0xfe, 0x02);
	if (ret < 0)
		return ret;

	return 0;
}

static int sensor_set_analog_gain(struct v4l2_subdev *sd, int value) {
	int ret = 0;

	unsigned char page = 0;
	sensor_read(sd, 0xfd, &page);
	if (page != 1) {
		sensor_write(sd, 0xfd, 0x01);
		sensor_write(sd, 0xfe, 0x02);
	}
	/* printk("a gain value=0x%x,final_verfl=0x%x\n",value,final_vrefl); */
	sensor_write(sd, 0x22, (unsigned char) (value >> 8));
	sensor_write(sd, 0x23, (unsigned char) (value & 0xff));
	sensor_write(sd, 0x7f, final_vrefl);
	sensor_write(sd, 0xfe, 0x02);
	if (ret < 0)
		return ret;

	return 0;
}

static int sensor_set_digital_gain(struct v4l2_subdev *sd, int value) {
	/* 0x00 bit[7] if gain > 2X set 0; if gain > 4X set 1 */
	/* int ret = 0; */

	/* ret = sensor_write(sd, 0x3610, (unsigned char)value); */
	/* if (ret < 0) */
	/* 	return ret; */
	return 0;
}

static int sensor_get_black_pedestal(struct v4l2_subdev *sd, int value) {
	return 0;
}

static int sensor_init(struct v4l2_subdev *sd, u32 enable) {
	struct tx_isp_sensor *sensor = (container_of(sd, struct tx_isp_sensor, sd));
	struct tx_isp_notify_argument arg;
	struct tx_isp_sensor_win_setting *wsize = &sensor_win_sizes[0];
	int ret = 0;

	if (!enable)
		return ISP_SUCCESS;

	sensor->video.mbus.width = wsize->width;
	sensor->video.mbus.height = wsize->height;
	sensor->video.mbus.code = wsize->mbus_code;
	sensor->video.mbus.field = V4L2_FIELD_NONE;
	sensor->video.mbus.colorspace = wsize->colorspace;
	sensor->video.fps = wsize->fps;
	ret = sensor_write_array(sd, wsize->regs);
	if (ret)
		return ret;
	arg.value = (int) &sensor->video;
	sd->v4l2_dev->notify(sd, TX_ISP_NOTIFY_SYNC_VIDEO_IN, &arg);
	sensor->priv = wsize;
	return 0;
}

static int sensor_s_stream(struct v4l2_subdev *sd, int enable) {
	int ret = 0;

	if (enable) {
		ret = sensor_write_array(sd, sensor_stream_on);
		printk("%s stream on\n", SENSOR_NAME));
	} else {
		ret = sensor_write_array(sd, sensor_stream_off);
		printk("%s stream off\n", SENSOR_NAME);
	}
	return ret;
}

static int sensor_g_parm(struct v4l2_subdev *sd, struct v4l2_streamparm *parms) {
	return 0;
}

static int sensor_s_parm(struct v4l2_subdev *sd, struct v4l2_streamparm *parms) {
	return 0;
}


static int sensor_set_fps(struct tx_isp_sensor *sensor, int fps) {
	struct tx_isp_notify_argument arg;
	struct v4l2_subdev *sd = &sensor->sd;
	unsigned int pclk = SENSOR_SUPPORT_PCLK;
	unsigned short hts;
	unsigned short vts_pre;
	unsigned short vsize_eff;
	unsigned short vblank;
	unsigned short vts = 0;
	unsigned char tmp, tmp1, tmp2;
	unsigned int newformat = 0; //the format is 24.8
	int ret = 0;
	unsigned char page = 0;
	/* fps=PCLK*1000000/(vts*hts) */

	/* the format of fps is 16/16. for example 25 << 16 | 2, the value is 25/2 fps. */
	newformat = (((fps >> 16) / (fps & 0xffff)) << 8) + ((((fps >> 16) % (fps & 0xffff)) << 8) / (fps & 0xffff));
	if (newformat > (SENSOR_OUTPUT_MAX_FPS << 8) || newformat < (SENSOR_OUTPUT_MIN_FPS << 8))
		return -1;

	sensor_read(sd, 0xfd, &page);
	if (page != 1) {
		sensor_write(sd, 0xfd, 0x01);

	}
	ret += sensor_read(sd, 0x25, &tmp);
	hts = tmp;
	ret += sensor_read(sd, 0x26, &tmp);
	sensor_write(sd, 0xfe, 0x02);
	if (ret < 0)
		return -1;
	hts = (hts << 8) + tmp;

	ret += sensor_read(sd, 0x28, &tmp);
	vts_pre = tmp;
	ret += sensor_read(sd, 0x27, &tmp);
	sensor_write(sd, 0xfe, 0x02);
	if (ret < 0)
		return -1;
	vts_pre = (vts_pre << 8) + tmp;

	ret += sensor_read(sd, 0x17, &tmp);
	vblank = tmp;
	ret += sensor_read(sd, 0x16, &tmp);
	sensor_write(sd, 0xfe, 0x02);
	if (ret < 0)
		return -1;
	vblank = (vblank << 8) + tmp;

	vsize_eff = vts_pre - vblank;
	/*vts = (pclk << 4) / (hts * (newformat >> 4));*/
	vts = pclk * (fps & 0xffff) / hts / ((fps & 0xffff0000) >> 16);
	ret = sensor_write(sd, 0x17, (unsigned char) ((vts - vsize_eff) & 0xff));
	ret += sensor_write(sd, 0x16, (unsigned char) ((vts - vsize_eff) >> 8));
	sensor_write(sd, 0xfe, 0x02);
	ret += sensor_read(sd, 0x17, &tmp1);
	ret += sensor_read(sd, 0x16, &tmp2);

	/* printk("hts======0x%x,vts ========0x%x,vsize_eff========0x%x vblank====0x%x   read 1716 0x%x\n",hts,vts,vsize_eff,vblank,tmp1<<8|tmp2); */
	if (ret < 0)
		return -1;
	sensor->video.fps = fps;
	sensor->video.attr->max_integration_time_native = vts - 5;
	sensor->video.attr->integration_time_limit = vts - 5;
	sensor->video.attr->total_height = vts;
	sensor->video.attr->max_integration_time = vts - 5;
	arg.value = (int) &sensor->video;
	sd->v4l2_dev->notify(sd, TX_ISP_NOTIFY_SYNC_VIDEO_IN, &arg);
	return 0;
}

static int sensor_set_mode(struct tx_isp_sensor *sensor, int value) {
	struct tx_isp_notify_argument arg;
	struct v4l2_subdev *sd = &sensor->sd;
	struct tx_isp_sensor_win_setting *wsize = NULL;
	int ret = ISP_SUCCESS;
	/* printk("functiong:%s, line:%d\n", __func__, __LINE__); */
	if (value == TX_ISP_SENSOR_FULL_RES_MAX_FPS) {
		wsize = &sensor_win_sizes[0];
	} else if (value == TX_ISP_SENSOR_PREVIEW_RES_MAX_FPS) {
		wsize = &sensor_win_sizes[0];
	}
	if (wsize) {
		sensor->video.mbus.width = wsize->width;
		sensor->video.mbus.height = wsize->height;
		sensor->video.mbus.code = wsize->mbus_code;
		sensor->video.mbus.field = V4L2_FIELD_NONE;
		sensor->video.mbus.colorspace = wsize->colorspace;
		if (sensor->priv != wsize) {
			ret = sensor_write_array(sd, wsize->regs);
			if (!ret)
				sensor->priv = wsize;
		}
		sensor->video.fps = wsize->fps;
		arg.value = (int) &sensor->video;
		sd->v4l2_dev->notify(sd, TX_ISP_NOTIFY_SYNC_VIDEO_IN, &arg);
	}
	return ret;
}

static int sensor_g_chip_ident(struct v4l2_subdev *sd,
			       struct v4l2_dbg_chip_ident *chip) {
	struct i2c_client *client = v4l2_get_subdevdata(sd);
	unsigned int ident = 0;
	int ret = ISP_SUCCESS;

	if (reset_gpio != -1) {
		ret = gpio_request(reset_gpio, "sensor_reset");
		if (!ret) {
			gpio_direction_output(reset_gpio, 1);
			msleep(10);
			gpio_direction_output(reset_gpio, 0);
			msleep(10);
			gpio_direction_output(reset_gpio, 1);
			msleep(10);
		} else {
			printk("gpio request fail %d\n", reset_gpio);
		}
	}
	if (pwdn_gpio != -1) {
		ret = gpio_request(pwdn_gpio, "sensor_pwdn");
		if (!ret) {
			gpio_direction_output(pwdn_gpio, 1);
			msleep(50);
			gpio_direction_output(pwdn_gpio, 0);
			msleep(10);
		} else {
			printk("gpio request fail %d\n", pwdn_gpio);
		}
	}
	ret = sensor_detect(sd, &ident);
	if (ret) {
		v4l_err(client, "chip found @ 0x%x (%s) is not an %s chip.\n",
			client->addr, client->adapter->name, SENSOR_NAME);
		return ret;
	}
	v4l_info(client, "%s chip found @ 0x%02x (%s)\n",
		 SENSOR_NAME, client->addr, client->adapter->name);
	return v4l2_chip_ident_i2c_client(client, chip, ident, 0);
}

static int sensor_s_power(struct v4l2_subdev *sd, int on) {
	return 0;
}

static long sensor_ops_private_ioctl(struct tx_isp_sensor *sensor, struct isp_private_ioctl *ctrl) {
	struct v4l2_subdev *sd = &sensor->sd;
	long ret = 0;
	switch (ctrl->cmd) {
		case TX_ISP_PRIVATE_IOCTL_SENSOR_INT_TIME:
			ret = sensor_set_integration_time(sd, ctrl->value);
			break;
		case TX_ISP_PRIVATE_IOCTL_SENSOR_AGAIN:
			ret = sensor_set_analog_gain(sd, ctrl->value);
			break;
		case TX_ISP_PRIVATE_IOCTL_SENSOR_DGAIN:
			ret = sensor_set_digital_gain(sd, ctrl->value);
			break;
		case TX_ISP_PRIVATE_IOCTL_SENSOR_BLACK_LEVEL:
			ret = sensor_get_black_pedestal(sd, ctrl->value);
			break;
		case TX_ISP_PRIVATE_IOCTL_SENSOR_RESIZE:
			ret = sensor_set_mode(sensor, ctrl->value);
			break;
		case TX_ISP_PRIVATE_IOCTL_SUBDEV_PREPARE_CHANGE:
			//	ret = sensor_write_array(sd, sensor_stream_off);
			break;
		case TX_ISP_PRIVATE_IOCTL_SUBDEV_FINISH_CHANGE:
			//	ret = sensor_write_array(sd, sensor_stream_on);
			break;
		case TX_ISP_PRIVATE_IOCTL_SENSOR_FPS:
			ret = sensor_set_fps(sensor, ctrl->value);
			break;
		default:
			printk("do not support ctrl->cmd ====%d\n", ctrl->cmd);
			break;
	}
	return 0;
}

static long sensor_ops_ioctl(struct v4l2_subdev *sd, unsigned int cmd, void *arg) {
	struct tx_isp_sensor *sensor = container_of(sd, struct tx_isp_sensor, sd);
	int ret;
	switch (cmd) {
		case VIDIOC_ISP_PRIVATE_IOCTL:
			ret = sensor_ops_private_ioctl(sensor, arg);
			break;
		default:
			return -1;
			break;
	}
	return 0;
}

#ifdef CONFIG_VIDEO_ADV_DEBUG
static int sensor_g_register(struct v4l2_subdev *sd, struct v4l2_dbg_register *reg)
{
	struct i2c_client *client = v4l2_get_subdevdata(sd);
	unsigned char val = 0;
	int ret;

	if (!v4l2_chip_match_i2c_client(client, &reg->match))
		return -EINVAL;
	if (!capable(CAP_SYS_ADMIN))
		return -EPERM;
	ret = sensor_read(sd, reg->reg & 0xffff, &val);
	reg->val = val;
	reg->size = 2;
	return ret;
}

static int sensor_s_register(struct v4l2_subdev *sd, const struct v4l2_dbg_register *reg)
{
	struct i2c_client *client = v4l2_get_subdevdata(sd);

	if (!v4l2_chip_match_i2c_client(client, &reg->match))
		return -EINVAL;
	if (!capable(CAP_SYS_ADMIN))
		return -EPERM;
	sensor_write(sd, reg->reg & 0xffff, reg->val & 0xff);
	return 0;
}
#endif

static const struct v4l2_subdev_core_ops sensor_core_ops = {
	.g_chip_ident = sensor_g_chip_ident,
	.reset = sensor_reset,
	.init = sensor_init,
	.s_power = sensor_s_power,
	.ioctl = sensor_ops_ioctl,
#ifdef CONFIG_VIDEO_ADV_DEBUG
	.g_register = sensor_g_register,
	.s_register = sensor_s_register,
#endif
};

static const struct v4l2_subdev_video_ops sensor_video_ops = {
	.s_stream = sensor_s_stream,
	.s_parm = sensor_s_parm,
	.g_parm = sensor_g_parm,
};

static const struct v4l2_subdev_ops sensor_ops = {
	.core = &sensor_core_ops,
	.video = &sensor_video_ops,
};

static int sensor_probe(struct i2c_client *client,
			const struct i2c_device_id *id) {
	struct v4l2_subdev *sd;
	struct tx_isp_video_in *video;
	struct tx_isp_sensor *sensor;
	struct tx_isp_sensor_win_setting *wsize = &sensor_win_sizes[0];
	int ret;

	sensor = (struct tx_isp_sensor *) kzalloc(sizeof(*sensor), GFP_KERNEL);
	if (!sensor) {
		printk("Failed to allocate sensor subdev.\n");
		return -ENOMEM;
	}
	memset(sensor, 0, sizeof(*sensor));
	/* request mclk of sensor */
	sensor->mclk = clk_get(NULL, "cgu_cim");
	if (IS_ERR(sensor->mclk)) {
		printk("Cannot get sensor input clock cgu_cim\n");
		goto err_get_mclk;
	}
	clk_set_rate(sensor->mclk, 24000000);
	clk_enable(sensor->mclk);

	ret = set_sensor_gpio_function(sensor_gpio_func);
	if (ret < 0)
		goto err_set_sensor_gpio;

	sensor_attr.dvp.gpio = sensor_gpio_func;
	/*
	  convert sensor-gain into isp-gain,
	*/
	sensor_attr.max_again = sensor_attr.max_again;
	sensor_attr.max_dgain = sensor_attr.max_dgain;
	sd = &sensor->sd;
	video = &sensor->video;
	sensor->video.attr = &sensor_attr;
	sensor->video.vi_max_width = wsize->width;
	sensor->video.vi_max_height = wsize->height;
	v4l2_i2c_subdev_init(sd, client, &sensor_ops);
	v4l2_set_subdev_hostdata(sd, sensor);

	return 0;
err_set_sensor_gpio:
	clk_disable(sensor->mclk);
	clk_put(sensor->mclk);
err_get_mclk:
	kfree(sensor);

	return -1;
}

static int sensor_remove(struct i2c_client *client) {
	struct v4l2_subdev *sd = i2c_get_clientdata(client);
	struct tx_isp_sensor *sensor = v4l2_get_subdev_hostdata(sd);

	if (reset_gpio != -1)
		gpio_free(reset_gpio);
	if (pwdn_gpio != -1)
		gpio_free(pwdn_gpio);

	clk_disable(sensor->mclk);
	clk_put(sensor->mclk);

	v4l2_device_unregister_subdev(sd);
	kfree(sensor);
	return 0;
}

static const struct i2c_device_id sensor_id[] = {
	{SENSOR_NAME, 0},
	{}
};
MODULE_DEVICE_TABLE(i2c, sensor_id);

static struct i2c_driver sensor_driver = {
	.driver = {
		.owner = THIS_MODULE,
		.name = SENSOR_NAME,
	},
	.probe = sensor_probe,
	.remove = sensor_remove,
	.id_table = sensor_id,
};

static __init int init_sensor(void) {
	sensor_common_init(&sensor_info);
	return i2c_add_driver(&sensor_driver);
}

static __exit void exit_sensor(void) {
	sensor_common_exit();
	i2c_del_driver(&sensor_driver);
}

module_init(init_sensor);
module_exit(exit_sensor);

MODULE_DESCRIPTION("A low-level driver for "SENSOR_NAME" sensor");
MODULE_LICENSE("GPL");
