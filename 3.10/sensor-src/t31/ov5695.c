// SPDX-License-Identifier: GPL-2.0+
/*
 * ov5695.c
 * Copyright (C) 2012 Ingenic Semiconductor Co., Ltd.
 */

#include <linux/init.h>
#include <linux/module.h>
#include <linux/slab.h>
#include <linux/i2c.h>
#include <linux/delay.h>
#include <linux/gpio.h>
#include <linux/clk.h>
#include <linux/proc_fs.h>
#include <tx-isp-common.h>
#include <sensor-common.h>
#include <sensor-info.h>

#define SENSOR_NAME "ov5695"
#define SENSOR_BUS_TYPE TX_SENSOR_CONTROL_INTERFACE_I2C
#define SENSOR_I2C_ADDRESS 0x36
#define SENSOR_MAX_WIDTH 1920
#define SENSOR_MAX_HEIGHT 1080
#define SENSOR_CHIP_ID 0x5695
#define SENSOR_CHIP_ID_H (0x56)
#define SENSOR_CHIP_ID_L (0x95)
#define SENSOR_REG_END 0xffff
#define SENSOR_REG_DELAY 0xfffe
#define SENSOR_OUTPUT_MAX_FPS 60
#define SENSOR_OUTPUT_MIN_FPS 5
#define SENSOR_SUPPORT_SCLK (45000000)
#define SENSOR_VERSION "H20211109a"

static int reset_gpio = GPIO_PA(18);
module_param(reset_gpio, int, S_IRUGO);
MODULE_PARM_DESC(reset_gpio, "Reset GPIO NUM");

static int pwdn_gpio = -1;
module_param(pwdn_gpio, int, S_IRUGO);
MODULE_PARM_DESC(pwdn_gpio, "Power down GPIO NUM");

static int data_interface = TX_SENSOR_DATA_INTERFACE_MIPI;
module_param(data_interface, int, S_IRUGO);
MODULE_PARM_DESC(data_interface, "Sensor Date interface");

static int sensor_resolution = TX_SENSOR_RES_200;
module_param(sensor_resolution, int, S_IRUGO);
MODULE_PARM_DESC(sensor_resolution, "Sensor Resolution");

static int shvflip = 0;
module_param(shvflip, int, S_IRUGO);
MODULE_PARM_DESC(shvflip, "Sensor HV Flip Enable interface");

static struct sensor_info sensor_info = {
	.name = SENSOR_NAME,
	.chip_id = SENSOR_CHIP_ID,
	.version = SENSOR_VERSION,
	.min_fps = SENSOR_OUTPUT_MIN_FPS,
	.max_fps = SENSOR_OUTPUT_MAX_FPS,
	.chip_i2c_addr = SENSOR_I2C_ADDRESS,
	.width = SENSOR_MAX_WIDTH,
	.height = SENSOR_MAX_HEIGHT,
};

struct regval_list {
    uint16_t reg_num;
    unsigned char value;
};

struct again_lut {
	unsigned int value;
	unsigned int gain;
};

struct again_lut sensor_again_lut[] = {
	{0x10, 0},
	{0x11, 5776},
	{0x12, 11136},
	{0x13, 16287},
	{0x14, 21097},
	{0x15, 25746},
	{0x16, 30109},
	{0x17, 34345},
	{0x18, 38336},
	{0x19, 42226},
	{0x1a, 45904},
	{0x1b, 49500},
	{0x1c, 52910},
	{0x1d, 56254},
	{0x1e, 59433},
	{0x1f, 62558},
	{0x20, 65536},
	{0x21, 71267},
	{0x22, 76672},
	{0x23, 81784},
	{0x24, 86633},
	{0x25, 91246},
	{0x26, 95645},
	{0x27, 99848},
	{0x28, 103872},
	{0x29, 107731},
	{0x2a, 111440},
	{0x2b, 115008},
	{0x2c, 118446},
	{0x2d, 121764},
	{0x2e, 124969},
	{0x2f, 128070},
	{0x30, 131072},
	{0x31, 136803},
	{0x32, 142208},
	{0x33, 147320},
	{0x34, 152169},
	{0x35, 156782},
	{0x36, 161181},
	{0x37, 165384},
	{0x38, 169408},
	{0x39, 173267},
	{0x3a, 176976},
	{0x3b, 180544},
	{0x3c, 183982},
	{0x3d, 187300},
	{0x3e, 190505},
	{0x3f, 193606},
	{0x40, 196608},
	{0x41, 202339},
	{0x42, 207744},
	{0x43, 212856},
	{0x44, 217705},
	{0x45, 222318},
	{0x46, 226717},
	{0x47, 230920},
	{0x48, 234944},
	{0x49, 238803},
	{0x4a, 242512},
	{0x4b, 246080},
	{0x4c, 249518},
	{0x4d, 252836},
	{0x4e, 256041},
	{0x4f, 259142},
};

struct tx_isp_sensor_attribute sensor_attr;

unsigned int sensor_alloc_again(unsigned int isp_gain, unsigned char shift, unsigned int *sensor_again)
{
	struct again_lut *lut = sensor_again_lut;
	while (lut->gain <= sensor_attr.max_again) {
		if (isp_gain == 0) {
			*sensor_again = lut->value;
			return 0;
		}
                else if (isp_gain < lut->gain) {
			*sensor_again = (lut - 1)->value;
			return (lut - 1)->gain;
		}
                else {
			if ((lut->gain == sensor_attr.max_again) && (isp_gain >= lut->gain)) {
				*sensor_again = lut->value;
				return lut->gain;
			}
		}

		lut++;
	}

	return isp_gain;
}

unsigned int sensor_alloc_dgain(unsigned int isp_gain, unsigned char shift, unsigned int *sensor_dgain)
{
	return 0;
}

struct tx_isp_sensor_attribute sensor_attr={
	.name = SENSOR_NAME,
	.chip_id = SENSOR_CHIP_ID,
	.cbus_type = SENSOR_BUS_TYPE,
	.cbus_mask = V4L2_SBUS_MASK_SAMPLE_8BITS | V4L2_SBUS_MASK_ADDR_16BITS,
	.cbus_device = SENSOR_I2C_ADDRESS,
	.dbus_type = TX_SENSOR_DATA_INTERFACE_MIPI,
	.mipi = {
		.mode = SENSOR_MIPI_OTHER_MODE,
		.clk = 450,
		.lans = 2,
		.settle_time_apative_en = 1,
		.mipi_sc.sensor_csi_fmt = TX_SENSOR_RAW10,
		.mipi_sc.hcrop_diff_en = 0,
		.mipi_sc.mipi_vcomp_en = 0,
		.mipi_sc.mipi_hcomp_en = 0,
		.mipi_sc.line_sync_mode = 0,
		.mipi_sc.work_start_flag = 0,
		.image_twidth = 1920,
		.image_theight = 1080,
		.mipi_sc.mipi_crop_start0x = 0,
		.mipi_sc.mipi_crop_start0y = 0,
		.mipi_sc.mipi_crop_start1x = 0,
		.mipi_sc.mipi_crop_start1y = 0,
		.mipi_sc.mipi_crop_start2x = 0,
		.mipi_sc.mipi_crop_start2y = 0,
		.mipi_sc.mipi_crop_start3x = 0,
		.mipi_sc.mipi_crop_start3y = 0,
		.mipi_sc.data_type_en = 0,
		.mipi_sc.data_type_value = RAW10,
		.mipi_sc.del_start = 0,
		.mipi_sc.sensor_frame_mode = TX_SENSOR_DEFAULT_FRAME_MODE,
		.mipi_sc.sensor_fid_mode = 0,
		.mipi_sc.sensor_mode = TX_SENSOR_DEFAULT_MODE,
	},
	.data_type = TX_SENSOR_DATA_TYPE_LINEAR,
	.max_again = 259142,
	.max_dgain = 0,
	.min_integration_time = 2,
	.min_integration_time_native = 2,
	.max_integration_time_native = 0x45c - 4,
	.integration_time_limit = 0x45c - 4,
	.total_width = 0x2a0,
	.total_height = 0x45c,
	.max_integration_time = 0x45c - 4,
	.one_line_expr_in_us = 14,
	.integration_time_apply_delay = 2,
	.again_apply_delay = 2,
	.dgain_apply_delay = 0,
	.sensor_ctrl.alloc_again = sensor_alloc_again,
	.sensor_ctrl.alloc_dgain = sensor_alloc_dgain,
};

static struct regval_list sensor_init_regs_1920_1080_60fps_mipi[] = {
	{0x0103,0x01},
	{0x0100,0x00},
	{0x0300,0x04},
	{0x0301,0x00},
	{0x0302,0x69},
	{0x0303,0x00},
	{0x0304,0x00},
	{0x0305,0x01},
	{0x0307,0x00},
	{0x030b,0x00},
	{0x030c,0x00},
	{0x030d,0x1e},
	{0x030e,0x04},
	{0x030f,0x03},
	{0x0312,0x01},
	{0x3000,0x00},
	{0x3002,0x21},
	{0x3016,0x32},
	{0x3022,0x51},
	{0x3106,0x15},
	{0x3107,0x01},
	{0x3108,0x05},
	{0x3500,0x00},
	{0x3501,0x45},
	{0x3502,0x00},
	{0x3503,0x08},
	{0x3504,0x03},
	{0x3505,0x8c},
	{0x3507,0x03},
	{0x3508,0x00},
	{0x3509,0x10},
	{0x350c,0x00},
	{0x350d,0x80},
	{0x3510,0x00},
	{0x3511,0x02},
	{0x3512,0x00},
	{0x3601,0x55},
	{0x3602,0x58},
	{0x3611,0x58},
	{0x3614,0x30},
	{0x3615,0x77},
	{0x3621,0x08},
	{0x3624,0x40},
	{0x3633,0x0c},
	{0x3634,0x0c},
	{0x3635,0x0c},
	{0x3636,0x0c},
	{0x3638,0x00},
	{0x3639,0x00},
	{0x363a,0x00},
	{0x363b,0x00},
	{0x363c,0xff},
	{0x363d,0xfa},
	{0x3650,0x44},
	{0x3651,0x44},
	{0x3652,0x44},
	{0x3653,0x44},
	{0x3654,0x44},
	{0x3655,0x44},
	{0x3656,0x44},
	{0x3657,0x44},
	{0x3660,0x00},
	{0x3661,0x00},
	{0x3662,0x00},
	{0x366a,0x00},
	{0x366e,0x18},
	{0x3673,0x04},
	{0x3700,0x14},
	{0x3703,0x0c},
	{0x3706,0x24},
	{0x3714,0x23},
	{0x3715,0x01},
	{0x3716,0x00},
	{0x3717,0x02},
	{0x3733,0x10},
	{0x3734,0x40},
	{0x373f,0xa0},
	{0x3765,0x20},
	{0x37a1,0x1d},
	{0x37a8,0x26},
	{0x37ab,0x14},
	{0x37c2,0x04},
	{0x37c3,0xf0},
	{0x37cb,0x09},
	{0x37cc,0x13},
	{0x37cd,0x1f},
	{0x37ce,0x1f},
	{0x3800,0x01},
	{0x3801,0x50},
	{0x3802,0x01},
	{0x3803,0xb8},
	{0x3804,0x08},
	{0x3805,0xef},
	{0x3806,0x05},
	{0x3807,0xf7},
	{0x3808,0x07},/*1920*/
	{0x3809,0x80},//
	{0x380a,0x04},/*1080*/
	{0x380b,0x38},//
	{0x380c,0x02},/* hts */
	{0x380d,0xa0},//
	{0x380e,0x04},/* vts */
	{0x380f,0x5c},//
	{0x3810,0x00},
	{0x3811,0x10},
	{0x3812,0x00},
	{0x3813,0x04},
	{0x3814,0x01},
	{0x3815,0x01},
	{0x3816,0x01},
	{0x3817,0x01},
	{0x3818,0x00},
	{0x3819,0x00},
	{0x381a,0x00},
	{0x381b,0x01},
	{0x3820,0x88},
	{0x3821,0x00},
	{0x3c80,0x08},
	{0x3c82,0x00},
	{0x3c83,0x00},
	{0x3c88,0x00},
	{0x3d85,0x14},
	{0x3f02,0x08},
	{0x3f03,0x10},
	{0x4008,0x04},
	{0x4009,0x13},
	{0x404e,0x20},
	{0x4501,0x00},
	{0x4502,0x10},
	{0x4800,0x00},
	{0x481f,0x2a},
	{0x4837,0x13},
	{0x5000,0x13},
	{0x5780,0x3e},
	{0x5781,0x0f},
	{0x5782,0x44},
	{0x5783,0x02},
	{0x5784,0x01},
	{0x5785,0x01},
	{0x5786,0x00},
	{0x5787,0x04},
	{0x5788,0x02},
	{0x5789,0x0f},
	{0x578a,0xfd},
	{0x578b,0xf5},
	{0x578c,0xf5},
	{0x578d,0x03},
	{0x578e,0x08},
	{0x578f,0x0c},
	{0x5790,0x08},
	{0x5791,0x06},
	{0x5792,0x00},
	{0x5793,0x52},
	{0x5794,0xa3},
	{0x5b00,0x00},
	{0x5b01,0x1c},
	{0x5b02,0x00},
	{0x5b03,0x7f},
	{0x5b05,0x6c},
	{0x5e10,0xfc},
	{0x4010,0xf1},
	{0x3503,0x08},
	{0x3505,0x8c},
	{0x3507,0x03},
	{0x3508,0x00},
	{0x3509,0xf8},
	{0x0100,0x01},

	{SENSOR_REG_END, 0x00},
};

static struct regval_list sensor_init_regs_1280_720_60fps_mipi[] = {
	{0x0103,0x01},
	{0x0100,0x00},
	{0x0300,0x04},
	{0x0301,0x00},
	{0x0302,0x69},
	{0x0303,0x00},
	{0x0304,0x00},
	{0x0305,0x01},
	{0x0307,0x00},
	{0x030b,0x00},
	{0x030c,0x00},
	{0x030d,0x1e},
	{0x030e,0x04},
	{0x030f,0x03},
	{0x0312,0x01},
	{0x3000,0x00},
	{0x3002,0x21},
	{0x3016,0x32},
	{0x3022,0x51},
	{0x3106,0x15},
	{0x3107,0x01},
	{0x3108,0x05},
	{0x3500,0x00},
	{0x3501,0x45},
	{0x3502,0x00},
	{0x3503,0x08},
	{0x3504,0x03},
	{0x3505,0x8c},
	{0x3507,0x03},
	{0x3508,0x00},
	{0x3509,0x10},
	{0x350c,0x00},
	{0x350d,0x80},
	{0x3510,0x00},
	{0x3511,0x02},
	{0x3512,0x00},
	{0x3601,0x55},
	{0x3602,0x58},
	{0x3611,0x58},
	{0x3614,0x30},
	{0x3615,0x77},
	{0x3621,0x08},
	{0x3624,0x40},
	{0x3633,0x0c},
	{0x3634,0x0c},
	{0x3635,0x0c},
	{0x3636,0x0c},
	{0x3638,0x00},
	{0x3639,0x00},
	{0x363a,0x00},
	{0x363b,0x00},
	{0x363c,0xff},
	{0x363d,0xfa},
	{0x3650,0x44},
	{0x3651,0x44},
	{0x3652,0x44},
	{0x3653,0x44},
	{0x3654,0x44},
	{0x3655,0x44},
	{0x3656,0x44},
	{0x3657,0x44},
	{0x3660,0x00},
	{0x3661,0x00},
	{0x3662,0x00},
	{0x366a,0x00},
	{0x366e,0x18},
	{0x3673,0x04},
	{0x3700,0x14},
	{0x3703,0x0c},
	{0x3706,0x24},
	{0x3714,0x23},
	{0x3715,0x01},
	{0x3716,0x00},
	{0x3717,0x02},
	{0x3733,0x10},
	{0x3734,0x40},
	{0x373f,0xa0},
	{0x3765,0x20},
	{0x37a1,0x1d},
	{0x37a8,0x26},
	{0x37ab,0x14},
	{0x37c2,0x04},
	{0x37c3,0xf0},
	{0x37cb,0x09},
	{0x37cc,0x13},
	{0x37cd,0x1f},
	{0x37ce,0x1f},
	{0x3800,0x01},
	{0x3801,0x50},
	{0x3802,0x01},
	{0x3803,0xb8},
	{0x3804,0x08},
	{0x3805,0xef},
	{0x3806,0x05},
	{0x3807,0xf7},
	{0x3808,0x05},/* 1280 */
	{0x3809,0x00},//
	{0x380a,0x02},/* 720 */
	{0x380b,0xd0},//
	{0x380c,0x02},/* hts */
	{0x380d,0xa0},//
	{0x380e,0x04},/* vts */
	{0x380f,0x5c},//
	{0x3810,0x01},
	{0x3811,0x50},
	{0x3812,0x00},
	{0x3813,0xb8},
	{0x3814,0x01},
	{0x3815,0x01},
	{0x3816,0x01},
	{0x3817,0x01},
	{0x3818,0x00},
	{0x3819,0x00},
	{0x381a,0x00},
	{0x381b,0x01},
	{0x3820,0x88},
	{0x3821,0x00},
	{0x3c80,0x08},
	{0x3c82,0x00},
	{0x3c83,0x00},
	{0x3c88,0x00},
	{0x3d85,0x14},
	{0x3f02,0x08},
	{0x3f03,0x10},
	{0x4008,0x04},
	{0x4009,0x13},
	{0x404e,0x20},
	{0x4501,0x00},
	{0x4502,0x10},
	{0x4800,0x00},
	{0x481f,0x2a},
	{0x4837,0x13},
	{0x5000,0x13},
	{0x5780,0x3e},
	{0x5781,0x0f},
	{0x5782,0x44},
	{0x5783,0x02},
	{0x5784,0x01},
	{0x5785,0x01},
	{0x5786,0x00},
	{0x5787,0x04},
	{0x5788,0x02},
	{0x5789,0x0f},
	{0x578a,0xfd},
	{0x578b,0xf5},
	{0x578c,0xf5},
	{0x578d,0x03},
	{0x578e,0x08},
	{0x578f,0x0c},
	{0x5790,0x08},
	{0x5791,0x06},
	{0x5792,0x00},
	{0x5793,0x52},
	{0x5794,0xa3},
	{0x5b00,0x00},
	{0x5b01,0x1c},
	{0x5b02,0x00},
	{0x5b03,0x7f},
	{0x5b05,0x6c},
	{0x5e10,0xfc},
	{0x4010,0xf1},
	{0x3503,0x08},
	{0x3505,0x8c},
	{0x3507,0x03},
	{0x3508,0x00},
	{0x3509,0xf8},
	{0x0100,0x01},

	{SENSOR_REG_END, 0x00},
};

static struct regval_list sensor_init_regs_640_480_60fps_mipi[] = {
	{0x0103,0x01},
	{0x0100,0x00},
	{0x0300,0x04},
	{0x0301,0x00},
	{0x0302,0x69},
	{0x0303,0x00},
	{0x0304,0x00},
	{0x0305,0x01},
	{0x0307,0x00},
	{0x030b,0x00},
	{0x030c,0x00},
	{0x030d,0x1e},
	{0x030e,0x04},
	{0x030f,0x03},
	{0x0312,0x01},
	{0x3000,0x00},
	{0x3002,0x21},
	{0x3016,0x32},
	{0x3022,0x51},
	{0x3106,0x15},
	{0x3107,0x01},
	{0x3108,0x05},
	{0x3500,0x00},
	{0x3501,0x45},
	{0x3502,0x00},
	{0x3503,0x08},
	{0x3504,0x03},
	{0x3505,0x8c},
	{0x3507,0x03},
	{0x3508,0x00},
	{0x3509,0x10},
	{0x350c,0x00},
	{0x350d,0x80},
	{0x3510,0x00},
	{0x3511,0x02},
	{0x3512,0x00},
	{0x3601,0x55},
	{0x3602,0x58},
	{0x3611,0x58},
	{0x3614,0x30},
	{0x3615,0x77},
	{0x3621,0x08},
	{0x3624,0x40},
	{0x3633,0x0c},
	{0x3634,0x0c},
	{0x3635,0x0c},
	{0x3636,0x0c},
	{0x3638,0x00},
	{0x3639,0x00},
	{0x363a,0x00},
	{0x363b,0x00},
	{0x363c,0xff},
	{0x363d,0xfa},
	{0x3650,0x44},
	{0x3651,0x44},
	{0x3652,0x44},
	{0x3653,0x44},
	{0x3654,0x44},
	{0x3655,0x44},
	{0x3656,0x44},
	{0x3657,0x44},
	{0x3660,0x00},
	{0x3661,0x00},
	{0x3662,0x00},
	{0x366a,0x00},
	{0x366e,0x18},
	{0x3673,0x04},
	{0x3700,0x14},
	{0x3703,0x0c},
	{0x3706,0x24},
	{0x3714,0x23},
	{0x3715,0x01},
	{0x3716,0x00},
	{0x3717,0x02},
	{0x3733,0x10},
	{0x3734,0x40},
	{0x373f,0xa0},
	{0x3765,0x20},
	{0x37a1,0x1d},
	{0x37a8,0x26},
	{0x37ab,0x14},
	{0x37c2,0x04},
	{0x37c3,0xf0},
	{0x37cb,0x09},
	{0x37cc,0x13},
	{0x37cd,0x1f},
	{0x37ce,0x1f},
	{0x3800,0x01},
	{0x3801,0x50},
	{0x3802,0x01},
	{0x3803,0xb8},
	{0x3804,0x08},
	{0x3805,0xef},
	{0x3806,0x05},
	{0x3807,0xf7},
	{0x3808,0x02},/*640*/
	{0x3809,0x80},//
	{0x380a,0x01},/*480*/
	{0x380b,0xe0},//
	{0x380c,0x02},/* hts */
	{0x380d,0xa0},//
	{0x380e,0x04},/* vts */
	{0x380f,0x5c},//
	{0x3810,0x02},
	{0x3811,0x90},
	{0x3812,0x01},
	{0x3813,0x30},
	{0x3814,0x01},
	{0x3815,0x01},
	{0x3816,0x01},
	{0x3817,0x01},
	{0x3818,0x00},
	{0x3819,0x00},
	{0x381a,0x00},
	{0x381b,0x01},
	{0x3820,0x88},
	{0x3821,0x00},
	{0x3c80,0x08},
	{0x3c82,0x00},
	{0x3c83,0x00},
	{0x3c88,0x00},
	{0x3d85,0x14},
	{0x3f02,0x08},
	{0x3f03,0x10},
	{0x4008,0x04},
	{0x4009,0x13},
	{0x404e,0x20},
	{0x4501,0x00},
	{0x4502,0x10},
	{0x4800,0x00},
	{0x481f,0x2a},
	{0x4837,0x13},
	{0x5000,0x13},
	{0x5780,0x3e},
	{0x5781,0x0f},
	{0x5782,0x44},
	{0x5783,0x02},
	{0x5784,0x01},
	{0x5785,0x01},
	{0x5786,0x00},
	{0x5787,0x04},
	{0x5788,0x02},
	{0x5789,0x0f},
	{0x578a,0xfd},
	{0x578b,0xf5},
	{0x578c,0xf5},
	{0x578d,0x03},
	{0x578e,0x08},
	{0x578f,0x0c},
	{0x5790,0x08},
	{0x5791,0x06},
	{0x5792,0x00},
	{0x5793,0x52},
	{0x5794,0xa3},
	{0x5b00,0x00},
	{0x5b01,0x1c},
	{0x5b02,0x00},
	{0x5b03,0x7f},
	{0x5b05,0x6c},
	{0x5e10,0xfc},
	{0x4010,0xf1},
	{0x3503,0x08},
	{0x3505,0x8c},
	{0x3507,0x03},
	{0x3508,0x00},
	{0x3509,0xf8},
	{0x0100,0x01},

	{SENSOR_REG_END, 0x00},
};

static struct regval_list sensor_init_regs_2592_1944_30fps_mipi[] = {
	{0x0103,0x01},
	{0x0100,0x00},
	{0x0300,0x04},
	{0x0301,0x00},
	{0x0302,0x69},
	{0x0303,0x00},
	{0x0304,0x00},
	{0x0305,0x01},
	{0x0307,0x00},
	{0x030b,0x00},
	{0x030c,0x00},
	{0x030d,0x1e},
	{0x030e,0x04},
	{0x030f,0x03},
	{0x0312,0x01},
	{0x3000,0x00},
	{0x3002,0x21},
	{0x3016,0x32},
	{0x3022,0x51},
	{0x3106,0x15},
	{0x3107,0x01},
	{0x3108,0x05},
	{0x3500,0x00},
	{0x3501,0x7e},
	{0x3502,0x00},
	{0x3503,0x08},
	{0x3504,0x03},
	{0x3505,0x8c},
	{0x3507,0x03},
	{0x3508,0x00},
	{0x3509,0x10},
	{0x350c,0x00},
	{0x350d,0x80},
	{0x3510,0x00},
	{0x3511,0x02},
	{0x3512,0x00},
	{0x3601,0x55},
	{0x3602,0x58},
	{0x3611,0x58},
	{0x3614,0x30},
	{0x3615,0x77},
	{0x3621,0x08},
	{0x3624,0x40},
	{0x3633,0x0c},
	{0x3634,0x0c},
	{0x3635,0x0c},
	{0x3636,0x0c},
	{0x3638,0x00},
	{0x3639,0x00},
	{0x363a,0x00},
	{0x363b,0x00},
	{0x363c,0xff},
	{0x363d,0xfa},
	{0x3650,0x44},
	{0x3651,0x44},
	{0x3652,0x44},
	{0x3653,0x44},
	{0x3654,0x44},
	{0x3655,0x44},
	{0x3656,0x44},
	{0x3657,0x44},
	{0x3660,0x00},
	{0x3661,0x00},
	{0x3662,0x00},
	{0x366a,0x00},
	{0x366e,0x18},
	{0x3673,0x04},
	{0x3700,0x14},
	{0x3703,0x0c},
	{0x3706,0x24},
	{0x3714,0x23},
	{0x3715,0x01},
	{0x3716,0x00},
	{0x3717,0x02},
	{0x3733,0x10},
	{0x3734,0x40},
	{0x373f,0xa0},
	{0x3765,0x20},
	{0x37a1,0x1d},
	{0x37a8,0x26},
	{0x37ab,0x14},
	{0x37c2,0x04},
	{0x37c3,0xf0},
	{0x37cb,0x09},
	{0x37cc,0x13},
	{0x37cd,0x1f},
	{0x37ce,0x1f},
	{0x3800,0x00},
	{0x3801,0x00},
	{0x3802,0x00},
	{0x3803,0x04},
	{0x3804,0x0a},
	{0x3805,0x3f},
	{0x3806,0x07},
	{0x3807,0xab},
	{0x3808,0x0a},/*2592*/
	{0x3809,0x20},//
	{0x380a,0x07},/*1944*/
	{0x380b,0x98},//
	{0x380c,0x02},/*hts*/
	{0x380d,0xe4},//
	{0x380e,0x07},/*vts*/
	{0x380f,0xe8},//
	{0x3810,0x00},
	{0x3811,0x10},
	{0x3812,0x00},
	{0x3813,0x08},
	{0x3814,0x01},
	{0x3815,0x01},
	{0x3816,0x01},
	{0x3817,0x01},
	{0x3818,0x00},
	{0x3819,0x00},
	{0x381a,0x00},
	{0x381b,0x01},
	{0x3820,0x88},
	{0x3821,0x00},
	{0x3c80,0x08},
	{0x3c82,0x00},
	{0x3c83,0x00},
	{0x3c88,0x00},
	{0x3d85,0x14},
	{0x3f02,0x08},
	{0x3f03,0x10},
	{0x4008,0x04},
	{0x4009,0x13},
	{0x404e,0x20},
	{0x4501,0x00},
	{0x4502,0x10},
	{0x4800,0x00},
	{0x481f,0x2a},
	{0x4837,0x13},
	{0x5000,0x13},
	{0x5780,0x3e},
	{0x5781,0x0f},
	{0x5782,0x44},
	{0x5783,0x02},
	{0x5784,0x01},
	{0x5785,0x01},
	{0x5786,0x00},
	{0x5787,0x04},
	{0x5788,0x02},
	{0x5789,0x0f},
	{0x578a,0xfd},
	{0x578b,0xf5},
	{0x578c,0xf5},
	{0x578d,0x03},
	{0x578e,0x08},
	{0x578f,0x0c},
	{0x5790,0x08},
	{0x5791,0x06},
	{0x5792,0x00},
	{0x5793,0x52},
	{0x5794,0xa3},
	{0x5b00,0x00},
	{0x5b01,0x1c},
	{0x5b02,0x00},
	{0x5b03,0x7f},
	{0x5b05,0x6c},
	{0x5e10,0xfc},
	{0x4010,0xf1},
	{0x3503,0x08},
	{0x3505,0x8c},
	{0x3507,0x03},
	{0x3508,0x00},
	{0x3509,0xf8},
	{0x0100,0x01},

	{SENSOR_REG_END, 0x00},
};


static struct tx_isp_sensor_win_setting sensor_win_sizes[] = {
	/* [0] 1920*1080 @60fps */
	{
		.width = 1920,
		.height = 1080,
		.fps = 60 << 16 | 1,
		.mbus_code = V4L2_MBUS_FMT_SBGGR10_1X10,
		.colorspace = V4L2_COLORSPACE_SRGB,
		.regs = sensor_init_regs_1920_1080_60fps_mipi,
	},
	/* [1] 1280*720 @60fps */
	{
		.width = 1280,
        .height = 720,
        .fps = 60 << 16 | 1,
        .mbus_code = V4L2_MBUS_FMT_SBGGR10_1X10,
        .colorspace = V4L2_COLORSPACE_SRGB,
        .regs = sensor_init_regs_1280_720_60fps_mipi,
	},
	/* [2] 640*480 @60fps */
    {
        .width = 640,
        .height = 480,
        .fps = 60 << 16 | 1,
        .mbus_code = V4L2_MBUS_FMT_SBGGR10_1X10,
        .colorspace = V4L2_COLORSPACE_SRGB,
        .regs = sensor_init_regs_640_480_60fps_mipi,
    },
	/* [3] 2592*1944 @30fps */
	{
        .width = 2592,
        .height = 1944,
        .fps = 30 << 16 | 1,
        .mbus_code = V4L2_MBUS_FMT_SBGGR10_1X10,
        .colorspace = V4L2_COLORSPACE_SRGB,
        .regs = sensor_init_regs_2592_1944_30fps_mipi,
	}
};
struct tx_isp_sensor_win_setting *wsize = &sensor_win_sizes[0];

static struct regval_list sensor_stream_on_mipi[] = {
	{SENSOR_REG_END, 0x00},
};

static struct regval_list sensor_stream_off_mipi[] = {
	{SENSOR_REG_END, 0x00},
};

int sensor_read(struct tx_isp_subdev *sd, uint16_t reg, unsigned char *value)
{
	struct i2c_client *client = tx_isp_get_subdevdata(sd);
	unsigned char buf[2] = {reg >> 8, reg & 0xff};
	struct i2c_msg msg[2] = {
		[0] = {
			.addr = client->addr,
			.flags = 0,
			.len = 2,
			.buf = buf,
		},
		[1] = {
			.addr = client->addr,
			.flags = I2C_M_RD,
			.len = 1,
			.buf = value,
		}
	};
	int ret;
	ret = private_i2c_transfer(client->adapter, msg, 2);
	if (ret > 0)
		ret = 0;

	return ret;
}

int sensor_write(struct tx_isp_subdev *sd, uint16_t reg, unsigned char value)
{
	struct i2c_client *client = tx_isp_get_subdevdata(sd);
	uint8_t buf[3] = {(reg >> 8) & 0xff, reg & 0xff, value};
	struct i2c_msg msg = {
		.addr = client->addr,
		.flags = 0,
		.len = 3,
		.buf = buf,
	};
	int ret;
	ret = private_i2c_transfer(client->adapter, &msg, 1);
	if (ret > 0)
		ret = 0;

	return ret;
}


static int sensor_read_array(struct tx_isp_subdev *sd, struct regval_list *vals)
{
	int ret;
	unsigned char val;
	while (vals->reg_num != SENSOR_REG_END) {
		if (vals->reg_num == SENSOR_REG_DELAY) {
			private_msleep(vals->value);
		} else {
			ret = sensor_read(sd, vals->reg_num, &val);
			if (ret < 0)
				return ret;
		}
		vals++;
	}

	return 0;
}

static int sensor_write_array(struct tx_isp_subdev *sd, struct regval_list *vals)
{
	int ret;
	while (vals->reg_num != SENSOR_REG_END) {
		if (vals->reg_num == SENSOR_REG_DELAY) {
			private_msleep(vals->value);
		} else {
			ret = sensor_write(sd, vals->reg_num, vals->value);
			if (ret < 0)
				return ret;
		}
		vals++;
	}

	return 0;
}

static int sensor_reset(struct tx_isp_subdev *sd, int val)
{
	return 0;
}

static int sensor_detect(struct tx_isp_subdev *sd, unsigned int *ident)
{
	int ret;
	unsigned char v;

	ret = sensor_read(sd, 0x300b, &v);
	ISP_WARNING("-----%s: %d ret = %d, v = 0x%02x\n", __func__, __LINE__, ret,v);
	if (ret < 0)
		return ret;
	if (v != SENSOR_CHIP_ID_H)
		return -ENODEV;
	*ident = v;

	ret = sensor_read(sd, 0x300c, &v);
	ISP_WARNING("-----%s: %d ret = %d, v = 0x%02x\n", __func__, __LINE__, ret,v);
	if (ret < 0)
		return ret;
	if (v != SENSOR_CHIP_ID_L)
		return -ENODEV;
	*ident = (*ident << 8) | v;

	return 0;
}

static int sensor_set_expo(struct tx_isp_subdev *sd, int value)
{
    int ret = 0;
    int it = (value & 0xffff) << 4;
    int again = (value & 0xffff0000) >> 16;

    /*set integration time*/
	ret = sensor_write(sd, 0x3502, (unsigned char)(it & 0xff));
	ret += sensor_write(sd, 0x3501, (unsigned char)((it >> 8) & 0xff));
	ret += sensor_write(sd, 0x3500, (unsigned char)((it >> 16) & 0xf));
    /*set analog gain*/
    ret += sensor_write(sd, 0x3509, (unsigned char)(again & 0xff));
    ret += sensor_write(sd, 0x3508, (unsigned char)(((again >> 8) & 0xff)));

	if (ret < 0)
	    return ret;

	return 0;
}

static int sensor_set_integration_time(struct tx_isp_subdev *sd, int value)
{
	int ret = 0;
	unsigned int it = value << 4;

	ret = sensor_write(sd, 0x3502, (unsigned char)(it & 0xff));
	ret += sensor_write(sd, 0x3501, (unsigned char)((it >> 8) & 0xff));
	ret += sensor_write(sd, 0x3500, (unsigned char)((it >> 16) & 0xf));
	if (ret < 0)
		return ret;
	return 0;
}

static int sensor_set_analog_gain(struct tx_isp_subdev *sd, int value)
{
	int ret = 0;

	ret += sensor_write(sd, 0x3509, (unsigned char)(value & 0xff));
	ret += sensor_write(sd, 0x3508, (unsigned char)(((value >> 8) & 0xff)));
	if (ret < 0)
		return ret;

	return 0;
}

static int sensor_set_digital_gain(struct tx_isp_subdev *sd, int value)
{
	return 0;
}

static int sensor_get_black_pedestal(struct tx_isp_subdev *sd, int value)
{
	return 0;
}

static int sensor_init(struct tx_isp_subdev *sd, int enable)
{
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);
	int ret = 0;

	if (!enable)
		return ISP_SUCCESS;

	sensor->video.mbus.width = wsize->width;
	sensor->video.mbus.height = wsize->height;
	sensor->video.mbus.code = wsize->mbus_code;
	sensor->video.mbus.field = V4L2_FIELD_NONE;
	sensor->video.mbus.colorspace = wsize->colorspace;
	sensor->video.fps = wsize->fps;

	ret = sensor_write_array(sd, wsize->regs);
	if (ret)
		return ret;
	ret = tx_isp_call_subdev_notify(sd, TX_ISP_EVENT_SYNC_SENSOR_ATTR, &sensor->video);
	sensor->priv = wsize;

	return 0;
}

static int sensor_s_stream(struct tx_isp_subdev *sd, int enable)
{
	int ret = 0;

	if (enable) {
		if (data_interface == TX_SENSOR_DATA_INTERFACE_MIPI) {
			ret = sensor_write_array(sd, sensor_stream_on_mipi);
		} else {
			ISP_ERROR("Don't support this Sensor Data interface\n");
		}
		ISP_WARNING("%s stream on\n", SENSOR_NAME);

	}
	else {
		if (data_interface == TX_SENSOR_DATA_INTERFACE_MIPI) {
			ret = sensor_write_array(sd, sensor_stream_off_mipi);
		} else {
			ISP_ERROR("Don't support this Sensor Data interface\n");
		}
		ISP_WARNING("%s stream off\n", SENSOR_NAME);
	}

	return ret;
}

static int sensor_set_fps(struct tx_isp_subdev *sd, int fps)
{
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);
	int ret = 0;
	unsigned int sclk = 0;
	unsigned int hts = 0;
	unsigned int vts = 0;
	unsigned char tmp = 0;
	unsigned int newformat = 0; //the format is 24.8

	newformat = (((fps >> 16) / (fps & 0xffff)) << 8) + ((((fps >> 16) % (fps & 0xffff)) << 8) / (fps & 0xffff));
	if (newformat > (SENSOR_OUTPUT_MAX_FPS << 8) || fps < (SENSOR_OUTPUT_MIN_FPS << 8)) {
		return -1;
	}

	sclk = SENSOR_SUPPORT_SCLK;

	ret += sensor_read(sd, 0x380c, &tmp);
	hts = tmp;
	ret += sensor_read(sd, 0x380d, &tmp);
	if (0 != ret) {
		printk("Error: %s read error\n", SENSOR_NAME);
		return ret;
	}

	hts = ((hts << 8) + tmp);

	vts = sclk * (fps & 0xffff) / hts / ((fps & 0xffff0000) >> 16);
	ret += sensor_write(sd, 0x380f, vts & 0xff);
	ret += sensor_write(sd, 0x380e, (vts >> 8) & 0xff);
	if (0 != ret) {
		printk("err: sensor_write err\n");
		return ret;
	}
	sensor->video.fps = fps;
	sensor->video.attr->max_integration_time_native = vts - 4;
	sensor->video.attr->integration_time_limit = vts - 4;
	sensor->video.attr->total_height = vts;
	sensor->video.attr->max_integration_time = vts - 4;
	ret = tx_isp_call_subdev_notify(sd, TX_ISP_EVENT_SYNC_SENSOR_ATTR, &sensor->video);

	return ret;
}

static int sensor_set_mode(struct tx_isp_subdev *sd, int value)
{
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);
	int ret = ISP_SUCCESS;

	if (wsize) {
		sensor->video.mbus.width = wsize->width;
		sensor->video.mbus.height = wsize->height;
		sensor->video.mbus.code = wsize->mbus_code;
		sensor->video.mbus.field = V4L2_FIELD_NONE;
		sensor->video.mbus.colorspace = wsize->colorspace;
		sensor->video.fps = wsize->fps;
		ret = tx_isp_call_subdev_notify(sd, TX_ISP_EVENT_SYNC_SENSOR_ATTR, &sensor->video);
	}

	return ret;
}

static int sensor_g_chip_ident(struct tx_isp_subdev *sd,
			       struct tx_isp_chip_ident *chip)
{
	struct i2c_client *client = tx_isp_get_subdevdata(sd);
	unsigned int ident = 0;
	int ret = ISP_SUCCESS;
	if (reset_gpio != -1) {
		ret = private_gpio_request(reset_gpio,"sensor_reset");
		if (!ret) {
			private_gpio_direction_output(reset_gpio, 1);
			private_msleep(5);
			private_gpio_direction_output(reset_gpio, 0);
			private_msleep(10);
			private_gpio_direction_output(reset_gpio, 1);
			private_msleep(10);
		} else {
			ISP_ERROR("gpio request fail %d\n",reset_gpio);
		}
	}
	if (pwdn_gpio != -1) {
		ret = private_gpio_request(pwdn_gpio,"sensor_pwdn");
		if (!ret) {
			private_gpio_direction_output(pwdn_gpio, 1);
			private_msleep(10);
			private_gpio_direction_output(pwdn_gpio, 0);
			private_msleep(10);
		} else {
			ISP_ERROR("gpio request fail %d\n",pwdn_gpio);
		}
	}
	ret = sensor_detect(sd, &ident);
	if (ret) {
		ISP_ERROR("chip found @ 0x%x (%s) is not an %s chip.\n",
			  client->addr, client->adapter->name, SENSOR_NAME);
		return ret;
	}
	ISP_WARNING("%s chip found @ 0x%02x (%s)\n",
		    SENSOR_NAME, client->addr, client->adapter->name);
	ISP_WARNING("sensor driver version %s\n",SENSOR_VERSION);
	if (chip) {
		memcpy(chip->name, SENSOR_NAME, sizeof(SENSOR_NAME));
		chip->ident = ident;
		chip->revision = SENSOR_VERSION;
	}

	return 0;
}

static int sensor_set_vflip(struct tx_isp_subdev *sd, int enable)
{
	return 0;
}

static int sensor_sensor_ops_ioctl(struct tx_isp_subdev *sd, unsigned int cmd, void *arg)
{
//	return 0;
	long ret = 0;
	if (IS_ERR_OR_NULL(sd)) {
		ISP_ERROR("[%d]The pointer is invalid!\n", __LINE__);
		return -EINVAL;
	}
	switch(cmd) {
	case TX_ISP_EVENT_SENSOR_EXPO:
//		if (arg)
//	     	ret = sensor_set_expo(sd, *(int*)arg);
		break;
	case TX_ISP_EVENT_SENSOR_INT_TIME:
		if (arg)
			ret = sensor_set_integration_time(sd, *(int*)arg);
		break;
	case TX_ISP_EVENT_SENSOR_AGAIN:
		if (arg)
			ret = sensor_set_analog_gain(sd, *(int*)arg);
		break;
	case TX_ISP_EVENT_SENSOR_DGAIN:
		if (arg)
			ret = sensor_set_digital_gain(sd, *(int*)arg);
		break;
	case TX_ISP_EVENT_SENSOR_BLACK_LEVEL:
		if (arg)
			ret = sensor_get_black_pedestal(sd, *(int*)arg);
		break;
	case TX_ISP_EVENT_SENSOR_RESIZE:
		if (arg)
			ret = sensor_set_mode(sd, *(int*)arg);
		break;
	case TX_ISP_EVENT_SENSOR_PREPARE_CHANGE:
		if (data_interface == TX_SENSOR_DATA_INTERFACE_MIPI) {
			ret = sensor_write_array(sd, sensor_stream_off_mipi);

		} else {
			ISP_ERROR("Don't support this Sensor Data interface\n");
		}
		break;
	case TX_ISP_EVENT_SENSOR_FINISH_CHANGE:
		if (data_interface == TX_SENSOR_DATA_INTERFACE_MIPI) {
			ret = sensor_write_array(sd, sensor_stream_on_mipi);

		} else {
			ISP_ERROR("Don't support this Sensor Data interface\n");
			ret = -1;
		}
		break;
	case TX_ISP_EVENT_SENSOR_FPS:
		if (arg)
			ret = sensor_set_fps(sd, *(int*)arg);
		break;
	case TX_ISP_EVENT_SENSOR_VFLIP:
		if (arg)
			ret = sensor_set_vflip(sd, *(int*)arg);
		break;
	default:
		break;
	}

	return ret;
}

static int sensor_g_register(struct tx_isp_subdev *sd, struct tx_isp_dbg_register *reg)
{
	unsigned char val = 0;
	int len = 0;
	int ret = 0;

	len = strlen(sd->chip.name);
	if (len && strncmp(sd->chip.name, reg->name, len)) {
		return -EINVAL;
	}
	if (!private_capable(CAP_SYS_ADMIN))
		return -EPERM;
	ret = sensor_read(sd, reg->reg & 0xffff, &val);
	reg->val = val;
	reg->size = 2;

	return ret;
}

static int sensor_s_register(struct tx_isp_subdev *sd, const struct tx_isp_dbg_register *reg)
{
	int len = 0;

	len = strlen(sd->chip.name);
	if (len && strncmp(sd->chip.name, reg->name, len)) {
		return -EINVAL;
	}
	if (!private_capable(CAP_SYS_ADMIN))
		return -EPERM;

	sensor_write(sd, reg->reg & 0xffff, reg->val & 0xff);

	return 0;
}

static struct tx_isp_subdev_core_ops sensor_core_ops = {
	.g_chip_ident = sensor_g_chip_ident,
	.reset = sensor_reset,
	.init = sensor_init,
	/*.ioctl = sensor_ops_ioctl,*/
	.g_register = sensor_g_register,
	.s_register = sensor_s_register,
};

static struct tx_isp_subdev_video_ops sensor_video_ops = {
	.s_stream = sensor_s_stream,
};

static struct tx_isp_subdev_sensor_ops sensor_sensor_ops = {
	.ioctl = sensor_sensor_ops_ioctl,
};

static struct tx_isp_subdev_ops sensor_ops = {
	.core = &sensor_core_ops,
	.video = &sensor_video_ops,
	.sensor = &sensor_sensor_ops,
};

/* It's the sensor device */
static u64 tx_isp_module_dma_mask = ~(u64)0;
struct platform_device sensor_platform_device = {
	.name = SENSOR_NAME,
	.id = -1,
	.dev = {
		.dma_mask = &tx_isp_module_dma_mask,
		.coherent_dma_mask = 0xffffffff,
		.platform_data = NULL,
	},
	.num_resources = 0,
};

static int sensor_probe(struct i2c_client *client, const struct i2c_device_id *id)
{
	struct tx_isp_subdev *sd;
	struct tx_isp_video_in *video;
	struct tx_isp_sensor *sensor;

	sensor = (struct tx_isp_sensor *)kzalloc(sizeof(*sensor), GFP_KERNEL);
	if (!sensor) {
		ISP_ERROR("Failed to allocate sensor subdev.\n");
		return -ENOMEM;
	}
	memset(sensor, 0 ,sizeof(*sensor));

	sensor->mclk = clk_get(NULL, "cgu_cim");
	if (IS_ERR(sensor->mclk)) {
		ISP_ERROR("Cannot get sensor input clock cgu_cim\n");
		goto err_get_mclk;
	}
	private_clk_set_rate(sensor->mclk, 24000000);
	private_clk_enable(sensor->mclk);

	switch(sensor_resolution) {
	case TX_SENSOR_RES_200:
		wsize = &sensor_win_sizes[0];
		printk("-----> 1920*1080 <------\n");
		break;
	case TX_SENSOR_RES_100:
		wsize = &sensor_win_sizes[1];
		sensor_attr.mipi.image_twidth = 1280;
		sensor_attr.mipi.image_theight = 720;
		printk("-------> 1280*720 <------\n");
		break;
	case TX_SENSOR_RES_30:
		wsize = &sensor_win_sizes[2];
		sensor_attr.mipi.image_twidth = 640;
		sensor_attr.mipi.image_theight = 480;
		printk("-------> 640*480 <------\n");
		break;
	case TX_SENSOR_RES_500:
		wsize = &sensor_win_sizes[3];
		sensor_attr.mipi.image_twidth = 2592;
		sensor_attr.mipi.image_theight = 1944;
		sensor_attr.max_integration_time_native = 0x7e8 - 4;
		sensor_attr.integration_time_limit = 0x7e8 - 4;
		sensor_attr.total_width = 0x2e4;
		sensor_attr.total_height = 0x7e8;
		sensor_attr.max_integration_time = 0x7e8 - 4;
		sensor_attr.one_line_expr_in_us = 30;
		printk("-------> 2592*1944 <-------\n");
		break;
	default:
	    ISP_ERROR("Now we do not support this framerate!!!\n");

	}

	/*
	  convert sensor-gain into isp-gain,
	*/
	sd = &sensor->sd;
	video = &sensor->video;
	sensor->video.shvflip = shvflip;
	sensor_attr.expo_fs = 1;
	sensor->video.attr = &sensor_attr;
	sensor->video.vi_max_width = wsize->width;
	sensor->video.vi_max_height = wsize->height;
	sensor->video.mbus.width = wsize->width;
	sensor->video.mbus.height = wsize->height;
	sensor->video.mbus.code = wsize->mbus_code;
	sensor->video.mbus.field = V4L2_FIELD_NONE;
	sensor->video.mbus.colorspace = wsize->colorspace;
	sensor->video.fps = wsize->fps;
	tx_isp_subdev_init(&sensor_platform_device, sd, &sensor_ops);
	tx_isp_set_subdevdata(sd, client);
	tx_isp_set_subdev_hostdata(sd, sensor);
	private_i2c_set_clientdata(client, sd);

	ISP_WARNING("probe ok ------->%s\n", SENSOR_NAME);

	return 0;

err_get_mclk:
	private_clk_disable(sensor->mclk);
	private_clk_put(sensor->mclk);
	kfree(sensor);

	return -1;
}

static int sensor_remove(struct i2c_client *client)
{
	struct tx_isp_subdev *sd = private_i2c_get_clientdata(client);
	struct tx_isp_sensor *sensor = tx_isp_get_subdev_hostdata(sd);

	if (reset_gpio != -1)
		private_gpio_free(reset_gpio);
	if (pwdn_gpio != -1)
		private_gpio_free(pwdn_gpio);

	private_clk_disable(sensor->mclk);
	private_clk_put(sensor->mclk);
	tx_isp_subdev_deinit(sd);
	kfree(sensor);

	return 0;
}

static const struct i2c_device_id sensor_id[] = {
	{ SENSOR_NAME, 0 },
	{ }
};
MODULE_DEVICE_TABLE(i2c, sensor_id);

static struct i2c_driver sensor_driver = {
	.driver = {
		.owner = THIS_MODULE,
		.name = SENSOR_NAME,
	},
	.probe = sensor_probe,
	.remove = sensor_remove,
	.id_table = sensor_id,
};

static __init int init_sensor(void)
{
	int ret = 0;
	sensor_common_init(&sensor_info);

	ret = private_driver_get_interface();
	if (ret) {
		ISP_ERROR("Failed to init %s driver.\n", SENSOR_NAME);
		return -1;
	}
	return private_i2c_add_driver(&sensor_driver);
}

static __exit void exit_sensor(void)
{
	private_i2c_del_driver(&sensor_driver);
	sensor_common_exit();
}

module_init(init_sensor);
module_exit(exit_sensor);

MODULE_DESCRIPTION("A low-level driver for "SENSOR_NAME" sensor");
MODULE_LICENSE("GPL");
