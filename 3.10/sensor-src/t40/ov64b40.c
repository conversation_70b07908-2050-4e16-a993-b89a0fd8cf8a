// SPDX-License-Identifier: GPL-2.0+
/*
 * ov64b40.c
 * Copyright (C) 2012 Ingenic Semiconductor Co., Ltd.
 *
 * Settings:
 * sboot        resolution      fps       interface              mode
 *   0          2304*1728       20        mipi_2lane           linear
 */

#include <linux/init.h>
#include <linux/module.h>
#include <linux/slab.h>
#include <linux/i2c.h>
#include <linux/delay.h>
#include <linux/gpio.h>
#include <linux/clk.h>
#include <linux/proc_fs.h>
#include <tx-isp-common.h>
#include <sensor-common.h>

#define SENSOR_NAME "ov64b40"
#define SENSOR_CHIP_ID_H (0x56)
#define SENSOR_CHIP_ID_M (0x64)
#define SENSOR_CHIP_ID_L (0x42)
#define SENSOR_REG_END 0xffff
#define SENSOR_REG_DELAY 0xfffe
#define SENSOR_SUPPORT_SCLK_MIPI (2112 * 2 * 2726 * 20)
#define SENSOR_OUTPUT_MAX_FPS 20
#define SENSOR_OUTPUT_MIN_FPS 5
#define MCLK 24000000
#define SENSOR_VERSION "H20221116a"

static int reset_gpio = GPIO_PC(27);
static int pwdn_gpio = -1;

struct regval_list {
    uint16_t reg_num;
    unsigned char value;
};

struct again_lut {
	unsigned int value;
	unsigned int gain;
};

struct again_lut sensor_again_lut[] = {
	{0x100, 0.000000},
	{0x102, 735.789368},
	{0x104, 1465.896973},
	{0x106, 2190.409668},
	{0x108, 2909.412842},
	{0x10a, 3622.989746},
	{0x10c, 4331.221191},
	{0x10e, 5034.187012},
	{0x110, 5731.964844},
	{0x112, 6424.630371},
	{0x114, 7112.258789},
	{0x116, 7794.922363},
	{0x118, 8472.691406},
	{0x11a, 9145.637695},
	{0x11c, 9813.827148},
	{0x11e, 10477.327148},
	{0x120, 11136.204102},
	{0x122, 11790.522461},
	{0x124, 12440.342773},
	{0x126, 13085.727539},
	{0x128, 13726.736328},
	{0x12a, 14363.427734},
	{0x12c, 14995.861328},
	{0x12e, 15624.092773},
	{0x130, 16248.177734},
	{0x132, 16868.169922},
	{0x134, 17484.123047},
	{0x136, 18096.087891},
	{0x138, 18704.119141},
	{0x13a, 19308.265625},
	{0x13c, 19908.574219},
	{0x13e, 20505.097656},
	{0x140, 21097.878906},
	{0x142, 21686.968750},
	{0x144, 22272.410156},
	{0x146, 22854.248047},
	{0x148, 23432.529297},
	{0x14a, 24007.292969},
	{0x14c, 24578.583984},
	{0x14e, 25146.445312},
	{0x150, 25710.914062},
	{0x152, 26272.035156},
	{0x154, 26829.843750},
	{0x156, 27384.380859},
	{0x158, 27935.687500},
	{0x15a, 28483.796875},
	{0x15c, 29028.746094},
	{0x15e, 29570.570312},
	{0x160, 30109.310547},
	{0x162, 30644.998047},
	{0x164, 31177.666016},
	{0x166, 31707.351562},
	{0x168, 32234.083984},
	{0x16a, 32757.900391},
	{0x16c, 33278.828125},
	{0x16e, 33796.906250},
	{0x170, 34312.156250},
	{0x172, 34824.617188},
	{0x174, 35334.312500},
	{0x176, 35841.273438},
	{0x178, 36345.535156},
	{0x17a, 36847.117188},
	{0x17c, 37346.054688},
	{0x17e, 37842.375000},
	{0x180, 38336.101562},
	{0x182, 38827.265625},
	{0x184, 39315.890625},
	{0x186, 39802.000000},
	{0x188, 40285.625000},
	{0x18a, 40766.789062},
	{0x18c, 41245.515625},
	{0x18e, 41721.828125},
	{0x190, 42195.757812},
	{0x192, 42667.324219},
	{0x194, 43136.546875},
	{0x196, 43603.453125},
	{0x198, 44068.066406},
	{0x19a, 44530.406250},
	{0x19c, 44990.500000},
	{0x19e, 45448.359375},
	{0x1a0, 45904.015625},
	{0x1a2, 46357.488281},
	{0x1a4, 46808.796875},
	{0x1a6, 47257.957031},
	{0x1a8, 47704.996094},
	{0x1aa, 48149.929688},
	{0x1ac, 48592.781250},
	{0x1ae, 49033.566406},
	{0x1b0, 49472.308594},
	{0x1b2, 49909.023438},
	{0x1b4, 50343.726562},
	{0x1b6, 50776.445312},
	{0x1b8, 51207.187500},
	{0x1ba, 51635.984375},
	{0x1bc, 52062.839844},
	{0x1be, 52487.773438},
	{0x1c0, 52910.812500},
	{0x1c2, 53331.964844},
	{0x1c4, 53751.250000},
	{0x1c6, 54168.679688},
	{0x1c8, 54584.281250},
	{0x1ca, 54998.058594},
	{0x1cc, 55410.035156},
	{0x1ce, 55820.226562},
	{0x1d0, 56228.640625},
	{0x1d2, 56635.304688},
	{0x1d4, 57040.222656},
	{0x1d6, 57443.414062},
	{0x1d8, 57844.894531},
	{0x1da, 58244.679688},
	{0x1dc, 58642.777344},
	{0x1de, 59039.207031},
	{0x1e0, 59433.980469},
	{0x1e2, 59827.117188},
	{0x1e4, 60218.621094},
	{0x1e6, 60608.511719},
	{0x1e8, 60996.800781},
	{0x1ea, 61383.503906},
	{0x1ec, 61768.628906},
	{0x1ee, 62152.195312},
	{0x1f0, 62534.210938},
	{0x1f2, 62914.687500},
	{0x1f4, 63293.640625},
	{0x1f6, 63671.078125},
	{0x1f8, 64047.015625},
	{0x1fa, 64421.468750},
	{0x1fc, 64794.441406},
	{0x1fe, 65165.945312},
	{0x200, 65536.000000},
	{0x204, 66271.789062},
	{0x208, 67001.898438},
	{0x20c, 67726.406250},
	{0x210, 68445.414062},
	{0x214, 69158.992188},
	{0x218, 69867.218750},
	{0x21c, 70570.187500},
	{0x220, 71267.968750},
	{0x224, 71960.632812},
	{0x228, 72648.257812},
	{0x22c, 73330.921875},
	{0x230, 74008.687500},
	{0x234, 74681.640625},
	{0x238, 75349.828125},
	{0x23c, 76013.328125},
	{0x240, 76672.203125},
	{0x244, 77326.523438},
	{0x248, 77976.343750},
	{0x24c, 78621.726562},
	{0x250, 79262.734375},
	{0x254, 79899.429688},
	{0x258, 80531.859375},
	{0x25c, 81160.093750},
	{0x260, 81784.179688},
	{0x264, 82404.171875},
	{0x268, 83020.125000},
	{0x26c, 83632.085938},
	{0x270, 84240.117188},
	{0x274, 84844.265625},
	{0x278, 85444.578125},
	{0x27c, 86041.093750},
	{0x280, 86633.875000},
	{0x284, 87222.968750},
	{0x288, 87808.406250},
	{0x28c, 88390.250000},
	{0x290, 88968.531250},
	{0x294, 89543.296875},
	{0x298, 90114.585938},
	{0x29c, 90682.445312},
	{0x2a0, 91246.914062},
	{0x2a4, 91808.031250},
	{0x2a8, 92365.843750},
	{0x2ac, 92920.382812},
	{0x2b0, 93471.687500},
	{0x2b4, 94019.796875},
	{0x2b8, 94564.750000},
	{0x2bc, 95106.570312},
	{0x2c0, 95645.312500},
	{0x2c4, 96181.000000},
	{0x2c8, 96713.664062},
	{0x2cc, 97243.351562},
	{0x2d0, 97770.085938},
	{0x2d4, 98293.898438},
	{0x2d8, 98814.828125},
	{0x2dc, 99332.906250},
	{0x2e0, 99848.156250},
	{0x2e4, 100360.617188},
	{0x2e8, 100870.312500},
	{0x2ec, 101377.273438},
	{0x2f0, 101881.531250},
	{0x2f4, 102383.117188},
	{0x2f8, 102882.054688},
	{0x2fc, 103378.375000},
	{0x300, 103872.101562},
	{0x304, 104363.265625},
	{0x308, 104851.890625},
	{0x30c, 105338.000000},
	{0x310, 105821.625000},
	{0x314, 106302.789062},
	{0x318, 106781.515625},
	{0x31c, 107257.828125},
	{0x320, 107731.757812},
	{0x324, 108203.328125},
	{0x328, 108672.546875},
	{0x32c, 109139.453125},
	{0x330, 109604.062500},
	{0x334, 110066.406250},
	{0x338, 110526.500000},
	{0x33c, 110984.359375},
	{0x340, 111440.015625},
	{0x344, 111893.484375},
	{0x348, 112344.796875},
	{0x34c, 112793.953125},
	{0x350, 113241.000000},
	{0x354, 113685.929688},
	{0x358, 114128.781250},
	{0x35c, 114569.562500},
	{0x360, 115008.304688},
	{0x364, 115445.023438},
	{0x368, 115879.726562},
	{0x36c, 116312.445312},
	{0x370, 116743.187500},
	{0x374, 117171.984375},
	{0x378, 117598.835938},
	{0x37c, 118023.773438},
	{0x380, 118446.812500},
	{0x384, 118867.960938},
	{0x388, 119287.250000},
	{0x38c, 119704.679688},
	{0x390, 120120.281250},
	{0x394, 120534.062500},
	{0x398, 120946.039062},
	{0x39c, 121356.226562},
	{0x3a0, 121764.640625},
	{0x3a4, 122171.304688},
	{0x3a8, 122576.218750},
	{0x3ac, 122979.414062},
	{0x3b0, 123380.898438},
	{0x3b4, 123780.679688},
	{0x3b8, 124178.773438},
	{0x3bc, 124575.203125},
	{0x3c0, 124969.984375},
	{0x3c4, 125363.117188},
	{0x3c8, 125754.625000},
	{0x3cc, 126144.515625},
	{0x3d0, 126532.804688},
	{0x3d4, 126919.500000},
	{0x3d8, 127304.632812},
	{0x3dc, 127688.195312},
	{0x3e0, 128070.210938},
	{0x3e4, 128450.687500},
	{0x3e8, 128829.640625},
	{0x3ec, 129207.078125},
	{0x3f0, 129583.015625},
	{0x3f4, 129957.468750},
	{0x3f8, 130330.437500},
	{0x3fc, 130701.945312},
	{0x400, 131072.000000},
	{0x408, 131807.796875},
	{0x410, 132537.890625},
	{0x418, 133262.406250},
	{0x420, 133981.406250},
	{0x428, 134694.984375},
	{0x430, 135403.218750},
	{0x438, 136106.187500},
	{0x440, 136803.968750},
	{0x448, 137496.625000},
	{0x450, 138184.265625},
	{0x458, 138866.921875},
	{0x460, 139544.687500},
	{0x468, 140217.640625},
	{0x470, 140885.828125},
	{0x478, 141549.328125},
	{0x480, 142208.203125},
	{0x488, 142862.515625},
	{0x490, 143512.343750},
	{0x498, 144157.734375},
	{0x4a0, 144798.734375},
	{0x4a8, 145435.421875},
	{0x4b0, 146067.859375},
	{0x4b8, 146696.093750},
	{0x4c0, 147320.171875},
	{0x4c8, 147940.171875},
	{0x4d0, 148556.125000},
	{0x4d8, 149168.093750},
	{0x4e0, 149776.125000},
	{0x4e8, 150380.265625},
	{0x4f0, 150980.578125},
	{0x4f8, 151577.093750},
	{0x500, 152169.875000},
	{0x508, 152758.968750},
	{0x510, 153344.406250},
	{0x518, 153926.250000},
	{0x520, 154504.531250},
	{0x528, 155079.296875},
	{0x530, 155650.578125},
	{0x538, 156218.437500},
	{0x540, 156782.906250},
	{0x548, 157344.031250},
	{0x550, 157901.843750},
	{0x558, 158456.375000},
	{0x560, 159007.687500},
	{0x568, 159555.796875},
	{0x570, 160100.750000},
	{0x578, 160642.562500},
	{0x580, 161181.312500},
	{0x588, 161717.000000},
	{0x590, 162249.671875},
	{0x598, 162779.343750},
	{0x5a0, 163306.078125},
	{0x5a8, 163829.906250},
	{0x5b0, 164350.828125},
	{0x5b8, 164868.906250},
	{0x5c0, 165384.156250},
	{0x5c8, 165896.609375},
	{0x5d0, 166406.312500},
	{0x5d8, 166913.281250},
	{0x5e0, 167417.531250},
	{0x5e8, 167919.125000},
	{0x5f0, 168418.062500},
	{0x5f8, 168914.375000},
	{0x600, 169408.093750},
	{0x608, 169899.265625},
	{0x610, 170387.890625},
	{0x618, 170874.000000},
	{0x620, 171357.625000},
	{0x628, 171838.781250},
	{0x630, 172317.515625},
	{0x638, 172793.828125},
	{0x640, 173267.765625},
	{0x648, 173739.328125},
	{0x650, 174208.546875},
	{0x658, 174675.453125},
	{0x660, 175140.062500},
	{0x668, 175602.406250},
	{0x670, 176062.500000},
	{0x678, 176520.359375},
	{0x680, 176976.015625},
	{0x688, 177429.484375},
	{0x690, 177880.796875},
	{0x698, 178329.953125},
	{0x6a0, 178777.000000},
	{0x6a8, 179221.937500},
	{0x6b0, 179664.781250},
	{0x6b8, 180105.562500},
	{0x6c0, 180544.312500},
	{0x6c8, 180981.015625},
	{0x6d0, 181415.734375},
	{0x6d8, 181848.437500},
	{0x6e0, 182279.187500},
	{0x6e8, 182707.984375},
	{0x6f0, 183134.843750},
	{0x6f8, 183559.781250},
	{0x700, 183982.812500},
	{0x708, 184403.968750},
	{0x710, 184823.250000},
	{0x718, 185240.687500},
	{0x720, 185656.281250},
	{0x728, 186070.062500},
	{0x730, 186482.031250},
	{0x738, 186892.218750},
	{0x740, 187300.640625},
	{0x748, 187707.296875},
	{0x750, 188112.218750},
	{0x758, 188515.421875},
	{0x760, 188916.890625},
	{0x768, 189316.671875},
	{0x770, 189714.781250},
	{0x778, 190111.203125},
	{0x780, 190505.984375},
	{0x788, 190899.109375},
	{0x790, 191290.625000},
	{0x798, 191680.515625},
	{0x7a0, 192068.796875},
	{0x7a8, 192455.500000},
	{0x7b0, 192840.625000},
	{0x7b8, 193224.187500},
	{0x7c0, 193606.203125},
	{0x7c8, 193986.687500},
	{0x7d0, 194365.640625},
	{0x7d8, 194743.078125},
	{0x7e0, 195119.015625},
	{0x7e8, 195493.468750},
	{0x7f0, 195866.437500},
	{0x7f8, 196237.953125},
	{0x800, 196608.000000},
	{0x810, 197343.796875},
	{0x820, 198073.890625},
	{0x830, 198798.406250},
	{0x840, 199517.406250},
	{0x850, 200230.984375},
	{0x860, 200939.218750},
	{0x870, 201642.187500},
	{0x880, 202339.968750},
	{0x890, 203032.625000},
	{0x8a0, 203720.265625},
	{0x8b0, 204402.921875},
	{0x8c0, 205080.687500},
	{0x8d0, 205753.640625},
	{0x8e0, 206421.828125},
	{0x8f0, 207085.328125},
	{0x900, 207744.203125},
	{0x910, 208398.515625},
	{0x920, 209048.343750},
	{0x930, 209693.734375},
	{0x940, 210334.734375},
	{0x950, 210971.421875},
	{0x960, 211603.859375},
	{0x970, 212232.093750},
	{0x980, 212856.171875},
	{0x990, 213476.171875},
	{0x9a0, 214092.125000},
	{0x9b0, 214704.093750},
	{0x9c0, 215312.125000},
	{0x9d0, 215916.265625},
	{0x9e0, 216516.578125},
	{0x9f0, 217113.093750},
	{0xa00, 217705.875000},
	{0xa10, 218294.968750},
	{0xa20, 218880.406250},
	{0xa30, 219462.250000},
	{0xa40, 220040.531250},
	{0xa50, 220615.296875},
	{0xa60, 221186.578125},
	{0xa70, 221754.437500},
	{0xa80, 222318.906250},
	{0xa90, 222880.031250},
	{0xaa0, 223437.843750},
	{0xab0, 223992.375000},
	{0xac0, 224543.687500},
	{0xad0, 225091.796875},
	{0xae0, 225636.750000},
	{0xaf0, 226178.562500},
	{0xb00, 226717.312500},
	{0xb10, 227253.000000},
	{0xb20, 227785.671875},
	{0xb30, 228315.343750},
	{0xb40, 228842.078125},
	{0xb50, 229365.906250},
	{0xb60, 229886.828125},
	{0xb70, 230404.906250},
	{0xb80, 230920.156250},
	{0xb90, 231432.609375},
	{0xba0, 231942.312500},
	{0xbb0, 232449.281250},
	{0xbc0, 232953.531250},
	{0xbd0, 233455.125000},
	{0xbe0, 233954.062500},
	{0xbf0, 234450.375000},
	{0xc00, 234944.093750},
	{0xc10, 235435.265625},
	{0xc20, 235923.890625},
	{0xc30, 236410.000000},
	{0xc40, 236893.625000},
	{0xc50, 237374.781250},
	{0xc60, 237853.515625},
	{0xc70, 238329.828125},
	{0xc80, 238803.765625},
	{0xc90, 239275.328125},
	{0xca0, 239744.546875},
	{0xcb0, 240211.453125},
	{0xcc0, 240676.062500},
	{0xcd0, 241138.406250},
	{0xce0, 241598.500000},
	{0xcf0, 242056.359375},
	{0xd00, 242512.015625},
	{0xd10, 242965.484375},
	{0xd20, 243416.796875},
	{0xd30, 243865.953125},
	{0xd40, 244313.000000},
	{0xd50, 244757.937500},
	{0xd60, 245200.781250},
	{0xd70, 245641.562500},
	{0xd80, 246080.312500},
	{0xd90, 246517.015625},
	{0xda0, 246951.734375},
	{0xdb0, 247384.437500},
	{0xdc0, 247815.187500},
	{0xdd0, 248243.984375},
	{0xde0, 248670.843750},
	{0xdf0, 249095.781250},
	{0xe00, 249518.812500},
	{0xe10, 249939.968750},
	{0xe20, 250359.250000},
	{0xe30, 250776.687500},
	{0xe40, 251192.281250},
	{0xe50, 251606.062500},
	{0xe60, 252018.031250},
	{0xe70, 252428.218750},
	{0xe80, 252836.640625},
	{0xe90, 253243.296875},
	{0xea0, 253648.218750},
	{0xeb0, 254051.421875},
	{0xec0, 254452.890625},
	{0xed0, 254852.671875},
	{0xee0, 255250.781250},
	{0xef0, 255647.203125},
	{0xf00, 256041.984375},
	{0xf10, 256435.109375},
	{0xf20, 256826.625000},
	{0xf30, 257216.515625},
	{0xf40, 257604.796875},
	{0xf50, 257991.500000},
	{0xf60, 258376.625000},
	{0xf70, 258760.187500},
	{0xf80, 259142.203125},
	{0xf90, 259522.687500},
	{0xfa0, 259901.640625},
	{0xfb0, 260279.078125},
	{0xfc0, 260655.015625},
	{0xfd0, 261029.468750},
	{0xfe0, 261402.437500},
	{0xff0, 261773.953125},

};

struct tx_isp_sensor_attribute sensor_attr;

unsigned int sensor_alloc_again(unsigned int isp_gain, unsigned char shift, unsigned int *sensor_again)
{
	struct again_lut *lut = sensor_again_lut;

	while (lut->gain <= sensor_attr.max_again) {
		if (isp_gain == 0) {
			*sensor_again = lut->value;
			return 0;
		}
		else if (isp_gain < lut->gain) {
			*sensor_again = (lut - 1)->value;
			return (lut - 1)->gain;
		}
		else {
			if ((lut->gain == sensor_attr.max_again) && (isp_gain >= lut->gain)) {
				*sensor_again = lut->value;
				return lut->gain;
			}
		}

		lut++;
	}

	return isp_gain;
}

unsigned int sensor_alloc_dgain(unsigned int isp_gain, unsigned char shift, unsigned int *sensor_dgain)
{
	return 0;
}

struct tx_isp_mipi_bus sensor_mipi_linear = {
	.clk = 800,
	.lans = 2,
	.settle_time_apative_en = 0,
	.mipi_sc.sensor_csi_fmt = TX_SENSOR_RAW10,
	.mipi_sc.hcrop_diff_en = 0,
	.mipi_sc.mipi_vcomp_en = 0,
	.mipi_sc.mipi_hcomp_en = 0,
	.image_twidth = 2304,
	.image_theight = 1728,
	.mipi_sc.mipi_crop_start0x = 0,
	.mipi_sc.mipi_crop_start0y = 0,
	.mipi_sc.mipi_crop_start1x = 0,
	.mipi_sc.mipi_crop_start1y = 0,
	.mipi_sc.mipi_crop_start2x = 0,
	.mipi_sc.mipi_crop_start2y = 0,
	.mipi_sc.mipi_crop_start3x = 0,
	.mipi_sc.mipi_crop_start3y = 0,
	.mipi_sc.line_sync_mode = 0,
	.mipi_sc.work_start_flag = 0,
	.mipi_sc.data_type_en = 0,
	.mipi_sc.data_type_value = RAW10,
	.mipi_sc.del_start = 0,
	.mipi_sc.sensor_frame_mode = TX_SENSOR_DEFAULT_FRAME_MODE,
	.mipi_sc.sensor_fid_mode = 0,
	.mipi_sc.sensor_mode = TX_SENSOR_DEFAULT_MODE,
};

struct tx_isp_sensor_attribute sensor_attr={
	.name = SENSOR_NAME,
	.chip_id = 0x566442,
	.cbus_type = TX_SENSOR_CONTROL_INTERFACE_I2C,
	.cbus_mask = TISP_SBUS_MASK_SAMPLE_8BITS | TISP_SBUS_MASK_ADDR_16BITS,
	.cbus_device = 0x36,
	.dbus_type = TX_SENSOR_DATA_INTERFACE_MIPI,
	.max_again = 261773,
	.max_dgain = 0,
	.min_integration_time = 4,
	.min_integration_time_native = 4,
	.integration_time_apply_delay = 4,
	.again_apply_delay = 4,
	.dgain_apply_delay = 0,
	.sensor_ctrl.alloc_again = sensor_alloc_again,
	.sensor_ctrl.alloc_dgain = sensor_alloc_dgain,
};

static struct regval_list sensor_init_regs_2304_1728_20fps_mipi[] = {
	{0x0103,0x01},
	{0x0102,0x01},
	{0x0102,0x01},
	{0x0301,0x48},
	{0x0304,0x02},
	{0x0305,0x71},
	{0x0306,0x04},
	{0x0307,0x00},
	{0x0309,0x50},
	{0x0316,0x73},
	{0x0320,0x02},
	{0x0321,0x03},
	{0x0323,0x03},
	{0x0324,0x01},
	{0x0325,0x80},
	{0x0326,0xc3},
	{0x0327,0x03},
	{0x0329,0x00},
	{0x032c,0x00},
	{0x032d,0x01},
	{0x032e,0x06},
	{0x032f,0xa1},
	{0x0350,0x00},
	{0x0360,0x01},
	{0x0361,0x07},
	{0x3002,0x00},
	{0x3008,0x00},
	{0x3012,0x41},
	{0x3015,0x00},
	{0x301a,0xb0},
	{0x301e,0x88},
	{0x3025,0x89},
	{0x3218,0xa1},
	{0x3220,0x12},
	{0x3221,0x28},
	{0x3400,0x04},
	{0x3406,0x10},
	{0x3422,0x00},
	{0x3423,0x00},
	{0x3424,0x15},
	{0x3425,0x40},
	{0x3426,0x10},
	{0x3427,0x10},
	{0x3428,0x00},
	{0x3429,0x00},
	{0x340c,0x00},
	{0x340d,0x00},
	{0x340e,0x60},
	{0x3421,0x08},
	{0x3500,0x00},
	{0x3501,0x0f},
	{0x3502,0xec},
	{0x3504,0x0c},
	{0x3508,0x08},
	{0x3509,0x00},
	{0x350a,0x01},
	{0x350b,0x00},
	{0x350c,0x00},
	{0x3540,0x00},
	{0x3541,0x00},
	{0x3542,0x20},
	{0x3544,0x08},
	{0x3548,0x01},
	{0x3549,0x00},
	{0x354a,0x01},
	{0x354b,0x00},
	{0x3580,0x00},
	{0x3581,0x00},
	{0x3582,0x10},
	{0x3584,0x08},
	{0x3588,0x01},
	{0x3589,0x00},
	{0x358a,0x01},
	{0x358b,0x00},
	{0x3605,0xf9},
	{0x3608,0x8a},
	{0x360a,0x5d},
	{0x360b,0x50},
	{0x360c,0x92},
	{0x360d,0x05},
	{0x360e,0x08},
	{0x3618,0x80},
	{0x361e,0x80},
	{0x3622,0x0a},
	{0x3623,0x08},
	{0x3628,0x99},
	{0x362b,0x77},
	{0x362d,0x0a},
	{0x3659,0x48},
	{0x365a,0x2b},
	{0x365b,0x0a},
	{0x3684,0x81},
	{0x3685,0x00},
	{0x3688,0x02},
	{0x3689,0x88},
	{0x368a,0x2e},
	{0x368d,0x00},
	{0x368e,0x78},
	{0x368f,0x00},
	{0x3694,0x7f},
	{0x3695,0x00},
	{0x3696,0x51},
	{0x3699,0x19},
	{0x369a,0x00},
	{0x369f,0x20},
	{0x36a4,0x00},
	{0x36a6,0x00},
	{0x36a7,0x00},
	{0x36a8,0x00},
	{0x36aa,0x12},
	{0x36ab,0x28},
	{0x36b0,0x12},
	{0x36b1,0x28},
	{0x36b2,0x12},
	{0x36b3,0x28},
	{0x36b4,0x12},
	{0x36b5,0x28},
	{0x36b6,0x12},
	{0x36b7,0x28},
	{0x36b8,0x12},
	{0x36b9,0x28},
	{0x36ba,0x12},
	{0x36bb,0x28},
	{0x36bc,0x12},
	{0x36bd,0x28},
	{0x36be,0x12},
	{0x36bf,0x28},
	{0x36c0,0x12},
	{0x36c1,0x28},
	{0x3700,0x2c},
	{0x3701,0x40},
	{0x3702,0x4b},
	{0x3703,0x40},
	{0x3706,0x3a},
	{0x3708,0x59},
	{0x3709,0xf8},
	{0x370b,0x82},
	{0x3711,0x00},
	{0x3712,0x50},
	{0x3713,0x00},
	{0x3714,0x67},
	{0x3716,0x40},
	{0x3717,0x82},
	{0x371d,0x02},
	{0x371e,0x12},
	{0x371f,0x02},
	{0x3720,0x08},
	{0x3721,0x02},
	{0x3724,0x0d},
	{0x3725,0x22},
	{0x372d,0x00},
	{0x3730,0x14},
	{0x3731,0x14},
	{0x3732,0x07},
	{0x3734,0x2e},
	{0x3736,0x0c},
	{0x3738,0x01},
	{0x3739,0x18},
	{0x373b,0x05},
	{0x3741,0x48},
	{0x3743,0xef},
	{0x3744,0x0f},
	{0x3745,0xff},
	{0x3746,0x00},
	{0x3748,0x01},
	{0x3749,0x18},
	{0x3751,0x49},
	{0x3753,0xff},
	{0x3754,0x0c},
	{0x3755,0x18},
	{0x3758,0x88},
	{0x3759,0x88},
	{0x375a,0x88},
	{0x375b,0x88},
	{0x375c,0x88},
	{0x375e,0x00},
	{0x375f,0x02},
	{0x3760,0x08},
	{0x3761,0x10},
	{0x3762,0x08},
	{0x3763,0x08},
	{0x3764,0x08},
	{0x3765,0x10},
	{0x3766,0x18},
	{0x3767,0x10},
	{0x3768,0x00},
	{0x3769,0x08},
	{0x376a,0x10},
	{0x376b,0x00},
	{0x376c,0x28},
	{0x3770,0x19},
	{0x3771,0x01},
	{0x3772,0x01},
	{0x3773,0x01},
	{0x3774,0x1a},
	{0x3775,0x01},
	{0x3776,0x01},
	{0x3777,0x01},
	{0x3780,0xc8},
	{0x3791,0x38},
	{0x3793,0x3a},
	{0x3795,0x3a},
	{0x3797,0x80},
	{0x379c,0x14},
	{0x379d,0x14},
	{0x3799,0x82},
	{0x379b,0x82},
	{0x37a0,0x07},
	{0x37a1,0x99},
	{0x37a2,0x11},
	{0x37af,0x00},
	{0x37f7,0x00},
	{0x37f8,0x5a},
	{0x37ff,0x18},
	{0x3800,0x00},
	{0x3801,0x00},
	{0x3802,0x00},
	{0x3803,0x00},
	{0x3804,0x24},
	{0x3805,0x3f},
	{0x3806,0x1b},
	{0x3807,0x3f},
	{0x3808,0x12},
	{0x3809,0x10},
	{0x380a,0x0d},
	{0x380b,0x90},
	{0x380c,0x03},
	{0x380d,0xa8},
	{0x380e,0x10},
	{0x380f,0x0c},
	{0x3810,0x00},
	{0x3811,0x09},
	{0x3812,0x00},
	{0x3813,0x08},
	{0x3814,0x11},
	{0x3815,0x11},
	{0x3820,0x42},
	{0x3821,0x04},
	{0x3822,0x10},
	{0x3823,0x04},
	{0x3828,0x0f},
	{0x382e,0x41},
	{0x3830,0x0b},
	{0x3837,0x0c},
	{0x383e,0x00},
	{0x383f,0x0c},
	{0x3840,0x00},
	{0x3847,0x00},
	{0x384a,0x00},
	{0x384c,0x03},
	{0x384d,0xa8},
	{0x3856,0x00},
	{0x3858,0x00},
	{0x3859,0x00},
	{0x3888,0x00},
	{0x3889,0x60},
	{0x388a,0x00},
	{0x388b,0x18},
	{0x388c,0x23},
	{0x388d,0x80},
	{0x388e,0x0d},
	{0x388f,0x70},
	{0x3894,0x00},
	{0x3900,0x0c},
	{0x3901,0x06},
	{0x3902,0xdf},
	{0x3903,0xfb},
	{0x3904,0xff},
	{0x3906,0x00},
	{0x3907,0x00},
	{0x3908,0x57},
	{0x3909,0xde},
	{0x390c,0x3f},
	{0x390e,0x3d},
	{0x390f,0x54},
	{0x3911,0x3c},
	{0x3912,0x03},
	{0x3913,0x41},
	{0x3914,0x40},
	{0x3919,0x26},
	{0x391b,0xa2},
	{0x391c,0x69},
	{0x3920,0x88},
	{0x3921,0x00},
	{0x3924,0x84},
	{0x3925,0x0f},
	{0x3926,0xfe},
	{0x3930,0x03},
	{0x3932,0x03},
	{0x3933,0x01},
	{0x3935,0x01},
	{0x393a,0x03},
	{0x393c,0x03},
	{0x393d,0x01},
	{0x393f,0x01},
	{0x3945,0x00},
	{0x3946,0x00},
	{0x3949,0x01},
	{0x394b,0x00},
	{0x394c,0x16},
	{0x3953,0x0c},
	{0x3954,0x0c},
	{0x3955,0x00},
	{0x3956,0x00},
	{0x3968,0x00},
	{0x396a,0x00},
	{0x3973,0x02},
	{0x3977,0x04},
	{0x3978,0x02},
	{0x3979,0x04},
	{0x397d,0x04},
	{0x3981,0x04},
	{0x3983,0x04},
	{0x3987,0x04},
	{0x3989,0x04},
	{0x3997,0x00},
	{0x399b,0x4b},
	{0x399c,0x00},
	{0x39a2,0x02},
	{0x39af,0x01},
	{0x39b7,0x01},
	{0x39bf,0x01},
	{0x39cb,0x00},
	{0x39cd,0x18},
	{0x39ce,0x48},
	{0x39cf,0x48},
	{0x39d0,0x48},
	{0x39d2,0x1b},
	{0x39d3,0x4b},
	{0x39d4,0x4b},
	{0x39d5,0x4b},
	{0x39d7,0x01},
	{0x39dc,0x00},
	{0x39e2,0x20},
	{0x39e4,0x20},
	{0x39e6,0x20},
	{0x39e8,0x20},
	{0x39eb,0x01},
	{0x39fd,0x01},
	{0x39f5,0x04},
	{0x39f6,0x1a},
	{0x39f7,0x00},
	{0x39f8,0x00},
	{0x39f9,0x00},
	{0x39ff,0x00},
	{0x3a01,0x03},
	{0x3a03,0x00},
	{0x3a05,0x05},
	{0x3a07,0x07},
	{0x3a17,0x04},
	{0x3a2d,0x00},
	{0x3a2e,0x00},
	{0x3a2f,0x20},
	{0x3a30,0x20},
	{0x3a31,0x20},
	{0x3a32,0x20},
	{0x3a33,0x20},
	{0x3a34,0x20},
	{0x3a35,0x55},
	{0x3a43,0x1e},
	{0x3a44,0x0f},
	{0x3a45,0x07},
	{0x3a46,0x03},
	{0x3a4b,0x08},
	{0x3a4c,0x08},
	{0x3a4d,0x08},
	{0x3a4e,0x08},
	{0x3a4f,0x08},
	{0x3a50,0x08},
	{0x3a51,0x08},
	{0x3a52,0x08},
	{0x3a53,0x88},
	{0x3a54,0x88},
	{0x3a55,0x88},
	{0x3a56,0x88},
	{0x3a57,0x15},
	{0x3a58,0x00},
	{0x3a59,0x00},
	{0x3a5a,0x01},
	{0x3a5f,0x3f},
	{0x3a60,0x3f},
	{0x3a61,0x3f},
	{0x3a62,0x3f},
	{0x3a63,0x3f},
	{0x3a64,0x3f},
	{0x3a65,0x3f},
	{0x3a66,0x3f},
	{0x3a67,0x1f},
	{0x3a68,0x1f},
	{0x3a69,0x1f},
	{0x3a6a,0x1f},
	{0x3a6b,0x1f},
	{0x3a6c,0x1f},
	{0x3a6d,0x1f},
	{0x3a6e,0x1f},
	{0x3a73,0x13},
	{0x3a74,0x10},
	{0x3a75,0x0b},
	{0x3a76,0x00},
	{0x3a77,0x13},
	{0x3a78,0x10},
	{0x3a79,0x0b},
	{0x3a7a,0x00},
	{0x3a7d,0x60},
	{0x3d85,0x0b},
	{0x3d86,0x12},
	{0x3d87,0x28},
	{0x3d8c,0x07},
	{0x3d8d,0xb0},
	{0x3daa,0x70},
	{0x3dab,0x10},
	{0x3dac,0x76},
	{0x3dad,0xa0},
	{0x3dae,0x77},
	{0x3daf,0x1f},
	{0x3f00,0x10},
	{0x4500,0x08},
	{0x4502,0x00},
	{0x4504,0x80},
	{0x4506,0x01},
	{0x4509,0x05},
	{0x450c,0x00},
	{0x450d,0x20},
	{0x450e,0x00},
	{0x450f,0x00},
	{0x4510,0x00},
	{0x4523,0x00},
	{0x4526,0x00},
	{0x4540,0x12},
	{0x4541,0x28},
	{0x4542,0x00},
	{0x4543,0x00},
	{0x4544,0x00},
	{0x4545,0x00},
	{0x4546,0x00},
	{0x4547,0x10},
	{0x4009,0x02},
	{0x400e,0xc6},
	{0x400f,0x00},
	{0x4010,0xe8},
	{0x4011,0x01},
	{0x4012,0x0c},
	{0x4015,0x00},
	{0x4016,0x0f},
	{0x4017,0x00},
	{0x4018,0x07},
	{0x401a,0x40},
	{0x401b,0x04},
	{0x401e,0x01},
	{0x401f,0x00},
	{0x4020,0x04},
	{0x4021,0x00},
	{0x4022,0x04},
	{0x4023,0x00},
	{0x4024,0x04},
	{0x4025,0x00},
	{0x4026,0x04},
	{0x4027,0x00},
	{0x4028,0x01},
	{0x4029,0x08},
	{0x40c2,0x12},
	{0x40c3,0x28},
	{0x4288,0xa7},
	{0x4289,0x20},
	{0x428c,0x00},
	{0x428d,0x80},
	{0x428e,0x04},
	{0x4580,0x01},
	{0x4589,0x12},
	{0x458a,0x28},
	{0x45c0,0x60},
	{0x45c9,0x12},
	{0x45ca,0x28},
	{0x45cb,0x30},
	{0x4602,0x00},
	{0x4603,0x15},
	{0x4606,0x12},
	{0x4607,0x28},
	{0x4609,0x20},
	{0x460b,0x03},
	{0x4640,0x00},
	{0x4641,0x47},
	{0x4643,0x04},
	{0x4644,0x40},
	{0x4645,0xb3},
	{0x4648,0x12},
	{0x4649,0x28},
	{0x464c,0x01},
	{0x464a,0x00},
	{0x464b,0x30},
	{0x4800,0x64},
	{0x4802,0x00},
	{0x480b,0x10},
	{0x480c,0x80},
	{0x480e,0x00},
	{0x480f,0x32},
	{0x481b,0x3c},
	{0x481f,0x30},
	{0x4833,0x20},
	{0x4837,0x05},
	{0x484b,0x27},
	{0x4850,0x47},
	{0x4853,0x04},
	{0x4854,0x08},
	{0x4860,0x00},
	{0x4861,0xec},
	{0x4862,0x05},
	{0x4883,0x05},
	{0x4885,0x3c},
	{0x4888,0x10},
	{0x4889,0x03},
	{0x488a,0x10},
	{0x4909,0x02},
	{0x4910,0xe8},
	{0x4911,0x01},
	{0x4912,0x0c},
	{0x4915,0x00},
	{0x4916,0x0f},
	{0x4917,0x00},
	{0x4918,0x07},
	{0x491a,0x40},
	{0x491b,0x04},
	{0x491e,0x01},
	{0x491f,0x00},
	{0x4920,0x04},
	{0x4921,0x00},
	{0x4922,0x04},
	{0x4923,0x00},
	{0x4924,0x04},
	{0x4925,0x00},
	{0x4926,0x04},
	{0x4927,0x00},
	{0x4929,0x08},
	{0x49c2,0x12},
	{0x49c3,0x28},
	{0x4a09,0x02},
	{0x4a10,0xe8},
	{0x4a11,0x01},
	{0x4a12,0x0c},
	{0x4a15,0x00},
	{0x4a16,0x0f},
	{0x4a17,0x00},
	{0x4a18,0x07},
	{0x4a1a,0x40},
	{0x4a1b,0x04},
	{0x4a1e,0x01},
	{0x4a1f,0x00},
	{0x4a20,0x04},
	{0x4a21,0x00},
	{0x4a22,0x04},
	{0x4a23,0x00},
	{0x4a24,0x04},
	{0x4a25,0x00},
	{0x4a26,0x04},
	{0x4a27,0x00},
	{0x4a29,0x08},
	{0x4ac2,0x12},
	{0x4ac3,0x28},
	{0x4d00,0x04},
	{0x4d01,0xf5},
	{0x4d02,0xb8},
	{0x4d03,0x76},
	{0x4d04,0x04},
	{0x4d05,0xcc},
	{0x5000,0xf7},
	{0x5001,0x01},
	{0x5002,0xf7},
	{0x5003,0x00},
	{0x5004,0x80},
	{0x5005,0x42},
	{0x5006,0x04},
	{0x5007,0x02},
	{0x5015,0x20},
	{0x5053,0x04},
	{0x5060,0x00},
	{0x5061,0x30},
	{0x5062,0x00},
	{0x5063,0x30},
	{0x5064,0x23},
	{0x5065,0xe0},
	{0x5066,0x1a},
	{0x5067,0xe0},
	{0x5068,0x02},
	{0x5069,0x10},
	{0x506a,0x10},
	{0x506b,0x04},
	{0x506c,0x04},
	{0x506d,0x0c},
	{0x506e,0x0c},
	{0x506f,0x04},
	{0x5070,0x04},
	{0x5071,0x0c},
	{0x5072,0x0c},
	{0x5073,0x01},
	{0x5074,0x01},
	{0x5075,0xaa},
	{0x5083,0x00},
	{0x50c1,0x00},
	{0x5110,0x70},
	{0x5111,0x10},
	{0x5112,0x77},
	{0x5113,0x1f},
	{0x5114,0x01},
	{0x5180,0xc1},
	{0x518a,0x04},
	{0x51b0,0x00},
	{0x51d0,0xc2},
	{0x51d1,0x68},
	{0x51d2,0xff},
	{0x51d3,0x1c},
	{0x51d8,0x08},
	{0x51d9,0x10},
	{0x51da,0x02},
	{0x51db,0x02},
	{0x51dc,0x06},
	{0x51dd,0x06},
	{0x51de,0x02},
	{0x51df,0x06},
	{0x51e0,0x0a},
	{0x51e1,0x0e},
	{0x51e2,0x00},
	{0x51e3,0x00},
	{0x51ee,0x01},
	{0x51ef,0x02},
	{0x51f0,0x04},
	{0x51f1,0xaa},
	{0x5200,0x12},
	{0x5201,0x20},
	{0x5202,0x0d},
	{0x5203,0xa0},
	{0x5205,0x10},
	{0x5207,0x10},
	{0x5208,0x12},
	{0x5209,0x00},
	{0x520a,0x0d},
	{0x520b,0x80},
	{0x520d,0x08},
	{0x5250,0x06},
	{0x5251,0x00},
	{0x525b,0x00},
	{0x525d,0x00},
	{0x527a,0x00},
	{0x527b,0x38},
	{0x527c,0x00},
	{0x527d,0x4b},
	{0x5286,0x1b},
	{0x5287,0x40},
	{0x5298,0x00},
	{0x5299,0x50},
	{0x529a,0x00},
	{0x529b,0x50},
	{0x529c,0x00},
	{0x529d,0x50},
	{0x529e,0x00},
	{0x529f,0x50},
	{0x52a0,0x00},
	{0x52a1,0x50},
	{0x52a2,0x00},
	{0x52a3,0x50},
	{0x52a4,0x00},
	{0x52a5,0x50},
	{0x52a6,0x00},
	{0x52a7,0x50},
	{0x52a8,0x00},
	{0x52a9,0x50},
	{0x52aa,0x00},
	{0x52ab,0x50},
	{0x52ac,0x00},
	{0x52ad,0x50},
	{0x52ae,0x00},
	{0x52af,0x50},
	{0x52d0,0x20},
	{0x52d1,0x20},
	{0x52d2,0x20},
	{0x52d3,0x20},
	{0x52d4,0x20},
	{0x52d5,0x20},
	{0x52d6,0x20},
	{0x52d7,0x20},
	{0x52d8,0x20},
	{0x52d9,0x20},
	{0x52da,0x20},
	{0x52db,0x20},
	{0x52dc,0x20},
	{0x52dd,0x20},
	{0x52de,0x20},
	{0x52df,0x20},
	{0x52f0,0x08},
	{0x52f1,0x07},
	{0x52f2,0x06},
	{0x52f3,0x05},
	{0x52f4,0x0a},
	{0x52f5,0x02},
	{0x52f6,0x01},
	{0x52f7,0x09},
	{0x52f8,0x0c},
	{0x52f9,0x04},
	{0x52fa,0x03},
	{0x52fb,0x0b},
	{0x52fc,0x10},
	{0x52fd,0x0f},
	{0x52fe,0x0e},
	{0x52ff,0x0d},
	{0x5300,0x00},
	{0x5301,0x00},
	{0x5302,0x00},
	{0x5303,0x00},
	{0x5304,0x00},
	{0x5305,0x00},
	{0x5306,0x00},
	{0x5307,0x00},
	{0x5308,0x00},
	{0x5309,0x00},
	{0x530a,0x00},
	{0x530b,0x00},
	{0x530c,0x00},
	{0x530d,0x00},
	{0x530e,0x00},
	{0x530f,0x00},
	{0x5310,0x03},
	{0x5311,0xe8},
	{0x5312,0x00},
	{0x5331,0x02},
	{0x5332,0x42},
	{0x5333,0x24},
	{0x5353,0x09},
	{0x5354,0x00},
	{0x53c1,0x00},
	{0x5414,0x01},
	{0x5480,0xc1},
	{0x548a,0x04},
	{0x54b0,0x10},
	{0x54d1,0x68},
	{0x54d2,0xff},
	{0x54d3,0x1c},
	{0x54ee,0x01},
	{0x54ef,0x02},
	{0x54f0,0x04},
	{0x5510,0x03},
	{0x5511,0xe8},
	{0x5550,0x06},
	{0x557a,0x00},
	{0x557b,0x38},
	{0x557c,0x00},
	{0x557d,0x4b},
	{0x5598,0x00},
	{0x5599,0x50},
	{0x559a,0x00},
	{0x559b,0x50},
	{0x559c,0x00},
	{0x559d,0x50},
	{0x559e,0x00},
	{0x559f,0x50},
	{0x55a0,0x00},
	{0x55a1,0x50},
	{0x55a2,0x00},
	{0x55a3,0x50},
	{0x55a4,0x00},
	{0x55a5,0x50},
	{0x55a6,0x00},
	{0x55a7,0x50},
	{0x55a8,0x00},
	{0x55a9,0x50},
	{0x55aa,0x00},
	{0x55ab,0x50},
	{0x55ac,0x00},
	{0x55ad,0x50},
	{0x55ae,0x00},
	{0x55af,0x50},
	{0x55d0,0x20},
	{0x55d1,0x20},
	{0x55d2,0x20},
	{0x55d3,0x20},
	{0x55d4,0x20},
	{0x55d5,0x20},
	{0x55d6,0x20},
	{0x55d7,0x20},
	{0x55d8,0x20},
	{0x55d9,0x20},
	{0x55da,0x20},
	{0x55db,0x20},
	{0x55dc,0x20},
	{0x55dd,0x20},
	{0x55de,0x20},
	{0x55df,0x20},
	{0x55f0,0x01},
	{0x55f1,0x02},
	{0x55f2,0x03},
	{0x55f3,0x04},
	{0x55f4,0x05},
	{0x55f5,0x06},
	{0x55f6,0x07},
	{0x55f7,0x08},
	{0x55f8,0x09},
	{0x55f9,0x0a},
	{0x55fa,0x0b},
	{0x55fb,0x0c},
	{0x55fc,0x0d},
	{0x55fd,0x0e},
	{0x55fe,0x0f},
	{0x55ff,0x10},
	{0x5600,0x11},
	{0x5601,0x12},
	{0x5602,0x13},
	{0x5603,0x14},
	{0x5604,0x15},
	{0x5605,0x16},
	{0x5606,0x17},
	{0x5607,0x18},
	{0x5608,0x19},
	{0x5609,0x1a},
	{0x560a,0x1b},
	{0x560b,0x1c},
	{0x560c,0x1d},
	{0x560d,0x1e},
	{0x560e,0x1f},
	{0x560f,0x20},
	{0x5631,0x02},
	{0x5632,0x42},
	{0x5633,0x24},
	{0x5653,0x09},
	{0x5654,0x00},
	{0x56c1,0x00},
	{0x5714,0x01},
	{0x5780,0xc1},
	{0x578a,0x04},
	{0x57b0,0x10},
	{0x57d1,0x68},
	{0x57d2,0xff},
	{0x57d3,0x1c},
	{0x57ee,0x01},
	{0x57ef,0x02},
	{0x57f0,0x04},
	{0x5810,0x03},
	{0x5811,0xe8},
	{0x5850,0x06},
	{0x587a,0x00},
	{0x587b,0x38},
	{0x587c,0x00},
	{0x587d,0x4b},
	{0x5898,0x00},
	{0x5899,0x50},
	{0x589a,0x00},
	{0x589b,0x50},
	{0x589c,0x00},
	{0x589d,0x50},
	{0x589e,0x00},
	{0x589f,0x50},
	{0x58a0,0x00},
	{0x58a1,0x50},
	{0x58a2,0x00},
	{0x58a3,0x50},
	{0x58a4,0x00},
	{0x58a5,0x50},
	{0x58a6,0x00},
	{0x58a7,0x50},
	{0x58a8,0x00},
	{0x58a9,0x50},
	{0x58aa,0x00},
	{0x58ab,0x50},
	{0x58ac,0x00},
	{0x58ad,0x50},
	{0x58ae,0x00},
	{0x58af,0x50},
	{0x58d0,0x20},
	{0x58d1,0x20},
	{0x58d2,0x20},
	{0x58d3,0x20},
	{0x58d4,0x20},
	{0x58d5,0x20},
	{0x58d6,0x20},
	{0x58d7,0x20},
	{0x58d8,0x20},
	{0x58d9,0x20},
	{0x58da,0x20},
	{0x58db,0x20},
	{0x58dc,0x20},
	{0x58dd,0x20},
	{0x58de,0x20},
	{0x58df,0x20},
	{0x58f0,0x01},
	{0x58f1,0x02},
	{0x58f2,0x03},
	{0x58f3,0x04},
	{0x58f4,0x05},
	{0x58f5,0x06},
	{0x58f6,0x07},
	{0x58f7,0x08},
	{0x58f8,0x09},
	{0x58f9,0x0a},
	{0x58fa,0x0b},
	{0x58fb,0x0c},
	{0x58fc,0x0d},
	{0x58fd,0x0e},
	{0x58fe,0x0f},
	{0x58ff,0x10},
	{0x5900,0x11},
	{0x5901,0x12},
	{0x5902,0x13},
	{0x5903,0x14},
	{0x5904,0x15},
	{0x5905,0x16},
	{0x5906,0x17},
	{0x5907,0x18},
	{0x5908,0x19},
	{0x5909,0x1a},
	{0x590a,0x1b},
	{0x590b,0x1c},
	{0x590c,0x1d},
	{0x590d,0x1e},
	{0x590e,0x1f},
	{0x590f,0x20},
	{0x5931,0x02},
	{0x5932,0x42},
	{0x5933,0x24},
	{0x5953,0x09},
	{0x5954,0x00},
	{0x5980,0x3b},
	{0x5989,0x84},
	{0x59c3,0x04},
	{0x59c4,0x24},
	{0x59c5,0x40},
	{0x59c6,0x1b},
	{0x59c7,0x40},
	{0x5a02,0x0f},
	{0x5a40,0x75},
	{0x5a41,0x75},
	{0x5a42,0x75},
	{0x5a43,0x75},
	{0x5a44,0x75},
	{0x5a45,0x75},
	{0x5a46,0x75},
	{0x5a47,0x75},
	{0x5a48,0x75},
	{0x5a49,0x75},
	{0x5a4a,0x75},
	{0x5a4b,0x75},
	{0x5a4c,0x75},
	{0x5a4d,0x75},
	{0x5a4e,0x75},
	{0x5a4f,0x75},
	{0x5a50,0x75},
	{0x5a51,0x75},
	{0x5a52,0x75},
	{0x5a53,0x75},
	{0x5a54,0x75},
	{0x5a55,0x75},
	{0x5a56,0x75},
	{0x5a57,0x75},
	{0x5a58,0x75},
	{0x5a59,0x75},
	{0x5a5a,0x75},
	{0x5a5b,0x75},
	{0x5a5c,0x75},
	{0x5a5d,0x75},
	{0x5a5e,0x75},
	{0x5a5f,0x75},
	{0x5a60,0x75},
	{0x5a61,0x75},
	{0x5a62,0x75},
	{0x5a63,0x75},
	{0x5a64,0x75},
	{0x5a65,0x75},
	{0x5a66,0x75},
	{0x5a67,0x75},
	{0x5a68,0x75},
	{0x5a69,0x75},
	{0x5a6a,0x75},
	{0x5a6b,0x75},
	{0x5a6c,0x75},
	{0x5a6d,0x75},
	{0x5a6e,0x75},
	{0x5a6f,0x75},
	{0x5a70,0x75},
	{0x5a71,0x75},
	{0x5a72,0x75},
	{0x5a73,0x75},
	{0x5a74,0x75},
	{0x5a75,0x75},
	{0x5a76,0x75},
	{0x5a77,0x75},
	{0x5a78,0x75},
	{0x5a79,0x75},
	{0x5a7a,0x75},
	{0x5a7b,0x75},
	{0x5a7c,0x75},
	{0x5a7d,0x75},
	{0x5a7e,0x75},
	{0x5a7f,0x75},
	{0x5a80,0x75},
	{0x5a81,0x75},
	{0x5a82,0x75},
	{0x5a83,0x75},
	{0x5a84,0x75},
	{0x5a85,0x75},
	{0x5a86,0x75},
	{0x5a87,0x75},
	{0x5a88,0x75},
	{0x5a89,0x75},
	{0x5a8a,0x75},
	{0x5a8b,0x75},
	{0x5a8c,0x75},
	{0x5a8d,0x75},
	{0x5a8e,0x75},
	{0x5a8f,0x75},
	{0x5a90,0x75},
	{0x5a91,0x75},
	{0x5a92,0x75},
	{0x5a93,0x75},
	{0x5a94,0x75},
	{0x5a95,0x75},
	{0x5a96,0x75},
	{0x5a97,0x75},
	{0x5a98,0x75},
	{0x5a99,0x75},
	{0x5a9a,0x75},
	{0x5a9b,0x75},
	{0x5a9c,0x75},
	{0x5a9d,0x75},
	{0x5a9e,0x75},
	{0x5a9f,0x75},
	{0x5aa0,0x75},
	{0x5aa1,0x75},
	{0x5aa2,0x75},
	{0x5aa3,0x75},
	{0x5aa4,0x75},
	{0x5aa5,0x75},
	{0x5aa6,0x75},
	{0x5aa7,0x75},
	{0x5aa8,0x75},
	{0x5aa9,0x75},
	{0x5aaa,0x75},
	{0x5aab,0x75},
	{0x5aac,0x75},
	{0x5aad,0x75},
	{0x5aae,0x75},
	{0x5aaf,0x75},
	{0x5ab0,0x75},
	{0x5ab1,0x75},
	{0x5ab2,0x75},
	{0x5ab3,0x75},
	{0x5ab4,0x75},
	{0x5ab5,0x75},
	{0x5ab6,0x75},
	{0x5ab7,0x75},
	{0x5ab8,0x75},
	{0x5ab9,0x75},
	{0x5aba,0x75},
	{0x5abb,0x75},
	{0x5abc,0x75},
	{0x5abd,0x75},
	{0x5abe,0x75},
	{0x5abf,0x75},
	{0x5ac0,0x75},
	{0x5ac1,0x75},
	{0x5ac2,0x75},
	{0x5ac3,0x75},
	{0x5ac4,0x75},
	{0x5ac5,0x75},
	{0x5ac6,0x75},
	{0x5ac7,0x75},
	{0x5ac8,0x75},
	{0x5ac9,0x75},
	{0x5aca,0x75},
	{0x5acb,0x75},
	{0x5acc,0x75},
	{0x5acd,0x75},
	{0x5ace,0x75},
	{0x5acf,0x75},
	{0x5ad0,0x75},
	{0x5ad1,0x75},
	{0x5ad2,0x75},
	{0x5ad3,0x75},
	{0x5ad4,0x75},
	{0x5ad5,0x75},
	{0x5ad6,0x75},
	{0x5ad7,0x75},
	{0x5ad8,0x75},
	{0x5ad9,0x75},
	{0x5ada,0x75},
	{0x5adb,0x75},
	{0x5adc,0x75},
	{0x5add,0x75},
	{0x5ade,0x75},
	{0x5adf,0x75},
	{0x5ae0,0x75},
	{0x5ae1,0x75},
	{0x5ae2,0x75},
	{0x5ae3,0x75},
	{0x5ae4,0x75},
	{0x5ae5,0x75},
	{0x5ae6,0x75},
	{0x5ae7,0x75},
	{0x5ae8,0x75},
	{0x5ae9,0x75},
	{0x5aea,0x75},
	{0x5aeb,0x75},
	{0x5aec,0x75},
	{0x5aed,0x75},
	{0x5aee,0x75},
	{0x5aef,0x75},
	{0x5af0,0x75},
	{0x5af1,0x75},
	{0x5af2,0x75},
	{0x5af3,0x75},
	{0x5af4,0x75},
	{0x5af5,0x75},
	{0x5af6,0x75},
	{0x5af7,0x75},
	{0x5af8,0x75},
	{0x5af9,0x75},
	{0x5afa,0x75},
	{0x5afb,0x75},
	{0x5afc,0x75},
	{0x5afd,0x75},
	{0x5afe,0x75},
	{0x5aff,0x75},
	{0x5b00,0x75},
	{0x5b01,0x75},
	{0x5b02,0x75},
	{0x5b03,0x75},
	{0x5b04,0x75},
	{0x5b05,0x75},
	{0x5b06,0x75},
	{0x5b07,0x75},
	{0x5b08,0x75},
	{0x5b09,0x75},
	{0x5b0a,0x75},
	{0x5b0b,0x75},
	{0x5b0c,0x75},
	{0x5b0d,0x75},
	{0x5b0e,0x75},
	{0x5b0f,0x75},
	{0x5b10,0x75},
	{0x5b11,0x75},
	{0x5b12,0x75},
	{0x5b13,0x75},
	{0x5b14,0x75},
	{0x5b15,0x75},
	{0x5b16,0x75},
	{0x5b17,0x75},
	{0x5b18,0x75},
	{0x5b19,0x75},
	{0x5b1a,0x75},
	{0x5b1b,0x75},
	{0x5b1c,0x75},
	{0x5b1d,0x75},
	{0x5b1e,0x75},
	{0x5b1f,0x75},
	{0x5b20,0x75},
	{0x5b21,0x75},
	{0x5b22,0x75},
	{0x5b23,0x75},
	{0x5b24,0x75},
	{0x5b25,0x75},
	{0x5b26,0x75},
	{0x5b27,0x75},
	{0x5b28,0x75},
	{0x5b29,0x75},
	{0x5b2a,0x75},
	{0x5b2b,0x75},
	{0x5b2c,0x75},
	{0x5b2d,0x75},
	{0x5b2e,0x75},
	{0x5b2f,0x75},
	{0x5b30,0x75},
	{0x5b31,0x75},
	{0x5b32,0x75},
	{0x5b33,0x75},
	{0x5b34,0x75},
	{0x5b35,0x75},
	{0x5b36,0x75},
	{0x5b37,0x75},
	{0x5b38,0x75},
	{0x5b39,0x75},
	{0x5b3a,0x75},
	{0x5b3b,0x75},
	{0x5b3c,0x75},
	{0x5b3d,0x75},
	{0x5b3e,0x75},
	{0x5b3f,0x75},
	{0x5b40,0x75},
	{0x5b41,0x75},
	{0x5b42,0x75},
	{0x5b43,0x75},
	{0x5b44,0x75},
	{0x5b45,0x75},
	{0x5b46,0x75},
	{0x5b47,0x75},
	{0x5b48,0x75},
	{0x5b49,0x75},
	{0x5b4a,0x75},
	{0x5b4b,0x75},
	{0x5b4c,0x75},
	{0x5b4d,0x75},
	{0x5b4e,0x75},
	{0x5b4f,0x75},
	{0x5b50,0x75},
	{0x5b51,0x75},
	{0x5b52,0x75},
	{0x5b53,0x75},
	{0x5b54,0x75},
	{0x5b55,0x75},
	{0x5b56,0x75},
	{0x5b57,0x75},
	{0x5b58,0x75},
	{0x5b59,0x75},
	{0x5b5a,0x75},
	{0x5b5b,0x75},
	{0x5b5c,0x75},
	{0x5b5d,0x75},
	{0x5b5e,0x75},
	{0x5b5f,0x75},
	{0x5b80,0x75},
	{0x5b81,0x75},
	{0x5b82,0x75},
	{0x5b83,0x75},
	{0x5b84,0x75},
	{0x5b85,0x75},
	{0x5b86,0x75},
	{0x5b87,0x75},
	{0x5b88,0x75},
	{0x5b89,0x75},
	{0x5b8a,0x75},
	{0x5b8b,0x75},
	{0x5b8c,0x75},
	{0x5b8d,0x75},
	{0x5b8e,0x75},
	{0x5b8f,0x75},
	{0x5b90,0x75},
	{0x5b91,0x75},
	{0x5b92,0x75},
	{0x5b93,0x75},
	{0x5b94,0x75},
	{0x5b95,0x75},
	{0x5b96,0x75},
	{0x5b97,0x75},
	{0x5b98,0x75},
	{0x5b99,0x75},
	{0x5b9a,0x75},
	{0x5b9b,0x75},
	{0x5b9c,0x75},
	{0x5b9d,0x75},
	{0x5b9e,0x75},
	{0x5b9f,0x75},
	{0x5ba0,0x75},
	{0x5ba1,0x75},
	{0x5ba2,0x75},
	{0x5ba3,0x75},
	{0x5ba4,0x75},
	{0x5ba5,0x75},
	{0x5ba6,0x75},
	{0x5ba7,0x75},
	{0x5ba8,0x75},
	{0x5ba9,0x75},
	{0x5baa,0x75},
	{0x5bab,0x75},
	{0x5bac,0x75},
	{0x5bad,0x75},
	{0x5bae,0x75},
	{0x5baf,0x75},
	{0x5bb0,0x75},
	{0x5bb1,0x75},
	{0x5bb2,0x75},
	{0x5bb3,0x75},
	{0x5bb4,0x75},
	{0x5bb5,0x75},
	{0x5bb6,0x75},
	{0x5bb7,0x75},
	{0x5bb8,0x75},
	{0x5bb9,0x75},
	{0x5bba,0x75},
	{0x5bbb,0x75},
	{0x5bbc,0x75},
	{0x5bbd,0x75},
	{0x5bbe,0x75},
	{0x5bbf,0x75},
	{0x5bc0,0x75},
	{0x5bc1,0x75},
	{0x5bc2,0x75},
	{0x5bc3,0x75},
	{0x5bc4,0x75},
	{0x5bc5,0x75},
	{0x5bc6,0x75},
	{0x5bc7,0x75},
	{0x5bc8,0x75},
	{0x5bc9,0x75},
	{0x5bca,0x75},
	{0x5bcb,0x75},
	{0x5bcc,0x75},
	{0x5bcd,0x75},
	{0x5bce,0x75},
	{0x5bcf,0x75},
	{0x5bd0,0x75},
	{0x5bd1,0x75},
	{0x5bd2,0x75},
	{0x5bd3,0x75},
	{0x5bd4,0x75},
	{0x5bd5,0x75},
	{0x5bd6,0x75},
	{0x5bd7,0x75},
	{0x5bd8,0x75},
	{0x5bd9,0x75},
	{0x5bda,0x75},
	{0x5bdb,0x75},
	{0x5bdc,0x75},
	{0x5bdd,0x75},
	{0x5bde,0x75},
	{0x5bdf,0x75},
	{0x5be0,0x75},
	{0x5be1,0x75},
	{0x5be2,0x75},
	{0x5be3,0x75},
	{0x5be4,0x75},
	{0x5be5,0x75},
	{0x5be6,0x75},
	{0x5be7,0x75},
	{0x5be8,0x75},
	{0x5be9,0x75},
	{0x5bea,0x75},
	{0x5beb,0x75},
	{0x5bec,0x75},
	{0x5bed,0x75},
	{0x5bee,0x75},
	{0x5bef,0x75},
	{0x5bf0,0x75},
	{0x5bf1,0x75},
	{0x5bf2,0x75},
	{0x5bf3,0x75},
	{0x5bf4,0x75},
	{0x5bf5,0x75},
	{0x5bf6,0x75},
	{0x5bf7,0x75},
	{0x5bf8,0x75},
	{0x5bf9,0x75},
	{0x5bfa,0x75},
	{0x5bfb,0x75},
	{0x5bfc,0x75},
	{0x5bfd,0x75},
	{0x5bfe,0x75},
	{0x5bff,0x75},
	{0x5c00,0x75},
	{0x5c01,0x75},
	{0x5c02,0x75},
	{0x5c03,0x75},
	{0x5c04,0x75},
	{0x5c05,0x75},
	{0x5c06,0x75},
	{0x5c07,0x75},
	{0x5c08,0x75},
	{0x5c09,0x75},
	{0x5c0a,0x75},
	{0x5c0b,0x75},
	{0x5c0c,0x75},
	{0x5c0d,0x75},
	{0x5c0e,0x75},
	{0x5c0f,0x75},
	{0x5c10,0x75},
	{0x5c11,0x75},
	{0x5c12,0x75},
	{0x5c13,0x75},
	{0x5c14,0x75},
	{0x5c15,0x75},
	{0x5c16,0x75},
	{0x5c17,0x75},
	{0x5c18,0x75},
	{0x5c19,0x75},
	{0x5c1a,0x75},
	{0x5c1b,0x75},
	{0x5c1c,0x75},
	{0x5c1d,0x75},
	{0x5c1e,0x75},
	{0x5c1f,0x75},
	{0x5c20,0x75},
	{0x5c21,0x75},
	{0x5c22,0x75},
	{0x5c23,0x75},
	{0x5c24,0x75},
	{0x5c25,0x75},
	{0x5c26,0x75},
	{0x5c27,0x75},
	{0x5c28,0x75},
	{0x5c29,0x75},
	{0x5c2a,0x75},
	{0x5c2b,0x75},
	{0x5c2c,0x75},
	{0x5c2d,0x75},
	{0x5c2e,0x75},
	{0x5c2f,0x75},
	{0x5c30,0x75},
	{0x5c31,0x75},
	{0x5c32,0x75},
	{0x5c33,0x75},
	{0x5c34,0x75},
	{0x5c35,0x75},
	{0x5c36,0x75},
	{0x5c37,0x75},
	{0x5c38,0x75},
	{0x5c39,0x75},
	{0x5c3a,0x75},
	{0x5c3b,0x75},
	{0x5c3c,0x75},
	{0x5c3d,0x75},
	{0x5c3e,0x75},
	{0x5c3f,0x75},
	{0x5c40,0x75},
	{0x5c41,0x75},
	{0x5c42,0x75},
	{0x5c43,0x75},
	{0x5c44,0x75},
	{0x5c45,0x75},
	{0x5c46,0x75},
	{0x5c47,0x75},
	{0x5c48,0x75},
	{0x5c49,0x75},
	{0x5c4a,0x75},
	{0x5c4b,0x75},
	{0x5c4c,0x75},
	{0x5c4d,0x75},
	{0x5c4e,0x75},
	{0x5c4f,0x75},
	{0x5c50,0x75},
	{0x5c51,0x75},
	{0x5c52,0x75},
	{0x5c53,0x75},
	{0x5c54,0x75},
	{0x5c55,0x75},
	{0x5c56,0x75},
	{0x5c57,0x75},
	{0x5c58,0x75},
	{0x5c59,0x75},
	{0x5c5a,0x75},
	{0x5c5b,0x75},
	{0x5c5c,0x75},
	{0x5c5d,0x75},
	{0x5c5e,0x75},
	{0x5c5f,0x75},
	{0x5c60,0x75},
	{0x5c61,0x75},
	{0x5c62,0x75},
	{0x5c63,0x75},
	{0x5c64,0x75},
	{0x5c65,0x75},
	{0x5c66,0x75},
	{0x5c67,0x75},
	{0x5c68,0x75},
	{0x5c69,0x75},
	{0x5c6a,0x75},
	{0x5c6b,0x75},
	{0x5c6c,0x75},
	{0x5c6d,0x75},
	{0x5c6e,0x75},
	{0x5c6f,0x75},
	{0x5c70,0x75},
	{0x5c71,0x75},
	{0x5c72,0x75},
	{0x5c73,0x75},
	{0x5c74,0x75},
	{0x5c75,0x75},
	{0x5c76,0x75},
	{0x5c77,0x75},
	{0x5c78,0x75},
	{0x5c79,0x75},
	{0x5c7a,0x75},
	{0x5c7b,0x75},
	{0x5c7c,0x75},
	{0x5c7d,0x75},
	{0x5c7e,0x75},
	{0x5c7f,0x75},
	{0x5c80,0x75},
	{0x5c81,0x75},
	{0x5c82,0x75},
	{0x5c83,0x75},
	{0x5c84,0x75},
	{0x5c85,0x75},
	{0x5c86,0x75},
	{0x5c87,0x75},
	{0x5c88,0x75},
	{0x5c89,0x75},
	{0x5c8a,0x75},
	{0x5c8b,0x75},
	{0x5c8c,0x75},
	{0x5c8d,0x75},
	{0x5c8e,0x75},
	{0x5c8f,0x75},
	{0x5c90,0x75},
	{0x5c91,0x75},
	{0x5c92,0x75},
	{0x5c93,0x75},
	{0x5c94,0x75},
	{0x5c95,0x75},
	{0x5c96,0x75},
	{0x5c97,0x75},
	{0x5c98,0x75},
	{0x5c99,0x75},
	{0x5c9a,0x75},
	{0x5c9b,0x75},
	{0x5c9c,0x75},
	{0x5c9d,0x75},
	{0x5c9e,0x75},
	{0x5c9f,0x75},
	{0x5ca0,0x75},
	{0x5ca1,0x75},
	{0x5ca2,0x75},
	{0x5ca3,0x75},
	{0x5ca4,0x75},
	{0x5ca5,0x75},
	{0x5ca6,0x75},
	{0x5ca7,0x75},
	{0x5ca8,0x75},
	{0x5ca9,0x75},
	{0x5caa,0x75},
	{0x5cab,0x75},
	{0x5cac,0x75},
	{0x5cad,0x75},
	{0x5cae,0x75},
	{0x5caf,0x75},
	{0x5cb0,0x75},
	{0x5cb1,0x75},
	{0x5cb2,0x75},
	{0x5cb3,0x75},
	{0x5cb4,0x75},
	{0x5cb5,0x75},
	{0x5cb6,0x75},
	{0x5cb7,0x75},
	{0x5cb8,0x75},
	{0x5cb9,0x75},
	{0x5cba,0x75},
	{0x5cbb,0x75},
	{0x5cbc,0x75},
	{0x5cbd,0x75},
	{0x5cbe,0x75},
	{0x5cbf,0x75},
	{0x5cc0,0x75},
	{0x5cc1,0x75},
	{0x5cc2,0x75},
	{0x5cc3,0x75},
	{0x5cc4,0x75},
	{0x5cc5,0x75},
	{0x5cc6,0x75},
	{0x5cc7,0x75},
	{0x5cc8,0x75},
	{0x5cc9,0x75},
	{0x5cca,0x75},
	{0x5ccb,0x75},
	{0x5ccc,0x75},
	{0x5ccd,0x75},
	{0x5cce,0x75},
	{0x5ccf,0x75},
	{0x5cd0,0x75},
	{0x5cd1,0x75},
	{0x5cd2,0x75},
	{0x5cd3,0x75},
	{0x5cd4,0x75},
	{0x5cd5,0x75},
	{0x5cd6,0x75},
	{0x5cd7,0x75},
	{0x5cd8,0x75},
	{0x5cd9,0x75},
	{0x5cda,0x75},
	{0x5cdb,0x75},
	{0x5cdc,0x75},
	{0x5cdd,0x75},
	{0x5cde,0x75},
	{0x5cdf,0x75},
	{0x5ce0,0x75},
	{0x5ce1,0x75},
	{0x5ce2,0x75},
	{0x5ce3,0x75},
	{0x5ce4,0x75},
	{0x5ce5,0x75},
	{0x5ce6,0x75},
	{0x5ce7,0x75},
	{0x5ce8,0x75},
	{0x5ce9,0x75},
	{0x5cea,0x75},
	{0x5ceb,0x75},
	{0x5cec,0x75},
	{0x5ced,0x75},
	{0x5cee,0x75},
	{0x5cef,0x75},
	{0x5cf0,0x75},
	{0x5cf1,0x75},
	{0x5cf2,0x75},
	{0x5cf3,0x75},
	{0x5cf4,0x75},
	{0x5cf5,0x75},
	{0x5cf6,0x75},
	{0x5cf7,0x75},
	{0x5cf8,0x75},
	{0x5cf9,0x75},
	{0x5cfa,0x75},
	{0x5cfb,0x75},
	{0x5cfc,0x75},
	{0x5cfd,0x75},
	{0x5cfe,0x75},
	{0x5cff,0x75},
	{0x5d00,0x75},
	{0x5d01,0x75},
	{0x5d02,0x75},
	{0x5d03,0x75},
	{0x5d04,0x75},
	{0x5d05,0x75},
	{0x5d06,0x75},
	{0x5d07,0x75},
	{0x5d08,0x75},
	{0x5d09,0x75},
	{0x5d0a,0x75},
	{0x5d0b,0x75},
	{0x5d0c,0x75},
	{0x5d0d,0x75},
	{0x5d0e,0x75},
	{0x5d0f,0x75},
	{0x5d10,0x75},
	{0x5d11,0x75},
	{0x5d12,0x75},
	{0x5d13,0x75},
	{0x5d14,0x75},
	{0x5d15,0x75},
	{0x5d16,0x75},
	{0x5d17,0x75},
	{0x5d18,0x75},
	{0x5d19,0x75},
	{0x5d1a,0x75},
	{0x5d1b,0x75},
	{0x5d1c,0x75},
	{0x5d1d,0x75},
	{0x5d1e,0x75},
	{0x5d1f,0x75},
	{0x5d20,0x75},
	{0x5d21,0x75},
	{0x5d22,0x75},
	{0x5d23,0x75},
	{0x5d24,0x75},
	{0x5d25,0x75},
	{0x5d26,0x75},
	{0x5d27,0x75},
	{0x5d28,0x75},
	{0x5d29,0x75},
	{0x5d2a,0x75},
	{0x5d2b,0x75},
	{0x5d2c,0x75},
	{0x5d2d,0x75},
	{0x5d2e,0x75},
	{0x5d2f,0x75},
	{0x5d30,0x75},
	{0x5d31,0x75},
	{0x5d32,0x75},
	{0x5d33,0x75},
	{0x5d34,0x75},
	{0x5d35,0x75},
	{0x5d36,0x75},
	{0x5d37,0x75},
	{0x5d38,0x75},
	{0x5d39,0x75},
	{0x5d3a,0x75},
	{0x5d3b,0x75},
	{0x5d3c,0x75},
	{0x5d3d,0x75},
	{0x5d3e,0x75},
	{0x5d3f,0x75},
	{0x5d40,0x75},
	{0x5d41,0x75},
	{0x5d42,0x75},
	{0x5d43,0x75},
	{0x5d44,0x75},
	{0x5d45,0x75},
	{0x5d46,0x75},
	{0x5d47,0x75},
	{0x5d48,0x75},
	{0x5d49,0x75},
	{0x5d4a,0x75},
	{0x5d4b,0x75},
	{0x5d4c,0x75},
	{0x5d4d,0x75},
	{0x5d4e,0x75},
	{0x5d4f,0x75},
	{0x5d50,0x75},
	{0x5d51,0x75},
	{0x5d52,0x75},
	{0x5d53,0x75},
	{0x5d54,0x75},
	{0x5d55,0x75},
	{0x5d56,0x75},
	{0x5d57,0x75},
	{0x5d58,0x75},
	{0x5d59,0x75},
	{0x5d5a,0x75},
	{0x5d5b,0x75},
	{0x5d5c,0x75},
	{0x5d5d,0x75},
	{0x5d5e,0x75},
	{0x5d5f,0x75},
	{0x5d60,0x75},
	{0x5d61,0x75},
	{0x5d62,0x75},
	{0x5d63,0x75},
	{0x5d64,0x75},
	{0x5d65,0x75},
	{0x5d66,0x75},
	{0x5d67,0x75},
	{0x5d68,0x75},
	{0x5d69,0x75},
	{0x5d6a,0x75},
	{0x5d6b,0x75},
	{0x5d6c,0x75},
	{0x5d6d,0x75},
	{0x5d6e,0x75},
	{0x5d6f,0x75},
	{0x5d70,0x75},
	{0x5d71,0x75},
	{0x5d72,0x75},
	{0x5d73,0x75},
	{0x5d74,0x75},
	{0x5d75,0x75},
	{0x5d76,0x75},
	{0x5d77,0x75},
	{0x5d78,0x75},
	{0x5d79,0x75},
	{0x5d7a,0x75},
	{0x5d7b,0x75},
	{0x5d7c,0x75},
	{0x5d7d,0x75},
	{0x5d7e,0x75},
	{0x5d7f,0x75},
	{0x5d80,0x75},
	{0x5d81,0x75},
	{0x5d82,0x75},
	{0x5d83,0x75},
	{0x5d84,0x75},
	{0x5d85,0x75},
	{0x5d86,0x75},
	{0x5d87,0x75},
	{0x5d88,0x75},
	{0x5d89,0x75},
	{0x5d8a,0x75},
	{0x5d8b,0x75},
	{0x5d8c,0x75},
	{0x5d8d,0x75},
	{0x5d8e,0x75},
	{0x5d8f,0x75},
	{0x5d90,0x75},
	{0x5d91,0x75},
	{0x5d92,0x75},
	{0x5d93,0x75},
	{0x5d94,0x75},
	{0x5d95,0x75},
	{0x5d96,0x75},
	{0x5d97,0x75},
	{0x5d98,0x75},
	{0x5d99,0x75},
	{0x5d9a,0x75},
	{0x5d9b,0x75},
	{0x5d9c,0x75},
	{0x5d9d,0x75},
	{0x5d9e,0x75},
	{0x5d9f,0x75},
	{0x5da0,0x75},
	{0x5da1,0x75},
	{0x5da2,0x75},
	{0x5da3,0x75},
	{0x5da4,0x75},
	{0x5da5,0x75},
	{0x5da6,0x75},
	{0x5da7,0x75},
	{0x5da8,0x75},
	{0x5da9,0x75},
	{0x5daa,0x75},
	{0x5dab,0x75},
	{0x5dac,0x75},
	{0x5dad,0x75},
	{0x5dae,0x75},
	{0x5daf,0x75},
	{0x5db0,0x75},
	{0x5db1,0x75},
	{0x5db2,0x75},
	{0x5db3,0x75},
	{0x5db4,0x75},
	{0x5db5,0x75},
	{0x5db6,0x75},
	{0x5db7,0x75},
	{0x5db8,0x75},
	{0x5db9,0x75},
	{0x5dba,0x75},
	{0x5dbb,0x75},
	{0x5dbc,0x75},
	{0x5dbd,0x75},
	{0x5dbe,0x75},
	{0x5dbf,0x75},
	{0x5dc0,0x75},
	{0x5dc1,0x75},
	{0x5dc2,0x75},
	{0x5dc3,0x75},
	{0x5dc4,0x75},
	{0x5dc5,0x75},
	{0x5dc6,0x75},
	{0x5dc7,0x75},
	{0x5dc8,0x75},
	{0x5dc9,0x75},
	{0x5dca,0x75},
	{0x5dcb,0x75},
	{0x5dcc,0x75},
	{0x5dcd,0x75},
	{0x5dce,0x75},
	{0x5dcf,0x75},
	{0x5dd0,0x75},
	{0x5dd1,0x75},
	{0x5dd2,0x75},
	{0x5dd3,0x75},
	{0x5dd4,0x75},
	{0x5dd5,0x75},
	{0x5dd6,0x75},
	{0x5dd7,0x75},
	{0x5dd8,0x75},
	{0x5dd9,0x75},
	{0x5dda,0x75},
	{0x5ddb,0x75},
	{0x5ddc,0x75},
	{0x5ddd,0x75},
	{0x5dde,0x75},
	{0x5ddf,0x75},
	{0x5de0,0x75},
	{0x5de1,0x75},
	{0x5de2,0x75},
	{0x5de3,0x75},
	{0x5de4,0x75},
	{0x5de5,0x75},
	{0x5de6,0x75},
	{0x5de7,0x75},
	{0x5de8,0x75},
	{0x5de9,0x75},
	{0x5dea,0x75},
	{0x5deb,0x75},
	{0x5dec,0x75},
	{0x5ded,0x75},
	{0x5dee,0x75},
	{0x5def,0x75},
	{0x5df0,0x75},
	{0x5df1,0x75},
	{0x5df2,0x75},
	{0x5df3,0x75},
	{0x5df4,0x75},
	{0x5df5,0x75},
	{0x5df6,0x75},
	{0x5df7,0x75},
	{0x5df8,0x75},
	{0x5df9,0x75},
	{0x5dfa,0x75},
	{0x5dfb,0x75},
	{0x5dfc,0x75},
	{0x5dfd,0x75},
	{0x5dfe,0x75},
	{0x5dff,0x75},
	{0x5e00,0x75},
	{0x5e01,0x75},
	{0x5e02,0x75},
	{0x5e03,0x75},
	{0x5e04,0x75},
	{0x5e05,0x75},
	{0x5e06,0x75},
	{0x5e07,0x75},
	{0x5e08,0x75},
	{0x5e09,0x75},
	{0x5e0a,0x75},
	{0x5e0b,0x75},
	{0x5e0c,0x75},
	{0x5e0d,0x75},
	{0x5e0e,0x75},
	{0x5e0f,0x75},
	{0x5e10,0x75},
	{0x5e11,0x75},
	{0x5e12,0x75},
	{0x5e13,0x75},
	{0x5e14,0x75},
	{0x5e15,0x75},
	{0x5e16,0x75},
	{0x5e17,0x75},
	{0x5e18,0x75},
	{0x5e19,0x75},
	{0x5e1a,0x75},
	{0x5e1b,0x75},
	{0x5e1c,0x75},
	{0x5e1d,0x75},
	{0x5e1e,0x75},
	{0x5e1f,0x75},
	{0x5e20,0x75},
	{0x5e21,0x75},
	{0x5e22,0x75},
	{0x5e23,0x75},
	{0x5e24,0x75},
	{0x5e25,0x75},
	{0x5e26,0x75},
	{0x5e27,0x75},
	{0x5e28,0x75},
	{0x5e29,0x75},
	{0x5e2a,0x75},
	{0x5e2b,0x75},
	{0x5e2c,0x75},
	{0x5e2d,0x75},
	{0x5e2e,0x75},
	{0x5e2f,0x75},
	{0x5e30,0x75},
	{0x5e31,0x75},
	{0x5e32,0x75},
	{0x5e33,0x75},
	{0x5e34,0x75},
	{0x5e35,0x75},
	{0x5e36,0x75},
	{0x5e37,0x75},
	{0x5e38,0x75},
	{0x5e39,0x75},
	{0x5e3a,0x75},
	{0x5e3b,0x75},
	{0x5e3c,0x75},
	{0x5e3d,0x75},
	{0x5e3e,0x75},
	{0x5e3f,0x75},
	{0x5e40,0x75},
	{0x5e41,0x75},
	{0x5e42,0x75},
	{0x5e43,0x75},
	{0x5e44,0x75},
	{0x5e45,0x75},
	{0x5e46,0x75},
	{0x5e47,0x75},
	{0x5e48,0x75},
	{0x5e49,0x75},
	{0x5e4a,0x75},
	{0x5e4b,0x75},
	{0x5e4c,0x75},
	{0x5e4d,0x75},
	{0x5e4e,0x75},
	{0x5e4f,0x75},
	{0x5e50,0x75},
	{0x5e51,0x75},
	{0x5e52,0x75},
	{0x5e53,0x75},
	{0x5e54,0x75},
	{0x5e55,0x75},
	{0x5e56,0x75},
	{0x5e57,0x75},
	{0x5e58,0x75},
	{0x5e59,0x75},
	{0x5e5a,0x75},
	{0x5e5b,0x75},
	{0x5e5c,0x75},
	{0x5e5d,0x75},
	{0x5e5e,0x75},
	{0x5e5f,0x75},
	{0x5e60,0x75},
	{0x5e61,0x75},
	{0x5e62,0x75},
	{0x5e63,0x75},
	{0x5e64,0x75},
	{0x5e65,0x75},
	{0x5e66,0x75},
	{0x5e67,0x75},
	{0x5e68,0x75},
	{0x5e69,0x75},
	{0x5e6a,0x75},
	{0x5e6b,0x75},
	{0x5e6c,0x75},
	{0x5e6d,0x75},
	{0x5e6e,0x75},
	{0x5e6f,0x75},
	{0x5e70,0x75},
	{0x5e71,0x75},
	{0x5e72,0x75},
	{0x5e73,0x75},
	{0x5e74,0x75},
	{0x5e75,0x75},
	{0x5e76,0x75},
	{0x5e77,0x75},
	{0x5e78,0x75},
	{0x5e79,0x75},
	{0x5e7a,0x75},
	{0x5e7b,0x75},
	{0x5e7c,0x75},
	{0x5e7d,0x75},
	{0x5e7e,0x75},
	{0x5e7f,0x75},
	{0x5e80,0x75},
	{0x5e81,0x75},
	{0x5e82,0x75},
	{0x5e83,0x75},
	{0x5e84,0x75},
	{0x5e85,0x75},
	{0x5e86,0x75},
	{0x5e87,0x75},
	{0x5e88,0x75},
	{0x5e89,0x75},
	{0x5e8a,0x75},
	{0x5e8b,0x75},
	{0x5e8c,0x75},
	{0x5e8d,0x75},
	{0x5e8e,0x75},
	{0x5e8f,0x75},
	{0x5e90,0x75},
	{0x5e91,0x75},
	{0x5e92,0x75},
	{0x5e93,0x75},
	{0x5e94,0x75},
	{0x5e95,0x75},
	{0x5e96,0x75},
	{0x5e97,0x75},
	{0x5e98,0x75},
	{0x5e99,0x75},
	{0x5e9a,0x75},
	{0x5e9b,0x75},
	{0x5e9c,0x75},
	{0x5e9d,0x75},
	{0x5e9e,0x75},
	{0x5e9f,0x75},
	{0x5ea0,0x75},
	{0x5ea1,0x75},
	{0x5ea2,0x75},
	{0x5ea3,0x75},
	{0x5ea4,0x75},
	{0x5ea5,0x75},
	{0x5ea6,0x75},
	{0x5ea7,0x75},
	{0x5ea8,0x75},
	{0x5ea9,0x75},
	{0x5eaa,0x75},
	{0x5eab,0x75},
	{0x5eac,0x75},
	{0x5ead,0x75},
	{0x5eae,0x75},
	{0x5eaf,0x75},
	{0x5eb0,0x75},
	{0x5eb1,0x75},
	{0x5eb2,0x75},
	{0x5eb3,0x75},
	{0x5eb4,0x75},
	{0x5eb5,0x75},
	{0x5eb6,0x75},
	{0x5eb7,0x75},
	{0x5eb8,0x75},
	{0x5eb9,0x75},
	{0x5eba,0x75},
	{0x5ebb,0x75},
	{0x5ebc,0x75},
	{0x5ebd,0x75},
	{0x5ebe,0x75},
	{0x5ebf,0x75},
	{0x5ec0,0x75},
	{0x5ec1,0x75},
	{0x5ec2,0x75},
	{0x5ec3,0x75},
	{0x5ec4,0x75},
	{0x5ec5,0x75},
	{0x5ec6,0x75},
	{0x5ec7,0x75},
	{0x5ec8,0x75},
	{0x5ec9,0x75},
	{0x5eca,0x75},
	{0x5ecb,0x75},
	{0x5ecc,0x75},
	{0x5ecd,0x75},
	{0x5ece,0x75},
	{0x5ecf,0x75},
	{0x5ed0,0x75},
	{0x5ed1,0x75},
	{0x5ed2,0x75},
	{0x5ed3,0x75},
	{0x5ed4,0x75},
	{0x5ed5,0x75},
	{0x5ed6,0x75},
	{0x5ed7,0x75},
	{0x5ed8,0x75},
	{0x5ed9,0x75},
	{0x5eda,0x75},
	{0x5edb,0x75},
	{0x5edc,0x75},
	{0x5edd,0x75},
	{0x5ede,0x75},
	{0x5edf,0x75},
	{0x5f00,0x29},
	{0x5f02,0x04},
	{0x5f2d,0x28},
	{0x5f2e,0x28},
	{0x5f80,0x40},
	{0x5f81,0x40},
	{0x5f82,0x40},
	{0x5f83,0x40},
	{0x5f84,0x40},
	{0x5f85,0x40},
	{0x5f86,0x40},
	{0x5f87,0x40},
	{0x5f88,0x40},
	{0x5f89,0x40},
	{0x5f8a,0x40},
	{0x5f8b,0x40},
	{0x5f8c,0x40},
	{0x5f8d,0x40},
	{0x5f8e,0x40},
	{0x5f8f,0x40},
	{0x5f90,0x40},
	{0x5f91,0x40},
	{0x5f92,0x40},
	{0x5f93,0x40},
	{0x5f94,0x40},
	{0x5f95,0x40},
	{0x5f96,0x40},
	{0x5f97,0x40},
	{0x5f98,0x40},
	{0x5f99,0x40},
	{0x5f9a,0x40},
	{0x5f9b,0x40},
	{0x5f9c,0x40},
	{0x5f9d,0x40},
	{0x5f9e,0x40},
	{0x5f9f,0x40},
	{0x5fa0,0x40},
	{0x5fa1,0x40},
	{0x5fa2,0x40},
	{0x5fa3,0x40},
	{0x5fa4,0x40},
	{0x5fa5,0x40},
	{0x5fa6,0x40},
	{0x5fa7,0x40},
	{0x5fa8,0x40},
	{0x5fa9,0x40},
	{0x5faa,0x40},
	{0x5fab,0x40},
	{0x5fac,0x40},
	{0x5fad,0x40},
	{0x5fae,0x40},
	{0x5faf,0x40},
	{0x5fb0,0x40},
	{0x5fb1,0x40},
	{0x5fb2,0x40},
	{0x5fb3,0x40},
	{0x5fb4,0x40},
	{0x5fb5,0x40},
	{0x5fb6,0x40},
	{0x5fb7,0x40},
	{0x5fb8,0x40},
	{0x5fb9,0x40},
	{0x5fba,0x40},
	{0x5fbb,0x40},
	{0x5fbc,0x40},
	{0x5fbd,0x40},
	{0x5fbe,0x40},
	{0x5fbf,0x40},
	{0x5fc0,0x40},
	{0x5fc1,0x40},
	{0x5fc2,0x40},
	{0x5fc3,0x40},
	{0x5fc4,0x40},
	{0x5fc5,0x40},
	{0x5fc6,0x40},
	{0x5fc7,0x40},
	{0x5fc8,0x40},
	{0x5fc9,0x40},
	{0x5fca,0x40},
	{0x5fcb,0x40},
	{0x5fcc,0x40},
	{0x5fcd,0x40},
	{0x5fce,0x40},
	{0x5fcf,0x40},
	{0x5fd0,0x40},
	{0x5fd1,0x40},
	{0x5fd2,0x40},
	{0x5fd3,0x40},
	{0x5fd4,0x40},
	{0x5fd5,0x40},
	{0x5fd6,0x40},
	{0x5fd7,0x40},
	{0x5fd8,0x40},
	{0x5fd9,0x40},
	{0x5fda,0x40},
	{0x5fdb,0x40},
	{0x5fdc,0x40},
	{0x5fdd,0x40},
	{0x5fde,0x40},
	{0x5fdf,0x40},
	{0x5fe0,0x40},
	{0x5fe1,0x40},
	{0x5fe2,0x40},
	{0x5fe3,0x40},
	{0x5fe4,0x40},
	{0x5fe5,0x40},
	{0x5fe6,0x40},
	{0x5fe7,0x40},
	{0x5fe8,0x40},
	{0x5fe9,0x40},
	{0x5fea,0x40},
	{0x5feb,0x40},
	{0x5fec,0x40},
	{0x5fed,0x40},
	{0x5fee,0x40},
	{0x5fef,0x40},
	{0x5ff0,0x40},
	{0x5ff1,0x40},
	{0x5ff2,0x40},
	{0x5ff3,0x40},
	{0x5ff4,0x40},
	{0x5ff5,0x40},
	{0x5ff6,0x40},
	{0x5ff7,0x40},
	{0x5ff8,0x40},
	{0x5ff9,0x40},
	{0x5ffa,0x40},
	{0x5ffb,0x40},
	{0x5ffc,0x40},
	{0x5ffd,0x40},
	{0x5ffe,0x40},
	{0x5fff,0x40},
	{0x6000,0x40},
	{0x6001,0x40},
	{0x6002,0x40},
	{0x6003,0x40},
	{0x6004,0x40},
	{0x6005,0x40},
	{0x6006,0x40},
	{0x6007,0x40},
	{0x6008,0x40},
	{0x6009,0x40},
	{0x600a,0x40},
	{0x600b,0x40},
	{0x600c,0x40},
	{0x600d,0x40},
	{0x600e,0x40},
	{0x600f,0x40},
	{0x6010,0x40},
	{0x6011,0x40},
	{0x6012,0x40},
	{0x6013,0x40},
	{0x6014,0x40},
	{0x6015,0x40},
	{0x6016,0x40},
	{0x6017,0x40},
	{0x6018,0x40},
	{0x6019,0x40},
	{0x601a,0x40},
	{0x601b,0x40},
	{0x601c,0x40},
	{0x601d,0x40},
	{0x601e,0x40},
	{0x601f,0x40},
	{0x6020,0x40},
	{0x6021,0x40},
	{0x6022,0x40},
	{0x6023,0x40},
	{0x6024,0x40},
	{0x6025,0x40},
	{0x6026,0x40},
	{0x6027,0x40},
	{0x6028,0x40},
	{0x6029,0x40},
	{0x602a,0x40},
	{0x602b,0x40},
	{0x602c,0x40},
	{0x602d,0x40},
	{0x602e,0x40},
	{0x602f,0x40},
	{0x6030,0x40},
	{0x6031,0x40},
	{0x6032,0x40},
	{0x6033,0x40},
	{0x6034,0xcd},
	{0x6035,0xcd},
	{0x6036,0xcd},
	{0x6037,0xcd},
	{0x6038,0xcd},
	{0x6039,0xcd},
	{0x603a,0xcd},
	{0x603b,0xcd},
	{0x603c,0xcd},
	{0x603d,0xcd},
	{0x603e,0xcd},
	{0x603f,0xcd},
	{0x6040,0xcd},
	{0x6041,0xcd},
	{0x6042,0xcd},
	{0x6043,0xcd},
	{0x6044,0xcd},
	{0x6045,0xcd},
	{0x6046,0xcd},
	{0x6047,0xcd},
	{0x6048,0xcd},
	{0x6049,0xcd},
	{0x604a,0xcd},
	{0x604b,0xcd},
	{0x604c,0xcd},
	{0x604d,0xcd},
	{0x604e,0xcd},
	{0x604f,0xcd},
	{0x6050,0xcd},
	{0x6051,0xcd},
	{0x6052,0xcd},
	{0x6053,0xcd},
	{0x6054,0xcd},
	{0x6055,0xcd},
	{0x6056,0xcd},
	{0x6057,0xcd},
	{0x6058,0xcd},
	{0x6059,0xcd},
	{0x605a,0xcd},
	{0x605b,0xcd},
	{0x605c,0xcd},
	{0x605d,0xcd},
	{0x605e,0xcd},
	{0x605f,0xcd},
	{0x6060,0xcd},
	{0x6061,0xcd},
	{0x6062,0xcd},
	{0x6063,0xcd},
	{0x6064,0xcd},
	{0x6065,0xcd},
	{0x6066,0xcd},
	{0x6067,0xcd},
	{0x6068,0xcd},
	{0x6069,0xcd},
	{0x606a,0xcd},
	{0x606b,0xcd},
	{0x606c,0xcd},
	{0x606d,0xcd},
	{0x606e,0xcd},
	{0x606f,0xcd},
	{0x6070,0xcd},
	{0x6071,0xcd},
	{0x6072,0xcd},
	{0x6073,0xcd},
	{0x6074,0xcd},
	{0x6075,0xcd},
	{0x6076,0xcd},
	{0x6077,0xcd},
	{0x6078,0xcd},
	{0x6079,0xcd},
	{0x607a,0xcd},
	{0x607b,0xcd},
	{0x607c,0xcd},
	{0x607d,0xcd},
	{0x607e,0xcd},
	{0x607f,0xcd},
	{0x6080,0xcd},
	{0x6081,0xcd},
	{0x6082,0xcd},
	{0x6083,0xcd},
	{0x6084,0xcd},
	{0x6085,0xcd},
	{0x6086,0xcd},
	{0x6087,0xcd},
	{0x6088,0xcd},
	{0x6089,0xcd},
	{0x608a,0xcd},
	{0x608b,0xcd},
	{0x608c,0xcd},
	{0x608d,0xcd},
	{0x608e,0xcd},
	{0x608f,0xcd},
	{0x6090,0xcd},
	{0x6091,0xcd},
	{0x6092,0xcd},
	{0x6093,0xcd},
	{0x6094,0xcd},
	{0x6095,0xcd},
	{0x6096,0xcd},
	{0x6097,0xcd},
	{0x6098,0xcd},
	{0x6099,0xcd},
	{0x609a,0xcd},
	{0x609b,0xcd},
	{0x609c,0xcd},
	{0x609d,0xcd},
	{0x609e,0xcd},
	{0x609f,0xcd},
	{0x60a0,0xcd},
	{0x60a1,0xcd},
	{0x60a2,0xcd},
	{0x60a3,0xcd},
	{0x60a4,0xcd},
	{0x60a5,0xcd},
	{0x60a6,0xcd},
	{0x60a7,0xcd},
	{0x60a8,0xcd},
	{0x60a9,0xcd},
	{0x60aa,0xcd},
	{0x60ab,0xcd},
	{0x60ac,0xcd},
	{0x60ad,0xcd},
	{0x60ae,0xcd},
	{0x60af,0xcd},
	{0x60b0,0xcd},
	{0x60b1,0xcd},
	{0x60b2,0xcd},
	{0x60b3,0xcd},
	{0x60b4,0xcd},
	{0x60b5,0xcd},
	{0x60b6,0xcd},
	{0x60b7,0xcd},
	{0x60b8,0xcd},
	{0x60b9,0xcd},
	{0x60ba,0xcd},
	{0x60bb,0xcd},
	{0x60bc,0xcd},
	{0x60bd,0xcd},
	{0x60be,0xcd},
	{0x60bf,0xcd},
	{0x60c0,0xcd},
	{0x60c1,0xcd},
	{0x60c2,0xcd},
	{0x60c3,0xcd},
	{0x60c4,0xcd},
	{0x60c5,0xcd},
	{0x60c6,0xcd},
	{0x60c7,0xcd},
	{0x60c8,0xcd},
	{0x60c9,0xcd},
	{0x60ca,0xcd},
	{0x60cb,0xcd},
	{0x60cc,0xcd},
	{0x60cd,0xcd},
	{0x60ce,0xcd},
	{0x60cf,0xcd},
	{0x60d0,0xcd},
	{0x60d1,0xcd},
	{0x60d2,0xcd},
	{0x60d3,0xcd},
	{0x60d4,0xcd},
	{0x60d5,0xcd},
	{0x60d6,0xcd},
	{0x60d7,0xcd},
	{0x60d8,0xcd},
	{0x60d9,0xcd},
	{0x60da,0xcd},
	{0x60db,0xcd},
	{0x60dc,0xcd},
	{0x60dd,0xcd},
	{0x60de,0xcd},
	{0x60df,0xcd},
	{0x60e0,0xcd},
	{0x60e1,0xcd},
	{0x60e2,0xcd},
	{0x60e3,0xcd},
	{0x60e4,0xcd},
	{0x60e5,0xcd},
	{0x60e6,0xcd},
	{0x60e7,0xcd},
	{0x60e8,0xcd},
	{0x60e9,0xcd},
	{0x60ea,0xcd},
	{0x60eb,0xcd},
	{0x60ec,0xcd},
	{0x60ed,0xcd},
	{0x60ee,0xcd},
	{0x60ef,0xcd},
	{0x60f0,0xcd},
	{0x60f1,0xcd},
	{0x60f2,0xcd},
	{0x60f3,0xcd},
	{0x60f4,0xcd},
	{0x60f5,0xcd},
	{0x60f6,0xcd},
	{0x60f7,0xcd},
	{0x60f8,0xcd},
	{0x60f9,0xcd},
	{0x60fa,0xcd},
	{0x60fb,0xcd},
	{0x60fc,0xcd},
	{0x60fd,0xcd},
	{0x60fe,0xcd},
	{0x60ff,0xcd},
	{0x6100,0xcd},
	{0x6101,0xcd},
	{0x6102,0xcd},
	{0x6103,0xcd},
	{0x6104,0xcd},
	{0x6105,0xcd},
	{0x6106,0xcd},
	{0x6107,0xcd},
	{0x6108,0xcd},
	{0x6109,0xcd},
	{0x610a,0xcd},
	{0x610b,0xcd},
	{0x610c,0xcd},
	{0x610d,0xcd},
	{0x610e,0xcd},
	{0x610f,0xcd},
	{0x6110,0xcd},
	{0x6111,0xcd},
	{0x6112,0xcd},
	{0x6113,0xcd},
	{0x6114,0xcd},
	{0x6115,0xcd},
	{0x6116,0xcd},
	{0x6117,0xcd},
	{0x6118,0xcd},
	{0x6119,0xcd},
	{0x611a,0xcd},
	{0x611b,0xcd},
	{0x611c,0xcd},
	{0x611d,0xcd},
	{0x611e,0xcd},
	{0x611f,0xcd},
	{0x6120,0xcd},
	{0x6121,0xcd},
	{0x6122,0xcd},
	{0x6123,0xcd},
	{0x6124,0xcd},
	{0x6125,0xcd},
	{0x6126,0xcd},
	{0x6127,0xcd},
	{0x6128,0xcd},
	{0x6129,0xcd},
	{0x612a,0xcd},
	{0x612b,0xcd},
	{0x612c,0xcd},
	{0x612d,0xcd},
	{0x612e,0xcd},
	{0x612f,0xcd},
	{0x6130,0xcd},
	{0x6131,0xcd},
	{0x6132,0xcd},
	{0x6133,0xcd},
	{0x6134,0xcd},
	{0x6135,0xcd},
	{0x6136,0xcd},
	{0x6137,0xcd},
	{0x6138,0xcd},
	{0x6139,0xcd},
	{0x613a,0xcd},
	{0x613b,0xcd},
	{0x613c,0xcd},
	{0x613d,0xcd},
	{0x613e,0xcd},
	{0x613f,0xcd},
	{0x6140,0xcd},
	{0x6141,0xcd},
	{0x6142,0xcd},
	{0x6143,0xcd},
	{0x6144,0xcd},
	{0x6145,0xcd},
	{0x6146,0xcd},
	{0x6147,0xcd},
	{0x6148,0xcd},
	{0x6149,0xcd},
	{0x614a,0xcd},
	{0x614b,0xcd},
	{0x614c,0xcd},
	{0x614d,0xcd},
	{0x614e,0xcd},
	{0x614f,0xcd},
	{0x6150,0xcd},
	{0x6151,0xcd},
	{0x6152,0xcd},
	{0x6153,0xcd},
	{0x6154,0xcd},
	{0x6155,0xcd},
	{0x6156,0xcd},
	{0x6157,0xcd},
	{0x6158,0xcd},
	{0x6159,0xcd},
	{0x615a,0xcd},
	{0x615b,0xcd},
	{0x615c,0xcd},
	{0x615d,0xcd},
	{0x615e,0xcd},
	{0x615f,0xcd},
	{0x6160,0xcd},
	{0x6161,0xcd},
	{0x6162,0xcd},
	{0x6163,0xcd},
	{0x6164,0xcd},
	{0x6165,0xcd},
	{0x6166,0xcd},
	{0x6167,0xcd},
	{0x6168,0xcd},
	{0x6169,0xcd},
	{0x616a,0xcd},
	{0x616b,0xcd},
	{0x616c,0xcd},
	{0x616d,0xcd},
	{0x616e,0xcd},
	{0x616f,0xcd},
	{0x6170,0xcd},
	{0x6171,0xcd},
	{0x6172,0xcd},
	{0x6173,0xcd},
	{0x6174,0xcd},
	{0x6175,0xcd},
	{0x6176,0xcd},
	{0x6177,0xcd},
	{0x6178,0xcd},
	{0x6179,0xcd},
	{0x617a,0xcd},
	{0x617b,0xcd},
	{0x617c,0xcd},
	{0x617d,0xcd},
	{0x617e,0xcd},
	{0x617f,0xcd},
	{0x6180,0xcd},
	{0x6181,0xcd},
	{0x6182,0xcd},
	{0x6183,0xcd},
	{0x6184,0xcd},
	{0x6185,0xcd},
	{0x6186,0xcd},
	{0x6187,0xcd},
	{0x6188,0xcd},
	{0x6189,0xcd},
	{0x618a,0xcd},
	{0x618b,0xcd},
	{0x618c,0xcd},
	{0x618d,0xcd},
	{0x618e,0xcd},
	{0x618f,0xcd},
	{0x6190,0xcd},
	{0x6191,0xcd},
	{0x6192,0xcd},
	{0x6193,0xcd},
	{0x6194,0xcd},
	{0x6195,0xcd},
	{0x6196,0xcd},
	{0x6197,0xcd},
	{0x6198,0xcd},
	{0x6199,0xcd},
	{0x619a,0xcd},
	{0x619b,0xcd},
	{0x619c,0xcd},
	{0x619d,0xcd},
	{0x619e,0xcd},
	{0x619f,0xcd},
	{0x61a0,0xcd},
	{0x61a1,0xcd},
	{0x61a2,0xcd},
	{0x61a3,0xcd},
	{0x61a4,0xcd},
	{0x61a5,0xcd},
	{0x61a6,0xcd},
	{0x61a7,0xcd},
	{0x61a8,0xcd},
	{0x61a9,0xcd},
	{0x61aa,0xcd},
	{0x61ab,0xcd},
	{0x61ac,0xcd},
	{0x61ad,0xcd},
	{0x61ae,0xcd},
	{0x61af,0xcd},
	{0x61b0,0xcd},
	{0x61b1,0xcd},
	{0x61b2,0xcd},
	{0x61b3,0xcd},
	{0x61b4,0xcd},
	{0x61b5,0xcd},
	{0x61b6,0xcd},
	{0x61b7,0xcd},
	{0x61b8,0xcd},
	{0x61b9,0xcd},
	{0x61ba,0xcd},
	{0x61bb,0xcd},
	{0x61bc,0xcd},
	{0x61bd,0xcd},
	{0x61be,0xcd},
	{0x61bf,0xcd},
	{0x61c0,0xcd},
	{0x61c1,0xcd},
	{0x61c2,0xcd},
	{0x61c3,0xcd},
	{0x61c4,0xcd},
	{0x61c5,0xcd},
	{0x61c6,0xcd},
	{0x61c7,0xcd},
	{0x61c8,0xcd},
	{0x61c9,0xcd},
	{0x61ca,0xcd},
	{0x61cb,0xcd},
	{0x61cc,0xcd},
	{0x61cd,0xcd},
	{0x61ce,0xcd},
	{0x61cf,0xcd},
	{0x61d0,0xcd},
	{0x61d1,0xcd},
	{0x61d2,0xcd},
	{0x61d3,0xcd},
	{0x61d4,0xcd},
	{0x61d5,0xcd},
	{0x61d6,0xcd},
	{0x61d7,0xcd},
	{0x61d8,0xcd},
	{0x61d9,0xcd},
	{0x61da,0xcd},
	{0x61db,0xcd},
	{0x61dc,0xcd},
	{0x61dd,0xcd},
	{0x61de,0xcd},
	{0x61df,0xcd},
	{0x61e0,0xcd},
	{0x61e1,0xcd},
	{0x61e2,0xcd},
	{0x61e3,0xcd},
	{0x61e4,0xcd},
	{0x61e5,0xcd},
	{0x61e6,0xcd},
	{0x61e7,0xcd},
	{0x61e8,0xcd},
	{0x61e9,0xcd},
	{0x61ea,0xcd},
	{0x61eb,0xcd},
	{0x61ec,0xcd},
	{0x61ed,0xcd},
	{0x61ee,0xcd},
	{0x61ef,0xcd},
	{0x61f0,0xcd},
	{0x61f1,0xcd},
	{0x61f2,0xcd},
	{0x61f3,0xcd},
	{0x61f4,0xcd},
	{0x61f5,0xcd},
	{0x61f6,0xcd},
	{0x61f7,0xcd},
	{0x61f8,0xcd},
	{0x61f9,0xcd},
	{0x61fa,0xcd},
	{0x61fb,0xcd},
	{0x61fc,0xcd},
	{0x61fd,0xcd},
	{0x61fe,0xcd},
	{0x61ff,0xcd},
	{0x6200,0xcd},
	{0x6201,0xcd},
	{0x6202,0xcd},
	{0x6203,0xcd},
	{0x6204,0xcd},
	{0x6205,0xcd},
	{0x6206,0xcd},
	{0x6207,0xcd},
	{0x6208,0xcd},
	{0x6209,0xcd},
	{0x620a,0xcd},
	{0x620b,0xcd},
	{0x620c,0xcd},
	{0x620d,0xcd},
	{0x620e,0xcd},
	{0x620f,0xcd},
	{0x6210,0xcd},
	{0x6211,0xcd},
	{0x6212,0xcd},
	{0x6213,0xcd},
	{0x6214,0xcd},
	{0x6215,0xcd},
	{0x6216,0xcd},
	{0x6217,0xcd},
	{0x6218,0xcd},
	{0x6219,0xcd},
	{0x621a,0xcd},
	{0x621b,0xcd},
	{0x621c,0xcd},
	{0x621d,0xcd},
	{0x621e,0xcd},
	{0x621f,0xcd},
	{0x6220,0xcd},
	{0x6221,0xcd},
	{0x6222,0xcd},
	{0x6223,0xcd},
	{0x6224,0xcd},
	{0x6225,0xcd},
	{0x6226,0xcd},
	{0x6227,0xcd},
	{0x6228,0xcd},
	{0x6229,0xcd},
	{0x622a,0xcd},
	{0x622b,0xcd},
	{0x622c,0xcd},
	{0x622d,0xcd},
	{0x622e,0xcd},
	{0x622f,0xcd},
	{0x6230,0xcd},
	{0x6231,0xcd},
	{0x6232,0xcd},
	{0x6233,0xcd},
	{0x6234,0xcd},
	{0x6235,0xcd},
	{0x6236,0xcd},
	{0x6237,0xcd},
	{0x6238,0xcd},
	{0x6239,0xcd},
	{0x623a,0xcd},
	{0x623b,0xcd},
	{0x623c,0xcd},
	{0x623d,0xcd},
	{0x623e,0xcd},
	{0x623f,0xcd},
	{0x6240,0xcd},
	{0x6241,0xcd},
	{0x6242,0xcd},
	{0x6243,0xcd},
	{0x6244,0xcd},
	{0x6245,0xcd},
	{0x6246,0xcd},
	{0x6247,0xcd},
	{0x6248,0xcd},
	{0x6249,0xcd},
	{0x624a,0xcd},
	{0x624b,0xcd},
	{0x624c,0xcd},
	{0x624d,0xcd},
	{0x624e,0xcd},
	{0x624f,0xcd},
	{0x6524,0x00},
	{0x6525,0x00},
	{0x0100,0x00},
	{0x0304,0x01},
	{0x0305,0x4d},
	{0x0307,0x01},
	{0x0325,0xE0},
	{0x0326,0xC4},
	{0x0327,0x04},
	{0x032F,0xC1},
	{0x3012,0x21},
	{0x3501,0x07},
	{0x3502,0x06},
	{0x3608,0x8A},
	{0x360B,0x58},
	{0x360D,0x05},
	{0x3622,0x10},
	{0x3623,0x0C},
	{0x3659,0x6A},
	{0x3701,0x20},
	{0x3702,0x30},
	{0x3709,0x98},
	{0x3712,0x50},
	{0x3714,0x61},
	{0x3724,0x0A},
	{0x3770,0x0E},
	{0x3774,0x0F},
	{0x3791,0x2A},
	{0x3797,0x64},
	{0x379c,0x14},
	{0x379d,0x14},
	{0x3800,0x00},
	{0x3802,0x00},
	{0x3803,0x00},
	{0x3804,0x24},
	{0x3805,0x3F},
	{0x3806,0x1B},
	{0x3807,0x3F},
	{0x3808,0x09},
	{0x3809,0x08},
	{0x380A,0x06},
	{0x380B,0xc8},
	{0x380c,0x08},
	{0x380d,0x40},
	{0x380E,0x0a},
	{0x380F,0xa6},
	{0x3811,0x05},
	{0x3813,0x04},
	{0x3815,0x31},
	{0x3820,0x43},
	{0x3821,0x15},
	{0x3822,0x00},
	{0x3830,0x03},
	{0x3837,0x04},
	{0x384C,0x02},
	{0x384D,0x10},
	{0x3889,0x60},
	{0x388B,0x0c},
	{0x388C,0x12},
	{0x388D,0x80},
	{0x388E,0x06},
	{0x388F,0xb8},
	{0x3909,0xDE},
	{0x390F,0x54},
	{0x3920,0x88},
	{0x3921,0x00},
	{0x3924,0x84},
	{0x3925,0x0E},
	{0x3926,0xAE},
	{0x3973,0x02},
	{0x3978,0x02},
	{0x399B,0x30},
	{0x39AF,0x10},
	{0x39B7,0x10},
	{0x39BF,0x10},
	{0x39CD,0x10},
	{0x39CE,0x20},
	{0x39CF,0x2B},
	{0x39D0,0x2B},
	{0x39D2,0x13},
	{0x39D3,0x23},
	{0x39D4,0x30},
	{0x39D5,0x30},
	{0x39FD,0x01},
	{0x39F5,0x04},
	{0x39F6,0x0F},
	{0x39F7,0x00},
	{0x39F8,0x00},
	{0x39F9,0x00},
	{0x39FF,0x00},
	{0x3A01,0x03},
	{0x3A03,0x00},
	{0x3A05,0x05},
	{0x3A2D,0x00},
	{0x3A2E,0x08},
	{0x3A2F,0x30},
	{0x3A30,0x30},
	{0x3A4B,0x09},
	{0x3A4C,0x0C},
	{0x3A4D,0x0C},
	{0x3A4E,0x0C},
	{0x3A4F,0x09},
	{0x3A50,0x0C},
	{0x3A51,0x0C},
	{0x3A52,0x0C},
	{0x3A57,0x00},
	{0x3A58,0x10},
	{0x3A59,0x01},
	{0x3A5A,0x01},
	{0x3A73,0x13},
	{0x3A74,0x10},
	{0x3A75,0x0B},
	{0x3A76,0x00},
	{0x3A77,0x13},
	{0x3A78,0x10},
	{0x3A79,0x0B},
	{0x3A7A,0x00},
	{0x3A7D,0x80},
	{0x4016,0x07},
	{0x4018,0x03},
	{0x401F,0x08},
	{0x45C0,0x60},
	{0x45CB,0x30},
	{0x4641,0x23},
	{0x4643,0x04},
	{0x4837,0x14},
	{0x4916,0x07},
	{0x4918,0x03},
	{0x491F,0x08},
	{0x4A16,0x07},
	{0x4A18,0x03},
	{0x4A1F,0x08},
	{0x5000,0xF5},
	{0x5001,0x83},
	{0x5002,0x37},
	{0x5068,0x02},
	{0x51B0,0x10},
	{0x51D2,0xEF},
	{0x51D9,0x08},
	{0x51DE,0x00},
	{0x51DF,0x04},
	{0x51E0,0x02},
	{0x51E1,0x06},
	{0x5202,0x06},
	{0x5203,0xD0},
	{0x5205,0x18},
	{0x5207,0x0C},
	{0x5208,0x11},
	{0x5209,0xF0},
	{0x520A,0x06},
	{0x520B,0xB8},
	{0x520D,0x0C},
	{0x5250,0x06},
	{0x5331,0x02},
	{0x5332,0x42},
	{0x5333,0x24},
	{0x54D2,0xEF},
	{0x5550,0x06},
	{0x57D2,0xEF},
	{0x5850,0x06},
	{0x0100,0x01},
	{0x0100,0x00},
	{0x3800,0x00},
	{0x3801,0x00},
	{0x3802,0x00},
	{0x3803,0x10},
	{0x3804,0x24},
	{0x3805,0x3F},
	{0x3806,0x1B},
	{0x3807,0x2F},
	{0x3808,0x09},
	{0x3809,0x00},
	{0x380A,0x06},
	{0x380B,0xC0},
	{0x380c,0x08},
	{0x380d,0x40},
	{0x380e,0x0a},
	{0x380f,0xa6},
	{0x3810,0x00},
	{0x3811,0x0a},
	{0x3812,0x00},
	{0x3813,0x03},
	{0x3888,0x00},
	{0x3889,0x00},
	{0x388a,0x00},
	{0x388b,0x00},
	{0x388c,0x00},
	{0x388d,0x00},
	{0x388e,0x00},
	{0x388f,0x00},
	{0x4641,0x00},
	{0x381A,0x03},
	{0x381B,0x89},
	{0x381C,0x08},
	{0x381D,0x40},
	{0x3824,0x08},
	{0x3825,0x40},
	{0x3826,0x03},
	{0x3827,0x89},
	{0x0100,0x01},
	{SENSOR_REG_END, 0x00},
};

/*
 * the order of the sensor_win_sizes is [full_resolution, preview_resolution].
 */
static struct tx_isp_sensor_win_setting sensor_win_sizes[] = {
	{
		.width = 2304,
		.height = 1728,
		.fps = 20 << 16 | 1,
		.mbus_code = TISP_VI_FMT_SRGGB10_1X10,
		.colorspace = TISP_COLORSPACE_SRGB,
		.regs = sensor_init_regs_2304_1728_20fps_mipi,
	}
};
struct tx_isp_sensor_win_setting *wsize = &sensor_win_sizes[0];

static struct regval_list sensor_stream_on_mipi[] = {
	{SENSOR_REG_END, 0x00},
};

static struct regval_list sensor_stream_off_mipi[] = {
	{SENSOR_REG_END, 0x00},
};

int sensor_read(struct tx_isp_subdev *sd, uint16_t reg,
	unsigned char *value)
{
	struct i2c_client *client = tx_isp_get_subdevdata(sd);
	uint8_t buf[2] = {(reg>>8)&0xff, reg&0xff};
	int ret;
	struct i2c_msg msg[2] = {
		[0] = {
			.addr = client->addr,
			.flags = 0,
			.len = 2,
			.buf = buf,
		},
		[1] = {
			.addr = client->addr,
			.flags = I2C_M_RD,
			.len = 1,
			.buf = value,
		}
	};

	ret = private_i2c_transfer(client->adapter, msg, 2);
	if (ret > 0)
		ret = 0;

	return ret;
}

static int sensor_write(struct tx_isp_subdev *sd, uint16_t reg, unsigned char value)
{
	struct i2c_client *client = tx_isp_get_subdevdata(sd);
	uint8_t buf[3] = {(reg>>8)&0xff, reg&0xff, value};
	struct i2c_msg msg = {
		.addr = client->addr,
		.flags = 0,
		.len = 3,
		.buf = buf,
	};

	int ret;
	ret = private_i2c_transfer(client->adapter, &msg, 1);
	if (ret > 0)
		ret = 0;

	return ret;
}

static int sensor_write_array(struct tx_isp_subdev *sd, struct regval_list *vals)
{
	int ret;
	while (vals->reg_num != SENSOR_REG_END) {
		if (vals->reg_num == SENSOR_REG_DELAY) {
			msleep(vals->value);
		} else {
			ret = sensor_write(sd, vals->reg_num, vals->value);
			if (ret < 0)
				return ret;
		}
		vals++;
	}
	return 0;
}

static int sensor_reset(struct tx_isp_subdev *sd, struct tx_isp_initarg *init)
{
	return 0;
}

static int sensor_detect(struct tx_isp_subdev *sd, unsigned int *ident)
{
	unsigned char v;
	int ret;

	ret = sensor_read(sd, 0x300a, &v);
	ISP_WARNING("-----%s: %d ret = %d, v = 0x%02x\n", __func__, __LINE__, ret,v);
	if (ret < 0)
		return ret;
	if (v != SENSOR_CHIP_ID_H)
		return -ENODEV;
	*ident = v;

	ret = sensor_read(sd, 0x300b, &v);
	ISP_WARNING("-----%s: %d ret = %d, v = 0x%02x\n", __func__, __LINE__, ret,v);
	if (ret < 0)
		return ret;
	if (v != SENSOR_CHIP_ID_M)
		return -ENODEV;
	*ident = (*ident << 8) | v;

	ret = sensor_read(sd, 0x300c, &v);
	ISP_WARNING("-----%s: %d ret = %d, v = 0x%02x\n", __func__, __LINE__, ret,v);
	if (ret < 0)
		return ret;
	if (v != SENSOR_CHIP_ID_L)
		return -ENODEV;
	*ident = (*ident << 8) | v;

	return 0;
}

static int sensor_set_expo(struct tx_isp_subdev *sd, int value)
{
	int ret = 0;
	int it = (value & 0xffff );
	int again = (value & 0xffff0000) >> 16;
	it=it/2*2;
	ret = sensor_write(sd,  0x3502, (unsigned char)(it & 0xff));
	ret += sensor_write(sd, 0x3501, (unsigned char)((it >> 8) & 0xff));
	ret += sensor_write(sd, 0x3500, (unsigned char)((it >> 16) & 0xf));

	ret += sensor_write(sd, 0x3509, (unsigned char)(again & 0xff));
	ret += sensor_write(sd, 0x3508, (unsigned char)((again >> 8 & 0xff)));

	if (ret < 0)
		return ret;

	return 0;
}

static int sensor_set_digital_gain(struct tx_isp_subdev *sd, int value)
{
	return 0;
}

static int sensor_get_black_pedestal(struct tx_isp_subdev *sd, int value)
{
	return 0;
}

static int sensor_set_attr(struct tx_isp_subdev *sd, struct tx_isp_sensor_win_setting *wise)
{
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);

	sensor->video.vi_max_width = wsize->width;
	sensor->video.vi_max_height = wsize->height;
	sensor->video.mbus.width = wsize->width;
	sensor->video.mbus.height = wsize->height;
	sensor->video.mbus.code = wsize->mbus_code;
	sensor->video.mbus.field = TISP_FIELD_NONE;
	sensor->video.mbus.colorspace = wsize->colorspace;
	sensor->video.fps = wsize->fps;

	return 0;
}

static int sensor_init(struct tx_isp_subdev *sd, struct tx_isp_initarg *init)
{
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);
	int ret = 0;

	if (!init->enable)
		return ISP_SUCCESS;

	sensor_set_attr(sd, wsize);
	sensor->video.state = TX_ISP_MODULE_DEINIT;
	ret = tx_isp_call_subdev_notify(sd, TX_ISP_EVENT_SYNC_SENSOR_ATTR, &sensor->video);
	sensor->priv = wsize;

	return 0;
}

static int sensor_s_stream(struct tx_isp_subdev *sd, struct tx_isp_initarg *init)
{
	int ret = 0;
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);

	if (init->enable) {
		if (sensor->video.state == TX_ISP_MODULE_DEINIT) {
			ret = sensor_write_array(sd, wsize->regs);
			if (ret)
				return ret;
			sensor->video.state = TX_ISP_MODULE_INIT;
		}
		if (sensor->video.state == TX_ISP_MODULE_INIT) {

			ret = sensor_write_array(sd, sensor_stream_on_mipi);
			sensor->video.state = TX_ISP_MODULE_RUNNING;
			ISP_WARNING("%s stream on\n", SENSOR_NAME);
		}
	} else {
		ret = sensor_write_array(sd, sensor_stream_off_mipi);
		sensor->video.state = TX_ISP_MODULE_INIT;
		ISP_WARNING("%s stream off\n", SENSOR_NAME);
	}

	return ret;
}

static int sensor_set_fps(struct tx_isp_subdev *sd, int fps)
{
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);
	struct tx_isp_sensor_register_info *info = &sensor->info;
	int ret = 0;
	unsigned char tmp;
	unsigned int sclk = 0;
	unsigned int hts = 0;
	unsigned int vts = 0;
	unsigned char val = 0;
	unsigned int sensor_max_fps = SENSOR_OUTPUT_MAX_FPS;
	unsigned int newformat = 0;

	switch(info->default_boot) {
		case 0:
			sclk = SENSOR_SUPPORT_SCLK_MIPI;
			sensor_max_fps = TX_SENSOR_MAX_FPS_20;
			break;
		default:
			ISP_ERROR("Have no this setting!!!\n");
	}

	newformat = (((fps >> 16) / (fps & 0xffff)) << 8) + ((((fps >> 16) % (fps & 0xffff)) << 8) / (fps & 0xffff));
	if (newformat > (sensor_max_fps << 8) || fps < (SENSOR_OUTPUT_MIN_FPS << 8)) {
		ISP_ERROR("warn: fps(%d) not in range\n", fps);
		return -1;
	}

	ret += sensor_read(sd, 0x380c, &val);
	hts = val;
	ret += sensor_read(sd, 0x380d, &val);
	if (0 != ret) {
		ISP_ERROR("Error: %s read error\n", SENSOR_NAME);
		return ret;
	}
	hts = ((hts << 8) | tmp) << 1;

	vts = sclk * (fps & 0xffff) / hts / ((fps & 0xffff0000) >> 16);
	ret += sensor_write(sd, 0x380f, vts & 0xff);
	ret += sensor_write(sd, 0x380e, (vts >> 8) & 0xff);
	if (0 != ret) {
		ISP_ERROR("Error: %s write error\n", SENSOR_NAME);
		return ret;
	}

	sensor->video.fps = fps;
	sensor->video.attr->max_integration_time_native = vts - 12;
	sensor->video.attr->integration_time_limit = vts - 12;
	sensor->video.attr->total_height = vts;
	sensor->video.attr->max_integration_time = vts - 12;
	ret = tx_isp_call_subdev_notify(sd, TX_ISP_EVENT_SYNC_SENSOR_ATTR, &sensor->video);

	return ret;
}

static int sensor_set_mode(struct tx_isp_subdev *sd, int value)
{
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);
	int ret = ISP_SUCCESS;

	if (wsize) {
		sensor_set_attr(sd, wsize);
		ret = tx_isp_call_subdev_notify(sd, TX_ISP_EVENT_SYNC_SENSOR_ATTR, &sensor->video);
	}

	return ret;
}

struct clk *sclka;
static int sensor_attr_check(struct tx_isp_subdev *sd)
{
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);
	struct tx_isp_sensor_register_info *info = &sensor->info;
	struct i2c_client *client = tx_isp_get_subdevdata(sd);
	unsigned long rate;

	switch(info->default_boot) {
	case 0:
		wsize = &sensor_win_sizes[0];
		memcpy(&sensor_attr.mipi, &sensor_mipi_linear, sizeof(sensor_mipi_linear));
		sensor_attr.data_type = TX_SENSOR_DATA_TYPE_LINEAR;
		sensor_attr.dbus_type = TX_SENSOR_DATA_INTERFACE_MIPI;
		sensor_attr.max_integration_time_native = 2726 - 12;
		sensor_attr.integration_time_limit = 2726 - 12;
		sensor_attr.total_width = 2112 * 2;
		sensor_attr.total_height = 2726;
		sensor_attr.max_integration_time = 2726 - 12;
		sensor_attr.one_line_expr_in_us = 18;
	        sensor_attr.again = 0x100;
                sensor_attr.integration_time = 0x706;
		break;
	default:
		ISP_ERROR("Have no this setting!!!\n");
	}

	switch(info->video_interface) {
	case TISP_SENSOR_VI_MIPI_CSI0:
		sensor_attr.dbus_type = TX_SENSOR_DATA_INTERFACE_MIPI;
		sensor_attr.mipi.index = 0;
		break;
	case TISP_SENSOR_VI_MIPI_CSI1:
		sensor_attr.dbus_type = TX_SENSOR_DATA_INTERFACE_MIPI;
		sensor_attr.mipi.index = 1;
		break;
	case TISP_SENSOR_VI_DVP:
		sensor_attr.dbus_type = TX_SENSOR_DATA_INTERFACE_DVP;
		break;
	default:
		ISP_ERROR("Have no this interface!!!\n");
	}

	switch(info->mclk) {
	case TISP_SENSOR_MCLK0:
		sclka = private_devm_clk_get(&client->dev, "mux_cim0");
		sensor->mclk = private_devm_clk_get(sensor->dev, "div_cim0");
		set_sensor_mclk_function(0);
		break;
	case TISP_SENSOR_MCLK1:
		sclka = private_devm_clk_get(&client->dev, "mux_cim1");
		sensor->mclk = private_devm_clk_get(sensor->dev, "div_cim1");
		set_sensor_mclk_function(1);
		break;
	case TISP_SENSOR_MCLK2:
		sclka = private_devm_clk_get(&client->dev, "mux_cim2");
		sensor->mclk = private_devm_clk_get(sensor->dev, "div_cim2");
		set_sensor_mclk_function(2);
		break;
	default:
		ISP_ERROR("Have no this MCLK Source!!!\n");
	}

	rate = private_clk_get_rate(sensor->mclk);
	if (IS_ERR(sensor->mclk)) {
		ISP_ERROR("Cannot get sensor input clock cgu_cim\n");
		goto err_get_mclk;
	}

    private_clk_set_rate(sensor->mclk, MCLK);
    private_clk_prepare_enable(sensor->mclk);

	reset_gpio = info->rst_gpio;
	pwdn_gpio = info->pwdn_gpio;

	sensor_set_attr(sd, wsize);
	sensor->priv = wsize;

	return 0;

err_get_mclk:
	return -1;
}

static int sensor_g_chip_ident(struct tx_isp_subdev *sd,
			       struct tx_isp_chip_ident *chip)
{
	struct i2c_client *client = tx_isp_get_subdevdata(sd);
	unsigned int ident = 0;
	int ret = ISP_SUCCESS;

	sensor_attr_check(sd);
	if (reset_gpio != -1) {
		ret = private_gpio_request(reset_gpio,"sensor_reset");
		if (!ret) {
			private_gpio_direction_output(reset_gpio, 1);
			private_msleep(5);
			private_gpio_direction_output(reset_gpio, 0);
			private_msleep(20);
			private_gpio_direction_output(reset_gpio, 1);
			private_msleep(20);
		} else {
			ISP_ERROR("gpio request fail %d\n",reset_gpio);
		}
	}
	if (pwdn_gpio != -1) {
		ret = private_gpio_request(pwdn_gpio,"sensor_pwdn");
		if (!ret) {
			private_gpio_direction_output(pwdn_gpio, 1);
			private_msleep(100);
			private_gpio_direction_output(pwdn_gpio, 0);
			private_msleep(100);
		} else {
			ISP_ERROR("gpio request fail %d\n",pwdn_gpio);
		}
	}

		ret = sensor_detect(sd, &ident);

	if (ret) {
		ISP_ERROR("chip found @ 0x%x (%s) is not an %s chip.\n",
			  client->addr, client->adapter->name, SENSOR_NAME);
		return ret;
	}
	ISP_WARNING("%s chip found @ 0x%02x (%s)\n",
		    SENSOR_NAME, client->addr, client->adapter->name);
	if (chip) {
		memcpy(chip->name, SENSOR_NAME, sizeof(SENSOR_NAME));
		chip->ident = ident;
		chip->revision = SENSOR_VERSION;
	}
	return 0;
}
static int sensor_sensor_ops_ioctl(struct tx_isp_subdev *sd, unsigned int cmd, void *arg)
{
	long ret = 0;
	struct tx_isp_sensor_value *sensor_val = arg;

	if (IS_ERR_OR_NULL(sd)) {
		ISP_ERROR("[%d]The pointer is invalid!\n", __LINE__);
		return -EINVAL;
	}
	switch(cmd) {
	case TX_ISP_EVENT_SENSOR_EXPO:
		if (arg)
			ret = sensor_set_expo(sd, sensor_val->value);
		break;
	//case TX_ISP_EVENT_SENSOR_INT_TIME:
	//	if (arg)
	//		ret = sensor_set_integration_time(sd, sensor_val->value);
	//	break;
	//case TX_ISP_EVENT_SENSOR_AGAIN:
	//	if (arg)
	//		ret = sensor_set_analog_gain(sd, sensor_val->value);
	//	break;
	case TX_ISP_EVENT_SENSOR_DGAIN:
		if (arg)
			ret = sensor_set_digital_gain(sd, sensor_val->value);
		break;
	case TX_ISP_EVENT_SENSOR_BLACK_LEVEL:
		if (arg)
			ret = sensor_get_black_pedestal(sd, sensor_val->value);
		break;
	case TX_ISP_EVENT_SENSOR_RESIZE:
		if (arg)
			ret = sensor_set_mode(sd, sensor_val->value);
		break;
	case TX_ISP_EVENT_SENSOR_PREPARE_CHANGE:
		ret = sensor_write_array(sd, sensor_stream_off_mipi);
		break;
	case TX_ISP_EVENT_SENSOR_FINISH_CHANGE:
		ret = sensor_write_array(sd, sensor_stream_on_mipi);
		break;
	case TX_ISP_EVENT_SENSOR_FPS:
		if (arg)
			ret = sensor_set_fps(sd, sensor_val->value);
		break;
	default:
		break;
	}

	return 0;
}

static int sensor_g_register(struct tx_isp_subdev *sd, struct tx_isp_dbg_register *reg)
{
	unsigned char val = 0;
	int len = 0;
	int ret = 0;

	len = strlen(sd->chip.name);
	if (len && strncmp(sd->chip.name, reg->name, len)) {
		return -EINVAL;
	}
	if (!private_capable(CAP_SYS_ADMIN))
		return -EPERM;
	ret = sensor_read(sd, reg->reg & 0xffff, &val);
	reg->val = val;
	reg->size = 2;

	return ret;
}

static int sensor_s_register(struct tx_isp_subdev *sd, const struct tx_isp_dbg_register *reg)
{
	int len = 0;

	len = strlen(sd->chip.name);
	if (len && strncmp(sd->chip.name, reg->name, len)) {
		return -EINVAL;
	}
	if (!private_capable(CAP_SYS_ADMIN))
		return -EPERM;
	sensor_write(sd, reg->reg & 0xffff, reg->val & 0xff);

	return 0;
}

static struct tx_isp_subdev_core_ops sensor_core_ops = {
	.g_chip_ident = sensor_g_chip_ident,
	.reset = sensor_reset,
	.init = sensor_init,
//	.fsync = sensor_frame_sync,
	.g_register = sensor_g_register,
	.s_register = sensor_s_register,
};

static struct tx_isp_subdev_video_ops sensor_video_ops = {
	.s_stream = sensor_s_stream,
};

static struct tx_isp_subdev_sensor_ops sensor_sensor_ops = {
	.ioctl = sensor_sensor_ops_ioctl,
};

static struct tx_isp_subdev_ops sensor_ops = {
	.core = &sensor_core_ops,
	.video = &sensor_video_ops,
	.sensor = &sensor_sensor_ops,
};

/* It's the sensor device */
static u64 tx_isp_module_dma_mask = ~(u64)0;
struct platform_device sensor_platform_device = {
	.name = SENSOR_NAME,
	.id = -1,
	.dev = {
		.dma_mask = &tx_isp_module_dma_mask,
		.coherent_dma_mask = 0xffffffff,
		.platform_data = NULL,
	},
	.num_resources = 0,
};

static int sensor_probe(struct i2c_client *client,
			const struct i2c_device_id *id)
{
	struct tx_isp_subdev *sd;
	struct tx_isp_video_in *video;
	struct tx_isp_sensor *sensor;

	sensor = (struct tx_isp_sensor *)kzalloc(sizeof(*sensor), GFP_KERNEL);
	if (!sensor) {
		ISP_ERROR("Failed to allocate sensor subdev.\n");
		return -ENOMEM;
	}
	memset(sensor, 0 ,sizeof(*sensor));
	sensor->dev = &client->dev;
	sd = &sensor->sd;
	video = &sensor->video;
	sensor->video.attr = &sensor_attr;
	tx_isp_subdev_init(&sensor_platform_device, sd, &sensor_ops);
	tx_isp_set_subdevdata(sd, client);
	tx_isp_set_subdev_hostdata(sd, sensor);
	private_i2c_set_clientdata(client, sd);

	ISP_WARNING("probe ok ------->%s\n", SENSOR_NAME);

	return 0;
}

static int sensor_remove(struct i2c_client *client)
{
	struct tx_isp_subdev *sd = private_i2c_get_clientdata(client);
	struct tx_isp_sensor *sensor = tx_isp_get_subdev_hostdata(sd);

	if (reset_gpio != -1)
		private_gpio_free(reset_gpio);
	if (pwdn_gpio != -1)
		private_gpio_free(pwdn_gpio);

	private_clk_disable_unprepare(sensor->mclk);
	private_devm_clk_put(&client->dev, sensor->mclk);
	tx_isp_subdev_deinit(sd);
	kfree(sensor);

	return 0;
}

static const struct i2c_device_id sensor_id[] = {
	{ SENSOR_NAME, 0 },
	{ }
};
MODULE_DEVICE_TABLE(i2c, sensor_id);

static struct i2c_driver sensor_driver = {
	.driver = {
		.owner = THIS_MODULE,
		.name = SENSOR_NAME,
	},
	.probe = sensor_probe,
	.remove = sensor_remove,
	.id_table = sensor_id,
};

static __init int init_sensor(void)
{
	return private_i2c_add_driver(&sensor_driver);
}

static __exit void exit_sensor(void)
{
	private_i2c_del_driver(&sensor_driver);
}

module_init(init_sensor);
module_exit(exit_sensor);

MODULE_DESCRIPTION("A low-level driver for "SENSOR_NAME" sensor");
MODULE_LICENSE("GPL");
