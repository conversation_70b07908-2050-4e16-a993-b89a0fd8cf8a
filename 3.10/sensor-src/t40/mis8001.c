// SPDX-License-Identifier: GPL-2.0+
/*
 * mis8001.c
 * Copyright (C) 2012 Ingenic Semiconductor Co., Ltd.
 */

#include <linux/init.h>
#include <linux/module.h>
#include <linux/slab.h>
#include <linux/i2c.h>
#include <linux/delay.h>
#include <linux/gpio.h>
#include <linux/clk.h>
#include <linux/proc_fs.h>
#include <tx-isp-common.h>
#include <sensor-common.h>

#define SENSOR_NAME "mis8001"
//#define SENSOR_CHIP_ID_H (0x82)
//#define SENSOR_CHIP_ID_L (0x35)
#define SENSOR_REG_END 0xffff
#define SENSOR_REG_DELAY 0xfffe
#define SENSOR_SUPPORT_SCLK_8M_FPS_15 (37125000)
#define SENSOR_SUPPORT_SCLK_8M_FPS_30 (37125000)
#define SENSOR_OUTPUT_MAX_FPS 30
#define SENSOR_OUTPUT_MIN_FPS 5
#define DRIVE_CAPABILITY_1
#define SENSOR_VERSION "H20210622a"

static int reset_gpio = GPIO_PC(27);
module_param(reset_gpio, int, S_IRUGO);
MODULE_PARM_DESC(reset_gpio, "Reset GPIO NUM");

static int pwdn_gpio = -1;
module_param(pwdn_gpio, int, S_IRUGO);
MODULE_PARM_DESC(pwdn_gpio, "Power down GPIO NUM");

static int sensor_max_fps = 30;

//static unsigned int expo_val = 2235;

struct regval_list {
    uint16_t reg_num;
    unsigned char value;
};

struct again_lut {
	unsigned int value;
	unsigned int gain;
};

struct again_lut sensor_again_lut[] = {
    {0x0, 0},
    {0x1, 3252},
    {0x2, 6573},
    {0x3, 9866},
    {0x4, 13131},
    {0x5, 16367},
    {0x6, 19649},
    {0x7, 22969},
    {0x8, 26249},
    {0x9, 29487},
    {0xa, 32753},
    {0xb, 36038},
    {0xc, 39338},
    {0xd, 42587},
    {0xe, 45903},
    {0xf, 49162},
    {0x10, 52422},
    {0x11, 55730},
    {0x12, 58978},
    {0x13, 62264},
    {0x14, 65535},
    {0x15, 68833},
    {0x16, 72108},
    {0x17, 75359},
    {0x18, 78625},
    {0x19, 81902},
    {0x1a, 85184},
    {0x1b, 88467},
    {0x1c, 91748},
    {0x1d, 95022},
    {0x1e, 98288},
    {0x1f, 101573},
    {0x20, 104842},
    {0x21, 108122},
    {0x22, 111409},
    {0x23, 114697},
    {0x24, 117957},
    {0x25, 121239},
    {0x26, 124513},
    {0x27, 127799},
    {0x28, 131070},
    {0x29, 134345},
    {0x2a, 137621},
    {0x2b, 140894},
    {0x2c, 144181},
    {0x2d, 147457},
    {0x2e, 150738},
    {0x2f, 154002},
    {0x30, 157283},
    {0x31, 160557},
    {0x32, 163839},
    {0x33, 167108},
    {0x34, 170393},
    {0x35, 173672},
    {0x36, 176944},
    {0x37, 180218},
    {0x38, 183492},
    {0x39, 186774},
    {0x3a, 190048},
    {0x3b, 193322},
    {0x3c, 196605},
    {0x3d, 199880},
    {0x3e, 203156},
    {0x3f, 206439},
    {0x40, 209716},
    {0x41, 212992},
    {0x42, 216264},
    {0x43, 219537},
    {0x44, 222818},
    {0x45, 226092},
    {0x46, 229374},
    {0x47, 232651},
    {0x48, 235928},
    {0x49, 239200},
    {0x4a, 242479},
    {0x4b, 245753},
    {0x4c, 249034},
    {0x4d, 252309},
    {0x4e, 255589},
    {0x4f, 258863},
    {0x50, 262140},
    {0x51, 265415},
    {0x52, 268691},
    {0x53, 271969},
    {0x54, 275246},
    {0x55, 278522},
    {0x56, 281799},
    {0x57, 285077},
    {0x58, 288353},
    {0x59, 291632},
    {0x5a, 294905},
    {0x5b, 298182},
    {0x5c, 301459},
    {0x5d, 304738},
    {0x5e, 308014},
    {0x5f, 311292},
    {0x60, 314569},
    {0x61, 317844},
    {0x62, 321121},
    {0x63, 324398},
    {0x64, 327675},
    {0x65, 330950},
    {0x66, 334229},
    {0x67, 337504},
    {0x68, 340781},
    {0x69, 344059},
    {0x6a, 347336},
    {0x6b, 350612},
    {0x6c, 353888},
    {0x6d, 357165},
    {0x6e, 360442},
    {0x6f, 363719},
    {0x70, 366996},
    {0x71, 370272},
    {0x72, 373549},
    {0x73, 376825},
    {0x74, 380102},
    {0x75, 383379},
    {0x76, 386656},
    {0x77, 389933},
    {0x78, 393210},
    {0x79, 396486},
    {0x7a, 399764},
    {0x7b, 403039},
    {0x7c, 406317},
    {0x7d, 409593},
    {0x7e, 412870},
    {0x7f, 416147},
    {0x80, 419424},
    {0x81, 422701},
    {0x82, 425977},
    {0x83, 429253},
    {0x84, 432531},
    {0x85, 435807},
    {0x86, 439084},
    {0x87, 442361},
    {0x88, 445637},
    {0x89, 448914},
    {0x8a, 452191},
    {0x8b, 455468},
    {0x8c, 458745},
    {0x8d, 462021},
    {0x8e, 465298},
    {0x8f, 468575},
    {0x90, 471851},
    {0x91, 475129},
    {0x92, 478405},
    {0x93, 481682},
    {0x94, 484958},
    {0x95, 488235},
    {0x96, 491512},
    {0x97, 494789},
    {0x98, 498066},
    {0x99, 501342},
    {0x9a, 504619},
    {0x9b, 507896},
    {0x9c, 511173},
    {0x9d, 514449},
    {0x9e, 517726},
    {0x9f, 521003},
    {0xa0, 524280},
    {0xa1, 527556},
    {0xa2, 530833},
    {0xa3, 534110},
    {0xa4, 537387},
    {0xa5, 540663},
    {0xa6, 543940},
    {0xa7, 547217},
    {0xa8, 550493},
    {0xa9, 553770},
    {0xaa, 557047},
    {0xab, 560324},
    {0xac, 563600},
    {0xad, 566877},
    {0xae, 570154},
    {0xaf, 573431},
    {0xb0, 576708},
    {0xb1, 579984},
    {0xb2, 583261},
    {0xb3, 586538},
    {0xb4, 589815},
    {0xb5, 593091},
    {0xb6, 596368},
    {0xb7, 599645},
    {0xb8, 602922},
    {0xb9, 606198},
    {0xba, 609475},
    {0xbb, 612752},
    {0xbc, 616028},
    {0xbd, 619305},
    {0xbe, 622582},
    {0xbf, 625859},
    {0xc0, 629136},
    {0xc1, 632412},
    {0xc2, 635689},
    {0xc3, 638966},
    {0xc4, 642243},
    {0xc5, 645519},
    {0xc6, 648796},
    {0xc7, 652073},
    {0xc8, 655350},
    {0xc9, 658626},
    {0xca, 661903},
    {0xcb, 665180},
    {0xcc, 668456},
    {0xcd, 671733},
    {0xce, 675010},
    {0xcf, 678287},
    {0xd0, 681563},
    {0xd1, 684840},
    {0xd2, 688117},
    {0xd3, 691394},
    {0xd4, 694671},
    {0xd5, 697947},
    {0xd6, 701224},
    {0xd7, 704501},
    {0xd8, 707778},
    {0xd9, 711054},
    {0xda, 714331},
    {0xdb, 717608},
    {0xdc, 720885},
};

struct tx_isp_sensor_attribute sensor_attr;

unsigned int sensor_alloc_again(unsigned int isp_gain, unsigned char shift, unsigned int *sensor_again)
{
	struct again_lut *lut = sensor_again_lut;
	while (lut->gain <= sensor_attr.max_again) {
		if (isp_gain == 0) {
			*sensor_again = lut[0].value;
			return lut[0].gain;
		}
		else if (isp_gain < lut->gain) {
			*sensor_again = (lut - 1)->value;
			return (lut - 1)->gain;
		}
		else {
			if ((lut->gain == sensor_attr.max_again) && (isp_gain >= lut->gain)) {
				*sensor_again = lut->value;
				return lut->gain;
			}
		}

		lut++;
	}

	return isp_gain;
}

unsigned int sensor_alloc_dgain(unsigned int isp_gain, unsigned char shift, unsigned int *sensor_dgain)
{
	return 0;
}
struct tx_isp_mipi_bus sensor_mipi_2M={
	.mode = SENSOR_MIPI_OTHER_MODE,
	.clk = 891,
	.lans = 4,
	.settle_time_apative_en = 0,
	.mipi_sc.sensor_csi_fmt = TX_SENSOR_RAW12,//RAW10
	.mipi_sc.hcrop_diff_en = 0,
	.mipi_sc.mipi_vcomp_en = 0,
	.mipi_sc.mipi_hcomp_en = 0,
	.image_twidth = 1920,
	.image_theight = 1080,
	.mipi_sc.mipi_crop_start0x = 0,
	.mipi_sc.mipi_crop_start0y = 0,
	.mipi_sc.mipi_crop_start1x = 0,
	.mipi_sc.mipi_crop_start1y = 0,
	.mipi_sc.mipi_crop_start2x = 0,
	.mipi_sc.mipi_crop_start2y = 0,
	.mipi_sc.mipi_crop_start3x = 0,
	.mipi_sc.mipi_crop_start3y = 0,
	.mipi_sc.line_sync_mode = 0,
	.mipi_sc.work_start_flag = 0,
	.mipi_sc.data_type_en = 0,
	.mipi_sc.data_type_value = RAW12,

	.mipi_sc.del_start = 0,
	.mipi_sc.sensor_frame_mode = TX_SENSOR_DEFAULT_FRAME_MODE,
	.mipi_sc.sensor_fid_mode = 0,
	.mipi_sc.sensor_mode = TX_SENSOR_DEFAULT_MODE,
};

struct tx_isp_sensor_attribute sensor_attr={
	.name = SENSOR_NAME,
	.chip_id = 0x8001,
	.cbus_type = TX_SENSOR_CONTROL_INTERFACE_I2C,
	.cbus_mask = TISP_SBUS_MASK_SAMPLE_8BITS | TISP_SBUS_MASK_ADDR_16BITS,
	.cbus_device = 0x36,
	.dbus_type = TX_SENSOR_DATA_INTERFACE_MIPI,
	.mipi = {
		.mode = SENSOR_MIPI_OTHER_MODE,
		.clk = 891,
		.lans = 4,
		.settle_time_apative_en = 0,
		.mipi_sc.sensor_csi_fmt = TX_SENSOR_RAW12,//RAW10
		.mipi_sc.hcrop_diff_en = 0,
		.mipi_sc.mipi_vcomp_en = 0,
		.mipi_sc.mipi_hcomp_en = 0,
		.mipi_sc.line_sync_mode = 0,
		.mipi_sc.work_start_flag = 0,
		.image_twidth = 3840,
		.image_theight = 2160,
		.mipi_sc.mipi_crop_start0x = 0,
		.mipi_sc.mipi_crop_start0y = 0,
		.mipi_sc.mipi_crop_start1x = 0,
		.mipi_sc.mipi_crop_start1y = 0,
		.mipi_sc.mipi_crop_start2x = 0,
		.mipi_sc.mipi_crop_start2y = 0,
		.mipi_sc.mipi_crop_start3x = 0,
		.mipi_sc.mipi_crop_start3y = 0,
		.mipi_sc.data_type_en = 0,
		.mipi_sc.data_type_value = RAW12,
		.mipi_sc.del_start = 0,
		.mipi_sc.sensor_frame_mode = TX_SENSOR_DEFAULT_FRAME_MODE,
		.mipi_sc.sensor_fid_mode = 0,
		.mipi_sc.sensor_mode = TX_SENSOR_DEFAULT_MODE,
	},
	.max_again = 327676,
	.max_dgain = 0,
	.min_integration_time = 2,
	.min_integration_time_native = 2,
	.max_integration_time_native = 2693,
	.integration_time_limit = 2693,
	//.total_width = 0x820 * 2,
	.total_height = 0x8c5,
	.max_integration_time = 2693,
	.integration_time_apply_delay = 2,
	.again_apply_delay = 2,
	.dgain_apply_delay = 0,
	.sensor_ctrl.alloc_again = sensor_alloc_again,
	.sensor_ctrl.alloc_dgain = sensor_alloc_dgain,
};

static struct regval_list sensor_init_regs_3840_2160_25fps_mipi[] = {
    //UHD_30fps_No.1
    {0x0000,0x10},
    {0x0001,0x00},
    {0x0002,0x04},
    {0x0003,0x00},
    {0x0004,0x20},
    {0x0005,0x06},
    {0x0006,0x87},
    {0x0007,0x0a},
    {0x0008,0x00},
    {0x0009,0x62},
    {0x000A,0x04},
    {0x000B,0x00},
    {0x000C,0x0F},
    {0x000D,0xB0},
    {0x000E,0x03},
    {0x000F,0x0F},
    {0x0010,0xB0},
    {0x0011,0x03},
    {0x0012,0x0F},
    {0x0013,0xB0},
    {0x0014,0x03},
    {0x0015,0x00},
    {0x0016,0x00},
    {0x0017,0x3C},
    {0x0018,0x02},
    {0x0019,0x3C},
    {0x001A,0x02},
    {0x001B,0x3C},
    {0x001C,0x02},
    {0x001D,0x16},
    {0x001E,0x2D},
    {0x001F,0x16},
    {0x0020,0x16},
    {0x0021,0x00},
    {0x0022,0x00},
    {0x0023,0x00},
    {0x0024,0x00},
    {0x0025,0x00},
    {0x0026,0x00},
    {0x0027,0x00},
    {0x0028,0x00},
    {0x0029,0x00},
    {0x002A,0x61},
    {0x002B,0x00},
    {0x002C,0x03},
    {0x002D,0x03},
    {0x002E,0x67},
    {0x002F,0x42},
    {0x0030,0x10},
    {0x0031,0x02},
    {0x0032,0x58},
    {0x0033,0x82},
    {0x0034,0x00},
    {0x0035,0x67},
    {0x0036,0x48},
    {0x0037,0x0A},
    {0x0038,0x01},
    {0x0039,0x58},
    {0x003A,0x82},
    {0x003B,0x00},
    {0x003C,0x03},
    {0x003D,0x0E},
    {0x003E,0x00},
    {0x003F,0x00},
    {0x0040,0x28},
    {0x0041,0x05},
    {0x0042,0x0C},
    {0x0043,0x01},
    {0x0044,0x00},
    {0x0045,0xC0},
    {0x0046,0x00},
    {0x0047,0x2B},//BLC 统计值开启
    {0x0048,0x00},
    {0x0049,0x00},
    {0x004A,0xFF},
    {0x004B,0x01},
    {0x004C,0xFF},
    {0x004D,0x01},
    {0x004E,0x06},
    {0x004F,0xB8},
    {0x0050,0x0E},
    {0x0051,0x00},
    {0x0052,0x06},
    {0x0053,0x10},
    {0x0054,0xF0},
    {0x0055,0x08},
    {0x0056,0x52},
    {0x0057,0xC0},
    {0x0058,0x12},
    {0x0059,0xFF},
    {0x005A,0xFF},
    {0x005B,0xFF},
    {0x005C,0xFF},
    {0x005D,0xFF},
    {0x005E,0xFF},
    {0x005F,0xFF},
    {0x0060,0xFF},
    {0x0061,0xFF},
    {0x0062,0xFF},
    {0x0063,0xFF},
    {0x0064,0xFF},
    {0x0065,0xFF},
    {0x0066,0xFF},
    {0x0067,0xFF},
    {0x0068,0xFF},
    {0x0069,0xFF},
    {0x006A,0xFF},
    {0x006B,0xFF},
    {0x006C,0xFF},
    {0x006D,0xFF},
    {0x006E,0x16},
    {0x006F,0x18},
    {0x0070,0x10},
    {0x0071,0x13},
    {0x0072,0x0C},
    {0x0073,0x00},
    {0x0074,0x3B},
    {0x0075,0xFC},
    {0x0076,0x27},
    {0x0077,0x02},
    {0x0078,0x05},
    {0x0079,0x00},
    {0x007A,0x07},
    {0x007B,0x00},
    {0x007C,0x09},
    {0x007D,0x00},
    {0x007E,0x19},
    {0x007F,0x00},
    {0x0080,0xFF},
    {0x0081,0x00},
    {0x0082,0x16},
    {0x0083,0x50},
    {0x0084,0x01},
    {0x0085,0xF1},
    {0x0086,0x00},
    {0x0087,0x00},
    {0x0088,0x00},
    {0x0089,0x00},
    {0x008A,0x01},
    {0x008B,0x0A},
    {0x008C,0x00},
    {0x008D,0x00},
    {0x008E,0x00},
    {0x008F,0x00},
    {0x0090,0x00},
    {0x0091,0x00},
    {0x0092,0x00},
    {0x0093,0x00},
    {0x0094,0x40},
    {0x0095,0x10},
    {0x0096,0x00},
    {0x0097,0x00},
    {0x0098,0x00},
    {0x0099,0x00},
    {0x009A,0x57},
    {0x009B,0x70},
    {0x009C,0x0C},
    {0x009D,0x5F},
    {0x009E,0xB0},
    {0x009F,0x16},
    {0x00A0,0x58},
    {0x00A1,0xC0},
    {0x00A2,0x05},
    {0x00A3,0x60},
    {0x00A4,0x40},
    {0x00A5,0x06},
    {0x00A6,0x33},
    {0x00A7,0x70},
    {0x00A8,0x0D},
    {0x00A9,0x08},
    {0x00AA,0xC1},
    {0x00AB,0x1F},
    {0x00AC,0x69},
    {0x00AD,0x60},
    {0x00AE,0x0C},
    {0x00AF,0x9C},
    {0x00B0,0xA0},
    {0x00B1,0x16},
    {0x00B2,0x00},
    {0x00B3,0x00},
    {0x00B4,0x00},
    {0x00B5,0x00},
    {0x00B6,0x00},
    {0x00B7,0x00},
    {0x00B8,0x33},
    {0x00B9,0x70},
    {0x00BA,0x0D},
    {0x00BB,0x08},
    {0x00BC,0xC1},
    {0x00BD,0x1F},
    {0x00BE,0xDF},
    {0x00BF,0x90},
    {0x00C0,0x10},
    {0x00C1,0xFF},
    {0x00C2,0xFF},
    {0x00C3,0xFF},
    {0x00C4,0xDF},
    {0x00C5,0x90},
    {0x00C6,0x10},
    {0x00C7,0xFF},
    {0x00C8,0xFF},
    {0x00C9,0xFF},
    {0x00CA,0xDF},
    {0x00CB,0x90},
    {0x00CC,0x10},
    {0x00CD,0xFF},
    {0x00CE,0xFF},
    {0x00CF,0xFF},
    {0x00D0,0x0C},
    {0x00D1,0x60},
    {0x00D2,0x03},
    {0x00D3,0xFF},
    {0x00D4,0xFF},
    {0x00D5,0xFF},
    {0x00D6,0x00},
    {0x00D7,0xF0},
    {0x00D8,0xFF},
    {0x00D9,0xFF},
    {0x00DA,0xFF},
    {0x00DB,0xFF},
    {0x00DC,0x00},
    {0x00DD,0x00},
    {0x00DE,0x01},
    {0x00DF,0xC0},
    {0x00E0,0x0D},
    {0x00E1,0x82},
    {0x00E2,0x80},
    {0x00E3,0x20},
    {0x00E4,0xC7},
    {0x00E5,0x90},
    {0x00E6,0x19},
    {0x00E7,0x6B},
    {0x00E8,0x50},
    {0x00E9,0x0C},
    {0x00EA,0x9E},
    {0x00EB,0x90},
    {0x00EC,0x16},
    {0x00ED,0xFF},
    {0x00EE,0xFF},
    {0x00EF,0xFF},
    {0x00F0,0xFF},
    {0x00F1,0xFF},
    {0x00F2,0xFF},
    {0x00F3,0xFF},
    {0x00F4,0xFF},
    {0x00F5,0xFF},
    {0x00F6,0xFF},
    {0x00F7,0xFF},
    {0x00F8,0xFF},
    {0x00F9,0x00},
    {0x00FA,0x00},
    {0x00FB,0x01},
    {0x00FC,0x10},
    {0x00FD,0x00},
    {0x00FE,0xFF},
    {0x00FF,0x1F},
    {0x0100,0x00},
    {0x0101,0x00},
    {0x0102,0xF0},
    {0x0103,0xFF},
    {0x0104,0xFF},
    {0x0105,0xEF},
    {0x0106,0x1F},
    {0x0107,0xD8},
    {0x0108,0x90},
    {0x0109,0x0D},
    {0x010A,0xFD},
    {0x010B,0x71},
    {0x010C,0x05},
    {0x010D,0x08},
    {0x010E,0x00},
    {0x010F,0x00},
    {0x0110,0x00},
    {0x0111,0x80},
    {0x0112,0x00},
    {0x0113,0x58},
    {0x0114,0x00},
    {0x0115,0x00},
    {0x0116,0x00},
    {0x0117,0x80},
    {0x0118,0x00},
    {0x0119,0x61},
    {0x011A,0x00},
    {0x011B,0x00},
    {0x011C,0x00},
    {0x011D,0x20},
    {0x011E,0x06},
    {0x011F,0x08},
    {0x0120,0x00},
    {0x0121,0x00},
    {0x0122,0x00},
    {0x0123,0x00},
    {0x0124,0x00},
    {0x0125,0x00},
    {0x0126,0x00},
    {0x0127,0x00},
    {0x0128,0x00},
    {0x0129,0xA0},
    {0x012A,0x06},
    {0x012B,0xC6},
    {0x012C,0xD0},
    {0x012D,0x09},
    {0x012E,0x6A},
    {0x012F,0x91},
    {0x0130,0x06},
    {0x0131,0xC7},
    {0x0132,0xC0},
    {0x0133,0x09},
    {0x0134,0x6B},
    {0x0135,0x01},
    {0x0136,0x00},
    {0x0137,0x00},
    {0x0138,0x00},
    {0x0139,0x00},
    {0x013A,0x00},
    {0x013B,0x00},
    {0x013C,0x00},
    {0x013D,0x00},
    {0x013E,0xF0},
    {0x013F,0xFF},
    {0x0140,0xFF},
    {0x0141,0x0F},
    {0x0142,0x00},
    {0x0143,0x00},
    {0x0144,0xF0},
    {0x0145,0xFF},
    {0x0146,0xFF},
    {0x0147,0x0F},
    {0x0148,0x02},
    {0x0149,0xFF},
    {0x014A,0x0F},
    {0x014B,0x00},
    {0x014C,0x00},
    {0x014D,0xF0},
    {0x014E,0xFF},
    {0x014F,0x00},
    {0x0150,0x00},
    {0x0151,0x00},
    {0x0152,0x00},
    {0x0153,0x00},
    {0x0154,0x00},
    {0x0155,0x00},
    {0x0156,0x00},
    {0x0157,0x00},
    {0x0158,0x00},
    {0x0159,0x00},
    {0x015A,0x00},
    {0x015B,0x00},
    {0x015C,0x00},
    {0x015D,0x00},
    {0x015E,0x00},
    {0x015F,0x00},
    {0x0160,0x00},
    {0x0161,0x08},
    {0x0162,0x70},
    {0x0163,0x0D},
    {0x0164,0x08},
    {0x0165,0xC1},
    {0x0166,0x1F},
    {0x0167,0x08},
    {0x0168,0x70},
    {0x0169,0x0D},
    {0x016A,0x08},
    {0x016B,0xC1},
    {0x016C,0x1F},
    {0x016D,0x63},
    {0x016E,0x50},
    {0x016F,0x06},
    {0x0170,0x96},
    {0x0171,0x80},
    {0x0172,0x09},
    {0x0173,0x67},
    {0x0174,0x60},
    {0x0175,0x0C},
    {0x0176,0x9A},
    {0x0177,0xA0},
    {0x0178,0x16},
    {0x0179,0x00},
    {0x017A,0x00},
    {0x017B,0x00},
    {0x017C,0x2C},
    {0x017D,0x14},
    {0x017E,0x6C},
    {0x017F,0xAC},
    {0x0180,0x00},
    {0x0181,0x00},
    {0x0182,0x00},
    {0x0183,0x00},
    {0x0184,0x00},
    {0x0185,0x00},
    {0x0186,0x00},
    {0x0187,0x00},
    {0x0188,0x00},
    {0x0189,0x00},
    {0x018A,0x02},
    {0x018B,0x01},
    {0x018C,0x00},
    {0x018D,0x00},
    {0x018E,0x08},
    {0x018F,0x06},
    {0x0190,0x00},
    {0x0191,0x05},
    {0x0192,0x02},
    {0x0193,0x0B},
    {0x0194,0x04},
    {0x0195,0x00},
    {0x0196,0xFF},
    {0x0197,0x03},
    {0x0198,0xFF},
    {0x0199,0xFF},
    {0x019A,0xFF},
    {0x019B,0xFF},
    {0x019C,0x00},
    {0x019D,0x00},
    {0x019E,0x00},
    {0x019F,0x00},
    {0x01A0,0x00},
    {0x01A1,0x00},
    {0x01A2,0x00},
    {0x01A3,0x00},
    {0x01A4,0x00},
    {0x01A5,0x00},
    {0x01A6,0x00},
    {0x01A7,0x00},
    {0x01A8,0x00},
    {0x01A9,0x00},
    {0x01AA,0x00},
    {0x01AB,0x00},
    {0x01AC,0x00},
    {0x01AD,0x00},
    {0x01AE,0x00},
    {0x01AF,0x00},
    {0x01B0,0x01},
    {0x01B1,0x00},
    {0x01B2,0x00},
    {0x01B3,0x00},
    {0x01B4,0x00},
    {0x01B5,0x00},
    {0x01B6,0x00},
    {0x01B7,0x00},
    {0x01B8,0x00},
    {0x01B9,0x00},
    {0x01BA,0x08},
    {0x01BB,0x00},
    {0x01BC,0x00},
    {0x01BD,0x00},
    {0x01BE,0x00},
    {0x01BF,0x0F},
    {0x01C0,0x70},
    {0x01C1,0x08},
    {0x01C2,0x00},
    {0x01C3,0x0F},
    {0x01C4,0x70},
    {0x01C5,0x08},
    {0x01C6,0x00},
    {0x01C7,0x0F},
    {0x01C8,0x70},
    {0x01C9,0x08},
    {0x01CA,0x80},
    {0x01CB,0x02},
    {0x01CC,0xE0},
    {0x01CD,0x01},
    {0x01CE,0x0F},
    {0x01CF,0x04},
    {0x01D0,0x00},
    {0x01D1,0xF0},
    {0x01D2,0xFF},
    {0x01D3,0x70},
    {0x01D4,0x10},
    {0x01D5,0x00},
    {0x01D6,0x02},
    {0x01D7,0x00},
    {0x01D8,0x50},
    {0x01D9,0x00},
    {0x01DA,0xDC},
    {0x01DB,0x00},
    {0x01DC,0x3C},
    {0x01DD,0x00},
    {0x01DE,0x00},
    {0x01DF,0x08},
    {0x01E0,0x3C},
    {0x01E1,0x34},
    {0x01E2,0x28},
    {0x01E3,0x04},
    {0x01E4,0x69},
    {0x01E5,0x06},
    {0x01E6,0x3C},
    {0x01E7,0x04},
    {0x01E8,0x64},
    {0x01E9,0x00},
    {0x01EA,0x32},
    {0x01EB,0x00},
    {0x01EC,0xFF},
    {0x01ED,0xFF},
    {0x01EE,0xFF},
    {0x01EF,0xFF},
    {0x01F0,0xFF},
    {0x01F1,0xFF},
    {0x01F2,0xFF},
    {0x01F3,0xFF},
    {0x01F4,0x8C},
    {0x01F5,0x00},
    {0x01F6,0xFF},
    {0x01F7,0x00},
    {0x01F8,0x00},
    {0x01F9,0x00},
    {0x01FA,0x00},
    {0x01FB,0x1E},
    {0x01FC,0x07},
    {0x01FD,0x00},
    {0x01FE,0x00},
    {0x01FF,0xFF},
    {0x0200,0x80},
    {0x0201,0x02},
    {0x0202,0xE0},
    {0x0203,0x01},
    {0x0204,0x03},
    {0x0205,0x05},
    {0x0206,0x99},
    {0x0207,0x50},
    {0x0208,0x00},
    {0x0209,0xFF},
    {0x020A,0x00},
    {0x020B,0x00},
    {0x020C,0x00},
    {0x020D,0x00},
    {0x020E,0x00},
    {0x020F,0x0F},
    {0x0210,0x00},
    {0x0211,0x00},
    {0x0212,0x00},
    {0x0213,0x00},
    {0x0214,0x00},
{SENSOR_REG_DELAY,0x14},
    {0x0000,0x1a},
 {SENSOR_REG_END, 0x00},
};

static struct regval_list sensor_init_regs_3840_2160_15fps_mipi[] = {
    //UHD_15fps_No.1
    {0x0000,0x10},
    {0x0001,0x00},
    {0x0002,0x04},
    {0x0003,0x00},
    {0x0004,0x20},
    {0x0005,0x06},
    {0x0006,0x8a},
    {0x0007,0x11},
    {0x0008,0x00},
    {0x0009,0x62},
    {0x000A,0x04},
    {0x000B,0x00},
    {0x000C,0x0F},
    {0x000D,0xB0},
    {0x000E,0x03},
    {0x000F,0x0F},
    {0x0010,0xB0},
    {0x0011,0x03},
    {0x0012,0x0F},
    {0x0013,0xB0},
    {0x0014,0x03},
    {0x0015,0x00},
    {0x0016,0x00},
    {0x0017,0x3C},
    {0x0018,0x02},
    {0x0019,0x3C},
    {0x001A,0x02},
    {0x001B,0x3C},
    {0x001C,0x02},
    {0x001D,0x16},
    {0x001E,0x2D},
    {0x001F,0x16},
    {0x0020,0x16},
    {0x0021,0x00},
    {0x0022,0x00},
    {0x0023,0x00},
    {0x0024,0x00},
    {0x0025,0x00},
    {0x0026,0x00},
    {0x0027,0x00},
    {0x0028,0x00},
    {0x0029,0x00},
    {0x002A,0x61},
    {0x002B,0x00},
    {0x002C,0x03},
    {0x002D,0x03},
    {0x002E,0x67},
    {0x002F,0x42},
    {0x0030,0x10},
    {0x0031,0x02},
    {0x0032,0x58},
    {0x0033,0x82},
    {0x0034,0x00},
    {0x0035,0x67},
    {0x0036,0x48},
    {0x0037,0x0A},
    {0x0038,0x01},
    {0x0039,0x58},
    {0x003A,0x82},
    {0x003B,0x00},
    {0x003C,0x03},
    {0x003D,0x0E},
    {0x003E,0x00},
    {0x003F,0x00},
    {0x0040,0x28},
    {0x0041,0x05},
    {0x0042,0x0C},
    {0x0043,0x01},
    {0x0044,0x00},
    {0x0045,0xC0},
    {0x0046,0x00},
    {0x0047,0x2B},//BLC 统计值开启
    {0x0048,0x00},
    {0x0049,0x00},
    {0x004A,0xFF},
    {0x004B,0x01},
    {0x004C,0xFF},
    {0x004D,0x01},
    {0x004E,0x06},
    {0x004F,0xB8},
    {0x0050,0x0E},
    {0x0051,0x00},
    {0x0052,0x06},
    {0x0053,0x10},
    {0x0054,0xF0},
    {0x0055,0x08},
    {0x0056,0x52},
    {0x0057,0xC0},
    {0x0058,0x12},
    {0x0059,0xFF},
    {0x005A,0xFF},
    {0x005B,0xFF},
    {0x005C,0xFF},
    {0x005D,0xFF},
    {0x005E,0xFF},
    {0x005F,0xFF},
    {0x0060,0xFF},
    {0x0061,0xFF},
    {0x0062,0xFF},
    {0x0063,0xFF},
    {0x0064,0xFF},
    {0x0065,0xFF},
    {0x0066,0xFF},
    {0x0067,0xFF},
    {0x0068,0xFF},
    {0x0069,0xFF},
    {0x006A,0xFF},
    {0x006B,0xFF},
    {0x006C,0xFF},
    {0x006D,0xFF},
    {0x006E,0x16},
    {0x006F,0x18},
    {0x0070,0x10},
    {0x0071,0x13},
    {0x0072,0x0C},
    {0x0073,0x00},
    {0x0074,0x3B},
    {0x0075,0xFC},
    {0x0076,0x27},
    {0x0077,0x02},
    {0x0078,0x05},
    {0x0079,0x00},
    {0x007A,0x07},
    {0x007B,0x00},
    {0x007C,0x09},
    {0x007D,0x00},
    {0x007E,0x19},
    {0x007F,0x00},
    {0x0080,0xFF},
    {0x0081,0x00},
    {0x0082,0x16},
    {0x0083,0x50},
    {0x0084,0x01},
    {0x0085,0xF1},
    {0x0086,0x00},
    {0x0087,0x00},
    {0x0088,0x00},
    {0x0089,0x00},
    {0x008A,0x01},
    {0x008B,0x0A},
    {0x008C,0x00},
    {0x008D,0x00},
    {0x008E,0x00},
    {0x008F,0x00},
    {0x0090,0x00},
    {0x0091,0x00},
    {0x0092,0x00},
    {0x0093,0x00},
    {0x0094,0x40},
    {0x0095,0x10},
    {0x0096,0x00},
    {0x0097,0x00},
    {0x0098,0x00},
    {0x0099,0x00},
    {0x009A,0x57},
    {0x009B,0x70},
    {0x009C,0x0C},
    {0x009D,0x5F},
    {0x009E,0xB0},
    {0x009F,0x16},
    {0x00A0,0x58},
    {0x00A1,0xC0},
    {0x00A2,0x05},
    {0x00A3,0x60},
    {0x00A4,0x40},
    {0x00A5,0x06},
    {0x00A6,0x33},
    {0x00A7,0x70},
    {0x00A8,0x0D},
    {0x00A9,0x08},
    {0x00AA,0xC1},
    {0x00AB,0x1F},
    {0x00AC,0x69},
    {0x00AD,0x60},
    {0x00AE,0x0C},
    {0x00AF,0x9C},
    {0x00B0,0xA0},
    {0x00B1,0x16},
    {0x00B2,0x00},
    {0x00B3,0x00},
    {0x00B4,0x00},
    {0x00B5,0x00},
    {0x00B6,0x00},
    {0x00B7,0x00},
    {0x00B8,0x33},
    {0x00B9,0x70},
    {0x00BA,0x0D},
    {0x00BB,0x08},
    {0x00BC,0xC1},
    {0x00BD,0x1F},
    {0x00BE,0xDF},
    {0x00BF,0x90},
    {0x00C0,0x10},
    {0x00C1,0xFF},
    {0x00C2,0xFF},
    {0x00C3,0xFF},
    {0x00C4,0xDF},
    {0x00C5,0x90},
    {0x00C6,0x10},
    {0x00C7,0xFF},
    {0x00C8,0xFF},
    {0x00C9,0xFF},
    {0x00CA,0xDF},
    {0x00CB,0x90},
    {0x00CC,0x10},
    {0x00CD,0xFF},
    {0x00CE,0xFF},
    {0x00CF,0xFF},
    {0x00D0,0x0C},
    {0x00D1,0x60},
    {0x00D2,0x03},
    {0x00D3,0xFF},
    {0x00D4,0xFF},
    {0x00D5,0xFF},
    {0x00D6,0x00},
    {0x00D7,0xF0},
    {0x00D8,0xFF},
    {0x00D9,0xFF},
    {0x00DA,0xFF},
    {0x00DB,0xFF},
    {0x00DC,0x00},
    {0x00DD,0x00},
    {0x00DE,0x01},
    {0x00DF,0xC0},
    {0x00E0,0x0D},
    {0x00E1,0x82},
    {0x00E2,0x80},
    {0x00E3,0x20},
    {0x00E4,0xC7},
    {0x00E5,0x90},
    {0x00E6,0x19},
    {0x00E7,0x6B},
    {0x00E8,0x50},
    {0x00E9,0x0C},
    {0x00EA,0x9E},
    {0x00EB,0x90},
    {0x00EC,0x16},
    {0x00ED,0xFF},
    {0x00EE,0xFF},
    {0x00EF,0xFF},
    {0x00F0,0xFF},
    {0x00F1,0xFF},
    {0x00F2,0xFF},
    {0x00F3,0xFF},
    {0x00F4,0xFF},
    {0x00F5,0xFF},
    {0x00F6,0xFF},
    {0x00F7,0xFF},
    {0x00F8,0xFF},
    {0x00F9,0x00},
    {0x00FA,0x00},
    {0x00FB,0x01},
    {0x00FC,0x10},
    {0x00FD,0x00},
    {0x00FE,0xFF},
    {0x00FF,0x1F},
    {0x0100,0x00},
    {0x0101,0x00},
    {0x0102,0xF0},
    {0x0103,0xFF},
    {0x0104,0xFF},
    {0x0105,0xEF},
    {0x0106,0x1F},
    {0x0107,0xD8},
    {0x0108,0x90},
    {0x0109,0x0D},
    {0x010A,0xFD},
    {0x010B,0x71},
    {0x010C,0x05},
    {0x010D,0x08},
    {0x010E,0x00},
    {0x010F,0x00},
    {0x0110,0x00},
    {0x0111,0x80},
    {0x0112,0x00},
    {0x0113,0x58},
    {0x0114,0x00},
    {0x0115,0x00},
    {0x0116,0x00},
    {0x0117,0x80},
    {0x0118,0x00},
    {0x0119,0x61},
    {0x011A,0x00},
    {0x011B,0x00},
    {0x011C,0x00},
    {0x011D,0x20},
    {0x011E,0x06},
    {0x011F,0x08},
    {0x0120,0x00},
    {0x0121,0x00},
    {0x0122,0x00},
    {0x0123,0x00},
    {0x0124,0x00},
    {0x0125,0x00},
    {0x0126,0x00},
    {0x0127,0x00},
    {0x0128,0x00},
    {0x0129,0xA0},
    {0x012A,0x06},
    {0x012B,0xC6},
    {0x012C,0xD0},
    {0x012D,0x09},
    {0x012E,0x6A},
    {0x012F,0x91},
    {0x0130,0x06},
    {0x0131,0xC7},
    {0x0132,0xC0},
    {0x0133,0x09},
    {0x0134,0x6B},
    {0x0135,0x01},
    {0x0136,0x00},
    {0x0137,0x00},
    {0x0138,0x00},
    {0x0139,0x00},
    {0x013A,0x00},
    {0x013B,0x00},
    {0x013C,0x00},
    {0x013D,0x00},
    {0x013E,0xF0},
    {0x013F,0xFF},
    {0x0140,0xFF},
    {0x0141,0x0F},
    {0x0142,0x00},
    {0x0143,0x00},
    {0x0144,0xF0},
    {0x0145,0xFF},
    {0x0146,0xFF},
    {0x0147,0x0F},
    {0x0148,0x02},
    {0x0149,0xFF},
    {0x014A,0x0F},
    {0x014B,0x00},
    {0x014C,0x00},
    {0x014D,0xF0},
    {0x014E,0xFF},
    {0x014F,0x00},
    {0x0150,0x00},
    {0x0151,0x00},
    {0x0152,0x00},
    {0x0153,0x00},
    {0x0154,0x00},
    {0x0155,0x00},
    {0x0156,0x00},
    {0x0157,0x00},
    {0x0158,0x00},
    {0x0159,0x00},
    {0x015A,0x00},
    {0x015B,0x00},
    {0x015C,0x00},
    {0x015D,0x00},
    {0x015E,0x00},
    {0x015F,0x00},
    {0x0160,0x00},
    {0x0161,0x08},
    {0x0162,0x70},
    {0x0163,0x0D},
    {0x0164,0x08},
    {0x0165,0xC1},
    {0x0166,0x1F},
    {0x0167,0x08},
    {0x0168,0x70},
    {0x0169,0x0D},
    {0x016A,0x08},
    {0x016B,0xC1},
    {0x016C,0x1F},
    {0x016D,0x63},
    {0x016E,0x50},
    {0x016F,0x06},
    {0x0170,0x96},
    {0x0171,0x80},
    {0x0172,0x09},
    {0x0173,0x67},
    {0x0174,0x60},
    {0x0175,0x0C},
    {0x0176,0x9A},
    {0x0177,0xA0},
    {0x0178,0x16},
    {0x0179,0x00},
    {0x017A,0x00},
    {0x017B,0x00},
    {0x017C,0x2C},
    {0x017D,0x14},
    {0x017E,0x6C},
    {0x017F,0xAC},
    {0x0180,0x00},
    {0x0181,0x00},
    {0x0182,0x00},
    {0x0183,0x00},
    {0x0184,0x00},
    {0x0185,0x00},
    {0x0186,0x00},
    {0x0187,0x00},
    {0x0188,0x00},
    {0x0189,0x00},
    {0x018A,0x02},
    {0x018B,0x01},
    {0x018C,0x00},
    {0x018D,0x00},
    {0x018E,0x08},
    {0x018F,0x06},
    {0x0190,0x00},
    {0x0191,0x05},
    {0x0192,0x02},
    {0x0193,0x0B},
    {0x0194,0x04},
    {0x0195,0x00},
    {0x0196,0xFF},
    {0x0197,0x03},
    {0x0198,0xFF},
    {0x0199,0xFF},
    {0x019A,0xFF},
    {0x019B,0xFF},
    {0x019C,0x00},
    {0x019D,0x00},
    {0x019E,0x00},
    {0x019F,0x00},
    {0x01A0,0x00},
    {0x01A1,0x00},
    {0x01A2,0x00},
    {0x01A3,0x00},
    {0x01A4,0x00},
    {0x01A5,0x00},
    {0x01A6,0x00},
    {0x01A7,0x00},
    {0x01A8,0x00},
    {0x01A9,0x00},
    {0x01AA,0x00},
    {0x01AB,0x00},
    {0x01AC,0x00},
    {0x01AD,0x00},
    {0x01AE,0x00},
    {0x01AF,0x00},
    {0x01B0,0x01},
    {0x01B1,0x00},
    {0x01B2,0x00},
    {0x01B3,0x00},
    {0x01B4,0x00},
    {0x01B5,0x00},
    {0x01B6,0x00},
    {0x01B7,0x00},
    {0x01B8,0x00},
    {0x01B9,0x00},
    {0x01BA,0x08},
    {0x01BB,0x00},
    {0x01BC,0x00},
    {0x01BD,0x00},
    {0x01BE,0x00},
    {0x01BF,0x0F},
    {0x01C0,0x70},
    {0x01C1,0x08},
    {0x01C2,0x00},
    {0x01C3,0x0F},
    {0x01C4,0x70},
    {0x01C5,0x08},
    {0x01C6,0x00},
    {0x01C7,0x0F},
    {0x01C8,0x70},
    {0x01C9,0x08},
    {0x01CA,0x80},
    {0x01CB,0x02},
    {0x01CC,0xE0},
    {0x01CD,0x01},
    {0x01CE,0x0F},
    {0x01CF,0x04},
    {0x01D0,0x00},
    {0x01D1,0xF0},
    {0x01D2,0xFF},
    {0x01D3,0x70},
    {0x01D4,0x10},
    {0x01D5,0x00},
    {0x01D6,0x02},
    {0x01D7,0x00},
    {0x01D8,0x50},
    {0x01D9,0x00},
    {0x01DA,0xDC},
    {0x01DB,0x00},
    {0x01DC,0x3C},
    {0x01DD,0x00},
    {0x01DE,0x00},
    {0x01DF,0x08},
    {0x01E0,0x3C},
    {0x01E1,0x34},
    {0x01E2,0x28},
    {0x01E3,0x04},
    {0x01E4,0x69},
    {0x01E5,0x06},
    {0x01E6,0x3C},
    {0x01E7,0x04},
    {0x01E8,0x64},
    {0x01E9,0x00},
    {0x01EA,0x32},
    {0x01EB,0x00},
    {0x01EC,0xFF},
    {0x01ED,0xFF},
    {0x01EE,0xFF},
    {0x01EF,0xFF},
    {0x01F0,0xFF},
    {0x01F1,0xFF},
    {0x01F2,0xFF},
    {0x01F3,0xFF},
    {0x01F4,0x8C},
    {0x01F5,0x00},
    {0x01F6,0xFF},
    {0x01F7,0x00},
    {0x01F8,0x00},
    {0x01F9,0x00},
    {0x01FA,0x00},
    {0x01FB,0x1E},
    {0x01FC,0x07},
    {0x01FD,0x00},
    {0x01FE,0x00},
    {0x01FF,0xFF},
    {0x0200,0x80},
    {0x0201,0x02},
    {0x0202,0xE0},
    {0x0203,0x01},
    {0x0204,0x03},
    {0x0205,0x05},
    {0x0206,0x99},
    {0x0207,0x50},
    {0x0208,0x00},
    {0x0209,0xFF},
    {0x020A,0x00},
    {0x020B,0x00},
    {0x020C,0x00},
    {0x020D,0x00},
    {0x020E,0x00},
    {0x020F,0x0F},
    {0x0210,0x00},
    {0x0211,0x00},
    {0x0212,0x00},
    {0x0213,0x00},
    {0x0214,0x00},
{SENSOR_REG_DELAY,0x14},
    {0x0000,0x1a},
 {SENSOR_REG_END, 0x00},
};

static struct regval_list sensor_init_regs_1920_1080_30fps_mipi[] = {
    {0x0000,0x10},
    {0x0001,0x00},
    {0x0002,0x00},
    {0x0003,0x00},
    {0x0004,0x20},
    {0x0005,0x08},
    {0x0006,0xCA},
    {0x0007,0x08},
    {0x0008,0x00},
    {0x0009,0x65},
    {0x000A,0x04},
    {0x000B,0x00},
    {0x000C,0x0F},
    {0x000D,0xB0},
    {0x000E,0x03},
    {0x000F,0x0F},
    {0x0010,0xB0},
    {0x0011,0x03},
    {0x0012,0x0F},
    {0x0013,0xB0},
    {0x0014,0x03},
    {0x0015,0x00},
    {0x0016,0x00},
    {0x0017,0x3C},
    {0x0018,0x02},
    {0x0019,0x3C},
    {0x001A,0x02},
    {0x001B,0x3C},
    {0x001C,0x02},
    {0x001D,0x16},
    {0x001E,0x2D},
    {0x001F,0x16},
    {0x0020,0x16},
    {0x0021,0x00},
    {0x0022,0x00},
    {0x0023,0x00},
    {0x0024,0x00},
    {0x0025,0x00},
    {0x0026,0x00},
    {0x0027,0x00},
    {0x0028,0x00},
    {0x0029,0x00},
    {0x002A,0x61},
    {0x002B,0x00},
    {0x002C,0x03},
    {0x002D,0x03},
    {0x002E,0x67},
    {0x002F,0x42},
    {0x0030,0x08},
    {0x0031,0x02},
    {0x0032,0x58},
    {0x0033,0x82},
    {0x0034,0x00},
    {0x0035,0x67},
    {0x0036,0x48},
    {0x0037,0x0A},
    {0x0038,0x01},
    {0x0039,0x58},
    {0x003A,0x82},
    {0x003B,0x00},
    {0x003C,0x03},
    {0x003D,0x16},
    {0x003E,0x00},
    {0x003F,0x00},
    {0x0040,0x28},
    {0x0041,0x05},
    {0x0042,0x0C},
    {0x0043,0x01},
    {0x0044,0x00},
    {0x0045,0xD0},
    {0x0046,0x00},
    {0x0047,0x2b},
    {0x0048,0x00},
    {0x0049,0x00},
    {0x004A,0x00},
    {0x004B,0x00},
    {0x004C,0x00},
    {0x004D,0x00},
    {0x004E,0x06},
    {0x004F,0xB8},
    {0x0050,0x0E},
    {0x0051,0x00},
    {0x0052,0x06},
    {0x0053,0x18},
    {0x0054,0xF0},
    {0x0055,0x08},
    {0x0056,0x22},
    {0x0057,0xD0},
    {0x0058,0x09},
    {0x0059,0xFF},
    {0x005A,0xFF},
    {0x005B,0xFF},
    {0x005C,0xFF},
    {0x005D,0xFF},
    {0x005E,0xFF},
    {0x005F,0xFF},
    {0x0060,0xFF},
    {0x0061,0xFF},
    {0x0062,0xFF},
    {0x0063,0xFF},
    {0x0064,0xFF},
    {0x0065,0xFF},
    {0x0066,0xFF},
    {0x0067,0xFF},
    {0x0068,0xFF},
    {0x0069,0xFF},
    {0x006A,0xFF},
    {0x006B,0xFF},
    {0x006C,0xFF},
    {0x006D,0xFF},
    {0x006E,0x16},
    {0x006F,0x18},
    {0x0070,0x10},
    {0x0071,0x13},
    {0x0072,0x0C},
    {0x0073,0x00},
    {0x0074,0x3B},
    {0x0075,0xFC},
    {0x0076,0x26},
    {0x0077,0x02},
    {0x0078,0x05},
    {0x0079,0x00},
    {0x007A,0x07},
    {0x007B,0x00},
    {0x007C,0x09},
    {0x007D,0x00},
    {0x007E,0x12},
    {0x007F,0x00},
    {0x0080,0x99},
    {0x0081,0x00},
    {0x0082,0x16},
    {0x0083,0x50},
    {0x0084,0x01},
    {0x0085,0xF1},
    {0x0086,0x00},
    {0x0087,0x00},
    {0x0088,0x00},
    {0x0089,0x00},
    {0x008A,0x00},
    {0x008B,0x0A},
    {0x008C,0x00},
    {0x008D,0x00},
    {0x008E,0x00},
    {0x008F,0x00},
    {0x0090,0x00},
    {0x0091,0x00},
    {0x0092,0x00},
    {0x0093,0x00},
    {0x0094,0x40},
    {0x0095,0x18},
    {0x0096,0x00},
    {0x0097,0x00},
    {0x0098,0x00},
    {0x0099,0x00},
    {0x009A,0x44},
    {0x009B,0xB0},
    {0x009C,0x07},
    {0x009D,0x07},
    {0x009E,0x80},
    {0x009F,0x07},
    {0x00A0,0x45},
    {0x00A1,0x90},
    {0x00A2,0x04},
    {0x00A3,0x08},
    {0x00A4,0xC0},
    {0x00A5,0x00},
    {0x00A6,0x1B},
    {0x00A7,0x40},
    {0x00A8,0x08},
    {0x00A9,0xA6},
    {0x00AA,0xC0},
    {0x00AB,0x10},
    {0x00AC,0x57},
    {0x00AD,0xA0},
    {0x00AE,0x07},
    {0x00AF,0x37},
    {0x00B0,0x70},
    {0x00B1,0x07},
    {0x00B2,0x00},
    {0x00B3,0x00},
    {0x00B4,0x00},
    {0x00B5,0x00},
    {0x00B6,0x00},
    {0x00B7,0x00},
    {0x00B8,0x1B},
    {0x00B9,0x40},
    {0x00BA,0x08},
    {0x00BB,0xA6},
    {0x00BC,0xC0},
    {0x00BD,0x10},
    {0x00BE,0x8C},
    {0x00BF,0x70},
    {0x00C0,0x0A},
    {0x00C1,0xFF},
    {0x00C2,0xFF},
    {0x00C3,0xFF},
    {0x00C4,0x8C},
    {0x00C5,0x70},
    {0x00C6,0x0A},
    {0x00C7,0xFF},
    {0x00C8,0xFF},
    {0x00C9,0xFF},
    {0x00CA,0x8C},
    {0x00CB,0x70},
    {0x00CC,0x0A},
    {0x00CD,0xFF},
    {0x00CE,0xFF},
    {0x00CF,0xFF},
    {0x00D0,0x0C},
    {0x00D1,0xE0},
    {0x00D2,0x01},
    {0x00D3,0xFF},
    {0x00D4,0xFF},
    {0x00D5,0xFF},
    {0x00D6,0x00},
    {0x00D7,0xF0},
    {0x00D8,0xFF},
    {0x00D9,0xFF},
    {0x00DA,0xFF},
    {0x00DB,0xFF},
    {0x00DC,0x00},
    {0x00DD,0x00},
    {0x00DE,0x01},
    {0x00DF,0x60},
    {0x00E0,0x09},
    {0x00E1,0x8C},
    {0x00E2,0xB0},
    {0x00E3,0x09},
    {0x00E4,0x7B},
    {0x00E5,0xA0},
    {0x00E6,0x07},
    {0x00E7,0x58},
    {0x00E8,0x90},
    {0x00E9,0x07},
    {0x00EA,0x38},
    {0x00EB,0x60},
    {0x00EC,0x07},
    {0x00ED,0xFF},
    {0x00EE,0xFF},
    {0x00EF,0xFF},
    {0x00F0,0xFF},
    {0x00F1,0xFF},
    {0x00F2,0xFF},
    {0x00F3,0xFF},
    {0x00F4,0xFF},
    {0x00F5,0xFF},
    {0x00F6,0xFF},
    {0x00F7,0xFF},
    {0x00F8,0xFF},
    {0x00F9,0x00},
    {0x00FA,0x00},
    {0x00FB,0x01},
    {0x00FC,0x10},
    {0x00FD,0x00},
    {0x00FE,0xFF},
    {0x00FF,0x1F},
    {0x0100,0x00},
    {0x0101,0x00},
    {0x0102,0xF0},
    {0x0103,0xFF},
    {0x0104,0xFF},
    {0x0105,0xEF},
    {0x0106,0x10},
    {0x0107,0x85},
    {0x0108,0x60},
    {0x0109,0x08},
    {0x010A,0x0D},
    {0x010B,0xF1},
    {0x010C,0x03},
    {0x010D,0x08},
    {0x010E,0x00},
    {0x010F,0x00},
    {0x0110,0x00},
    {0x0111,0x80},
    {0x0112,0x00},
    {0x0113,0x40},
    {0x0114,0x00},
    {0x0115,0x00},
    {0x0116,0x00},
    {0x0117,0x80},
    {0x0118,0x00},
    {0x0119,0x49},
    {0x011A,0x00},
    {0x011B,0x00},
    {0x011C,0x00},
    {0x011D,0xA0},
    {0x011E,0x04},
    {0x011F,0x08},
    {0x0120,0x00},
    {0x0121,0x00},
    {0x0122,0x00},
    {0x0123,0x00},
    {0x0124,0x00},
    {0x0125,0x00},
    {0x0126,0x00},
    {0x0127,0x00},
    {0x0128,0x00},
    {0x0129,0x80},
    {0x012A,0x05},
    {0x012B,0x7A},
    {0x012C,0x80},
    {0x012D,0x03},
    {0x012E,0x77},
    {0x012F,0x70},
    {0x0130,0x05},
    {0x0131,0x7B},
    {0x0132,0x70},
    {0x0133,0x03},
    {0x0134,0x78},
    {0x0135,0x00},
    {0x0136,0x00},
    {0x0137,0x00},
    {0x0138,0x00},
    {0x0139,0x00},
    {0x013A,0x00},
    {0x013B,0x00},
    {0x013C,0x00},
    {0x013D,0x00},
    {0x013E,0xF0},
    {0x013F,0xFF},
    {0x0140,0xFF},
    {0x0141,0x0F},
    {0x0142,0x00},
    {0x0143,0x00},
    {0x0144,0xF0},
    {0x0145,0xFF},
    {0x0146,0xFF},
    {0x0147,0x0F},
    {0x0148,0x02},
    {0x0149,0xFF},
    {0x014A,0x0F},
    {0x014B,0x00},
    {0x014C,0x00},
    {0x014D,0xF0},
    {0x014E,0xFF},
    {0x014F,0x00},
    {0x0150,0x00},
    {0x0151,0x00},
    {0x0152,0x00},
    {0x0153,0x00},
    {0x0154,0x00},
    {0x0155,0x00},
    {0x0156,0x00},
    {0x0157,0x00},
    {0x0158,0x00},
    {0x0159,0x00},
    {0x015A,0x00},
    {0x015B,0x00},
    {0x015C,0x00},
    {0x015D,0x00},
    {0x015E,0x00},
    {0x015F,0x00},
    {0x0160,0x00},
    {0x0161,0x08},
    {0x0162,0x40},
    {0x0163,0x08},
    {0x0164,0xA6},
    {0x0165,0xC0},
    {0x0166,0x10},
    {0x0167,0x08},
    {0x0168,0x40},
    {0x0169,0x08},
    {0x016A,0xA6},
    {0x016B,0xC0},
    {0x016C,0x10},
    {0x016D,0x51},
    {0x016E,0x30},
    {0x016F,0x05},
    {0x0170,0x31},
    {0x0171,0x30},
    {0x0172,0x03},
    {0x0173,0x55},
    {0x0174,0xA0},
    {0x0175,0x07},
    {0x0176,0x35},
    {0x0177,0x70},
    {0x0178,0x07},
    {0x0179,0x00},
    {0x017A,0x00},
    {0x017B,0x00},
    {0x017C,0x2C},
    {0x017D,0x0A},
    {0x017E,0x6C},
    {0x017F,0xAC},
    {0x0180,0x00},
    {0x0181,0x00},
    {0x0182,0x00},
    {0x0183,0x00},
    {0x0184,0x00},
    {0x0185,0x00},
    {0x0186,0x00},
    {0x0187,0x00},
    {0x0188,0x00},
    {0x0189,0x00},
    {0x018A,0x00},
    {0x018B,0x01},
    {0x018C,0x00},
    {0x018D,0x00},
    {0x018E,0x08},
    {0x018F,0x06},
    {0x0190,0x00},
    {0x0191,0x05},
    {0x0192,0x02},
    {0x0193,0x0B},
    {0x0194,0x00},
    {0x0195,0x04},
    {0x0196,0xFF},
    {0x0197,0x03},
    {0x0198,0xFF},
    {0x0199,0xFF},
    {0x019A,0xFF},
    {0x019B,0xFF},
    {0x019C,0x00},
    {0x019D,0x00},
    {0x019E,0x00},
    {0x019F,0x00},
    {0x01A0,0x00},
    {0x01A1,0x00},
    {0x01A2,0x00},
    {0x01A3,0x00},
    {0x01A4,0x00},
    {0x01A5,0x00},
    {0x01A6,0x00},
    {0x01A7,0x00},
    {0x01A8,0x00},
    {0x01A9,0x00},
    {0x01AA,0x00},
    {0x01AB,0x00},
    {0x01AC,0x00},
    {0x01AD,0x00},
    {0x01AE,0x00},
    {0x01AF,0x00},
    {0x01B0,0x01},
    {0x01B1,0x00},
    {0x01B2,0x00},
    {0x01B3,0x00},
    {0x01B4,0x00},
    {0x01B5,0x00},
    {0x01B6,0x00},
    {0x01B7,0x00},
    {0x01B8,0x00},
    {0x01B9,0x00},
    {0x01BA,0x08},
    {0x01BB,0x00},
    {0x01BC,0x00},
    {0x01BD,0x00},
    {0x01BE,0x80},
    {0x01BF,0x07},
    {0x01C0,0x38},
    {0x01C1,0x04},
    {0x01C2,0x80},
    {0x01C3,0x07},
    {0x01C4,0x38},
    {0x01C5,0x04},
    {0x01C6,0x80},
    {0x01C7,0x07},
    {0x01C8,0x38},
    {0x01C9,0x04},
    {0x01CA,0x80},
    {0x01CB,0x02},
    {0x01CC,0xE0},
    {0x01CD,0x01},
    {0x01CE,0x0F},
    {0x01CF,0x04},
    {0x01D0,0x00},
    {0x01D1,0xF0},
    {0x01D2,0xFF},
    {0x01D3,0x70},
    {0x01D4,0x10},
    {0x01D5,0x00},
    {0x01D6,0x02},
    {0x01D7,0x00},
    {0x01D8,0x50},
    {0x01D9,0x00},
    {0x01DA,0xDC},
    {0x01DB,0x00},
    {0x01DC,0x3C},
    {0x01DD,0x00},
    {0x01DE,0x00},
    {0x01DF,0x08},
    {0x01E0,0x3C},
    {0x01E1,0x34},
    {0x01E2,0x28},
    {0x01E3,0x04},
    {0x01E4,0x69},
    {0x01E5,0x06},
    {0x01E6,0x3C},
    {0x01E7,0x04},
    {0x01E8,0x64},
    {0x01E9,0x00},
    {0x01EA,0x32},
    {0x01EB,0x00},
    {0x01EC,0xFF},
    {0x01ED,0xFF},
    {0x01EE,0xFF},
    {0x01EF,0xFF},
    {0x01F0,0xFF},
    {0x01F1,0xFF},
    {0x01F2,0xFF},
    {0x01F3,0xFF},
    {0x01F4,0x8C},
    {0x01F5,0x00},
    {0x01F6,0xFF},
    {0x01F7,0x00},
    {0x01F8,0x00},
    {0x01F9,0x00},
    {0x01FA,0x00},
    {0x01FB,0x1E},
    {0x01FC,0x07},
    {0x01FD,0x00},
    {0x01FE,0x00},
    {0x01FF,0xFF},
    {0x0200,0x80},
    {0x0201,0x02},
    {0x0202,0xE0},
    {0x0203,0x01},
    {0x0204,0x03},
    {0x0205,0x05},
    {0x0206,0x99},
    {0x0207,0x50},
    {0x0208,0x00},
    {0x0209,0xFF},
    {0x020A,0x00},
    {0x020B,0x00},
    {0x020C,0x00},
    {0x020D,0x00},
    {0x020E,0x00},
    {0x020F,0x0F},
    {0x0210,0x00},
    {0x0211,0x00},
    {0x0212,0x00},
    {0x0213,0x00},
    {0x0214,0x00},
{SENSOR_REG_DELAY,0x14},
    {0x0000,0x1a},
 {SENSOR_REG_END, 0x00},
};

static struct tx_isp_sensor_win_setting sensor_win_sizes[] = {
	/* 3840*2160 */

	{
		.width = 3840,
		.height = 2160,
		.fps = 15 << 16 | 1,
		.mbus_code = TISP_VI_FMT_SRGGB12_1X12,
		.colorspace = TISP_COLORSPACE_SRGB,
		.regs = sensor_init_regs_3840_2160_15fps_mipi,
	},
	{
		.width = 3840,
		.height = 2160,
		.fps = 25 << 16 | 1,
		.mbus_code = TISP_VI_FMT_SRGGB12_1X12,
		.colorspace = TISP_COLORSPACE_SRGB,
		.regs = sensor_init_regs_3840_2160_25fps_mipi,
	},
	{
		.width = 1920,
		.height = 1080,
		.fps = 30 << 16 | 1,
		.mbus_code = TISP_VI_FMT_SRGGB12_1X12,
		.colorspace = TISP_COLORSPACE_SRGB,
		.regs = sensor_init_regs_1920_1080_30fps_mipi,
	},
};
struct tx_isp_sensor_win_setting *wsize = &sensor_win_sizes[0];

static struct regval_list sensor_stream_on_mipi[] = {
	{0x0000,0x1a},
	{SENSOR_REG_END, 0x00},
};

static struct regval_list sensor_stream_off_mipi[] = {
	{0x0000, 0x10},
	{SENSOR_REG_END, 0x00},
};

int sensor_read(struct tx_isp_subdev *sd, uint16_t reg,
		unsigned char *value)
{
	struct i2c_client *client = tx_isp_get_subdevdata(sd);
	uint8_t buf[2] = {(reg >> 8) & 0xff, reg & 0xff};
	struct i2c_msg msg[2] = {
		[0] = {
			.addr = client->addr,
			.flags = 0,
			.len = 2,
			.buf = buf,
		},
		[1] = {
			.addr = client->addr,
			.flags = I2C_M_RD,
			.len = 1,
			.buf = value,
		}
	};
	int ret;
	ret = private_i2c_transfer(client->adapter, msg, 2);
	if (ret > 0)
		ret = 0;

	return ret;
}

int sensor_write(struct tx_isp_subdev *sd, uint16_t reg,
		 unsigned char value)
{
	struct i2c_client *client = tx_isp_get_subdevdata(sd);
	uint8_t buf[3] = {(reg >> 8) & 0xff, reg & 0xff, value};
	struct i2c_msg msg = {
		.addr = client->addr,
		.flags = 0,
		.len = 3,
		.buf = buf,
	};
	int ret;
	ret = private_i2c_transfer(client->adapter, &msg, 1);
	if (ret > 0)
		ret = 0;

	return ret;
}

/*
static int sensor_read_array(struct tx_isp_subdev *sd, struct regval_list *vals)
{
	int ret;
	unsigned char val;
	while (vals->reg_num != SENSOR_REG_END) {
		if (vals->reg_num == SENSOR_REG_DELAY) {
			msleep(vals->value);
		} else {
			ret = sensor_read(sd, vals->reg_num, &val);
			if (ret < 0)
				return ret;
		}
		vals++;
	}

	return 0;
}
*/

static int sensor_write_array(struct tx_isp_subdev *sd, struct regval_list *vals)
{
	int ret;
	while (vals->reg_num != SENSOR_REG_END) {
		if (vals->reg_num == SENSOR_REG_DELAY) {
			msleep(vals->value);
		} else {
			ret = sensor_write(sd, vals->reg_num, vals->value);
			if (ret < 0)
				return ret;
		}
		vals++;
	}

	return 0;
}

static int sensor_reset(struct tx_isp_subdev *sd, struct tx_isp_initarg *init)
{
	return 0;
}

static int sensor_detect(struct tx_isp_subdev *sd, unsigned int *ident)
{
	//int ret;
	//unsigned char v;
	//
	//ret = sensor_read(sd, 0x3107, &v);
	//ISP_WARNING("-----%s: %d ret = %d, v = 0x%02x\n", __func__, __LINE__, ret,v);
	//if (ret < 0)
	//	return ret;
	//if (v != SENSOR_CHIP_ID_H)
	//	return -ENODEV;
	//*ident = v;
	//
	//ret = sensor_read(sd, 0x3108, &v);
	//ISP_WARNING("-----%s: %d ret = %d, v = 0x%02x\n", __func__, __LINE__, ret,v);
	//if (ret < 0)
	//	return ret;
	//if (v != SENSOR_CHIP_ID_L)
	//	return -ENODEV;
	//*ident = (*ident << 8) | v;

	return 0;
}

static int sensor_set_expo(struct tx_isp_subdev *sd, int value)
{
	int ret = 0;
    //unsigned int FH ;
    unsigned int FH_h ;
    unsigned int FH_m ;
    unsigned char val;

	int it = 0;
	int again = (value & 0xffff0000) >> 16;
#if 1
	int  SensorVOB=254;
	unsigned char  SensorVOB_LSB=0;
	unsigned char  SensorVOB_MSB=0;
	unsigned char  SensorGain=0;
	int Sensor_BlcLSB=0;
	int Sensor_BlcMSB=0;

	ret += sensor_read(sd, 0x024B, &SensorVOB_LSB);
	ret += sensor_read(sd, 0x024C, &SensorVOB_MSB);
        SensorVOB=(SensorVOB_MSB<<8)+SensorVOB_LSB;
	ret = sensor_read(sd, 0x0212, &SensorGain);

    if ( SensorVOB_MSB>=2) {
        SensorVOB=SensorVOB-767;
    } else {
    	if ( SensorVOB>=182) {
            SensorVOB=SensorVOB+32;
        } else if ( SensorVOB>=102) {
            SensorVOB=SensorVOB+16;
        } else if ( SensorVOB>=72) {
            SensorVOB=SensorVOB+12;
        } else if ( SensorVOB>=12) {
            SensorVOB=SensorVOB+8;
        }
  	}

	if (SensorGain<=100)
		{SensorVOB=SensorVOB;}
	else if (SensorGain<=120)
		{SensorVOB=SensorVOB*2;}
	else if (SensorGain<=140)
		{SensorVOB=SensorVOB*4;}
	else if (SensorGain<=160)
		{SensorVOB=SensorVOB*8;}
	else if (SensorGain<=180)
		{SensorVOB=SensorVOB*16;}
	else if (SensorGain<=200)
		{SensorVOB=SensorVOB*32;}
	else
		{SensorVOB=SensorVOB*64;}

	//if (SensorVOB<768)
	//	{SensorVOB=768-SensorVOB;}   //2FF
	//else
       	//	{SensorVOB=0;}
	Sensor_BlcLSB=SensorVOB&0x00FF;
	Sensor_BlcMSB=(SensorVOB&0xFF00)>>8;
  	ret += sensor_write(sd, 0x01af,Sensor_BlcLSB);
	ret += sensor_write(sd, 0x01b0,Sensor_BlcMSB);
#endif
	ret += sensor_read(sd, 0x0008, &val);
	FH_h = val << 16;
	ret += sensor_read(sd, 0x0007, &val);
        FH_m = val << 8;
	ret += sensor_read(sd, 0x0006, &val);
	it = (FH_h | FH_m | val)-(value & 0xffff);


	ret += sensor_write(sd, 0x000b, (unsigned char)((it >> 16) & 0x3));
	ret += sensor_write(sd, 0x000a, (unsigned char)((it >>  8) & 0xff));
	ret += sensor_write(sd, 0x0009, (unsigned char)( it        & 0xff));
	ret = sensor_write(sd, 0x0212, (unsigned char)(again & 0xff));

	if (ret < 0)
		return ret;
	return 0;
}

/*
static int sensor_set_integration_time(struct tx_isp_subdev *sd, int value)
{
	int ret = 0;

	//value *= 2;
	ret = sensor_write(sd, 0x000b, (unsigned char)((value >> 16) & 0x0f));
	ret += sensor_write(sd, 0x000a, (unsigned char)((value >> 8) & 0xff));
	ret += sensor_write(sd, 0x0009, (unsigned char)( value       & 0x0f));
	if (ret < 0)
		return ret;

	return 0;
}

static int sensor_set_analog_gain(struct tx_isp_subdev *sd, int value)
{
	int ret = 0;

	//ret += sensor_write(sd, 0x3e09, (unsigned char)(value & 0xff));
	//ret += sensor_write(sd, 0x3e08, (unsigned char)((value & 0xff00) >> 8));
        ret = sensor_write(sd, 0x0212, (unsigned char)(value & 0xdc));
	if (ret < 0)
		return ret;

	return 0;
}
*/
static int sensor_set_digital_gain(struct tx_isp_subdev *sd, int value)
{


	return 0;
}

static int sensor_get_black_pedestal(struct tx_isp_subdev *sd, int value)
{
	return 0;
}

static int sensor_init(struct tx_isp_subdev *sd, struct tx_isp_initarg *init)
{
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);
	int ret = 0;

	if (!init->enable)
		return ISP_SUCCESS;

	sensor->video.mbus.width = wsize->width;
	sensor->video.mbus.height = wsize->height;
	sensor->video.mbus.code = wsize->mbus_code;
	sensor->video.mbus.field = TISP_FIELD_NONE;
	sensor->video.mbus.colorspace = wsize->colorspace;
	sensor->video.fps = wsize->fps;
	sensor->video.state = TX_ISP_MODULE_INIT;

	ret = tx_isp_call_subdev_notify(sd, TX_ISP_EVENT_SYNC_SENSOR_ATTR, &sensor->video);
	sensor->priv = wsize;

	return 0;
}

static int sensor_s_stream(struct tx_isp_subdev *sd, struct tx_isp_initarg *init)
{
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);
	int ret = 0;

	if (init->enable) {
		if (sensor->video.state == TX_ISP_MODULE_INIT) {
			ret = sensor_write_array(sd, wsize->regs);
			if (ret)
				return ret;
			sensor->video.state = TX_ISP_MODULE_RUNNING;
		}
		if (sensor->video.state == TX_ISP_MODULE_RUNNING) {

			ret = sensor_write_array(sd, sensor_stream_on_mipi);
			ISP_WARNING("%s stream on\n", SENSOR_NAME);
		}
	}
	else {
		ret = sensor_write_array(sd, sensor_stream_off_mipi);
		ISP_WARNING("%s stream off\n", SENSOR_NAME);
	}

	return ret;
}

static int sensor_set_fps(struct tx_isp_subdev *sd, int fps)
{
        int ret = 0;
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);
	unsigned int sclk = 0;
	unsigned int hts = 0;
	unsigned int vts = 0;
	unsigned int max_fps;
	unsigned char val = 0;
	unsigned int newformat = 0; //the format is 24.8


	switch(sensor_max_fps) {
	case TX_SENSOR_MAX_FPS_30:
		sclk = SENSOR_SUPPORT_SCLK_8M_FPS_30;
		max_fps = SENSOR_OUTPUT_MAX_FPS;
		break;
	case TX_SENSOR_MAX_FPS_15:
		sclk = SENSOR_SUPPORT_SCLK_8M_FPS_15;
		max_fps = TX_SENSOR_MAX_FPS_15;
		break;
	default:
		ISP_ERROR("Now we do not support this framerate!!!\n");
		break;
	}
	newformat = (((fps >> 16) / (fps & 0xffff)) << 8) + ((((fps >> 16) % (fps & 0xffff)) << 8) / (fps & 0xffff));
	if (newformat > (max_fps<< 8) || newformat < (SENSOR_OUTPUT_MIN_FPS << 8)) {
		ISP_ERROR("warn: fps(%d) not in range\n", fps);
		return -1;
	}
	//sclk = SENSOR_SUPPORT_SCLK_8M_FPS_30

	ret += sensor_read(sd, 0x0077, &val);
	hts = val << 8;
	ret += sensor_read(sd, 0x0076, &val);
	hts = (hts | val);
	if (0 != ret) {
		ISP_ERROR("Error: %s read error\n", SENSOR_NAME);
		return -1;
	}
	vts = sclk * (fps & 0xffff) / hts / ((fps & 0xffff0000) >> 16);
	ret = sensor_write(sd, 0x0008, (unsigned char)(vts >> 16));
	ret += sensor_write(sd, 0x0007, (unsigned char)(vts >> 8));
	ret += sensor_write(sd, 0x0006, (unsigned char)(vts & 0xff));
	//expo_val = vts-10;
	if (0 != ret) {
		ISP_ERROR("Error: %s write error\n", SENSOR_NAME);
		return ret;
	}
	sensor->video.fps = fps;
	sensor->video.attr->max_integration_time_native = vts -2;
	sensor->video.attr->integration_time_limit = vts -2;
	sensor->video.attr->total_height = vts;
	sensor->video.attr->max_integration_time = vts -2;
	ret = tx_isp_call_subdev_notify(sd, TX_ISP_EVENT_SYNC_SENSOR_ATTR, &sensor->video);

	return ret;
}

static int sensor_set_mode(struct tx_isp_subdev *sd, int value)
{
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);
	int ret = ISP_SUCCESS;

	if (wsize) {
		sensor->video.mbus.width = wsize->width;
		sensor->video.mbus.height = wsize->height;
		sensor->video.mbus.code = wsize->mbus_code;
		sensor->video.mbus.field = TISP_FIELD_NONE;
		sensor->video.mbus.colorspace = wsize->colorspace;
		sensor->video.fps = wsize->fps;
		ret = tx_isp_call_subdev_notify(sd, TX_ISP_EVENT_SYNC_SENSOR_ATTR, &sensor->video);
	}

	return ret;
}

static int sensor_attr_check(struct tx_isp_subdev *sd)
{
	struct tx_isp_sensor *sensor = sd_to_sensor_device(sd);
	struct tx_isp_sensor_register_info *info = &sensor->info;
	unsigned long rate;

	switch(info->default_boot) {
	case 0:
		wsize = &sensor_win_sizes[0];
		sensor_max_fps = TX_SENSOR_MAX_FPS_15;
		sensor_attr.data_type = TX_SENSOR_DATA_TYPE_LINEAR;
		sensor_attr.max_integration_time_native =4490-2;
		sensor_attr.integration_time_limit =4490-2;
		sensor_attr.total_width = 550;
		sensor_attr.total_height = 4490;
		sensor_attr.max_integration_time = 4490-2;
		sensor_attr.mipi.mipi_sc.sensor_csi_fmt = TX_SENSOR_RAW12;
		sensor_attr.mipi.image_twidth =3840;
		sensor_attr.mipi.image_theight =2160;
		sensor->video.vi_max_width = wsize->width;
		sensor->video.vi_max_height = wsize->height;
	        sensor_attr.again = 0;
                sensor_attr.integration_time = 0x462;
		break;
	case 1:
		wsize = &sensor_win_sizes[1];
		sensor_max_fps = TX_SENSOR_MAX_FPS_30;
		sensor_attr.data_type = TX_SENSOR_DATA_TYPE_LINEAR;
		sensor_attr.max_integration_time_native =2695-2;
		sensor_attr.integration_time_limit =2695-2;
		sensor_attr.total_width = 550;
		sensor_attr.total_height = 2695;
		sensor_attr.max_integration_time = 2695-2;
		sensor_attr.mipi.mipi_sc.sensor_csi_fmt = TX_SENSOR_RAW12;
		sensor_attr.mipi.image_twidth =3840;
		sensor_attr.mipi.image_theight =2160;
		sensor->video.vi_max_width = wsize->width;
		sensor->video.vi_max_height = wsize->height;
	        sensor_attr.again = 0;
                sensor_attr.integration_time = 0x462;
		break;
	case 2:
		wsize = &sensor_win_sizes[2];
		sensor_max_fps = TX_SENSOR_MAX_FPS_30;
		sensor_attr.data_type = TX_SENSOR_DATA_TYPE_LINEAR;
		memcpy((void*)(&(sensor_attr.mipi)),(void*)(&sensor_mipi_2M),sizeof(sensor_mipi_2M));
		sensor_attr.max_integration_time_native =2250-2;
		sensor_attr.integration_time_limit =2250-2;
		sensor_attr.total_width = 550;
		sensor_attr.total_height = 2250;
		sensor_attr.max_integration_time = 2250-2;
		sensor_attr.mipi.mipi_sc.sensor_csi_fmt = TX_SENSOR_RAW12;
		sensor_attr.mipi.image_twidth =1920;
		sensor_attr.mipi.image_theight =1080;
		sensor->video.vi_max_width = wsize->width;
		sensor->video.vi_max_height = wsize->height;
	        sensor_attr.again = 0;
                sensor_attr.integration_time = 0x465;
		break;
	default:
		ISP_ERROR("Have no this MCLK Source!!!\n");
	}

	switch(info->video_interface) {
	case TISP_SENSOR_VI_MIPI_CSI0:
		sensor_attr.dbus_type = TX_SENSOR_DATA_INTERFACE_MIPI;
		sensor_attr.mipi.index = 0;
		break;
	default:
		ISP_ERROR("Have no this MCLK Source!!!\n");
	}

	switch(info->mclk) {
	case TISP_SENSOR_MCLK0:
		sensor->mclk = private_devm_clk_get(sensor->dev, "div_cim0");
		set_sensor_mclk_function(0);
		break;
	case TISP_SENSOR_MCLK1:
		sensor->mclk = private_devm_clk_get(sensor->dev, "div_cim1");
		set_sensor_mclk_function(1);
		break;
	case TISP_SENSOR_MCLK2:
		sensor->mclk = private_devm_clk_get(sensor->dev, "div_cim2");
		set_sensor_mclk_function(2);
		break;
	default:
		ISP_ERROR("Have no this MCLK Source!!!\n");
	}

	rate = private_clk_get_rate(sensor->mclk);
	if (IS_ERR(sensor->mclk)) {
		ISP_ERROR("Cannot get sensor input clock cgu_cim\n");
		goto err_get_mclk;
	}
	private_clk_set_rate(sensor->mclk, 27000000);
	private_clk_prepare_enable(sensor->mclk);

	reset_gpio = info->rst_gpio;
	pwdn_gpio = info->pwdn_gpio;

	return 0;

err_get_mclk:
	return -1;
}

static int sensor_g_chip_ident(struct tx_isp_subdev *sd,
			       struct tx_isp_chip_ident *chip)
{
	struct i2c_client *client = tx_isp_get_subdevdata(sd);
	unsigned int ident = 0;
	int ret = ISP_SUCCESS;

	sensor_attr_check(sd);
	if (reset_gpio != -1) {
		ret = private_gpio_request(reset_gpio,"sensor_reset");
		if (!ret) {
			private_gpio_direction_output(reset_gpio, 1);
			private_msleep(5);
			private_gpio_direction_output(reset_gpio, 0);
			private_msleep(5);
			private_gpio_direction_output(reset_gpio, 1);
			private_msleep(5);
		} else {
			ISP_ERROR("gpio request fail %d\n",reset_gpio);
		}
	}
	if (pwdn_gpio != -1) {
		ret = private_gpio_request(pwdn_gpio,"sensor_pwdn");
		if (!ret) {
			private_gpio_direction_output(pwdn_gpio, 0);
			private_msleep(5);
			private_gpio_direction_output(pwdn_gpio, 1);
			private_msleep(5);
		} else {
			ISP_ERROR("gpio request fail %d\n",pwdn_gpio);
		}
	}
	ret = sensor_detect(sd, &ident);
	if (ret) {
		ISP_ERROR("chip found @ 0x%x (%s) is not an %s chip.\n",
			  client->addr, client->adapter->name, SENSOR_NAME);
		return ret;
	}
	ISP_WARNING("%s chip found @ 0x%02x (%s)\n",
		    SENSOR_NAME, client->addr, client->adapter->name);
	ISP_WARNING("sensor driver version %s\n",SENSOR_VERSION);
	if (chip) {
		memcpy(chip->name, SENSOR_NAME, sizeof(SENSOR_NAME));
		chip->ident = ident;
		chip->revision = SENSOR_VERSION;
	}

	return 0;
}

static int sensor_sensor_ops_ioctl(struct tx_isp_subdev *sd, unsigned int cmd, void *arg)
{
	long ret = 0;
	struct tx_isp_sensor_value *sensor_val = arg;


	if (IS_ERR_OR_NULL(sd)) {
		ISP_ERROR("[%d]The pointer is invalid!\n", __LINE__);
		return -EINVAL;
	}
	switch(cmd) {
	case TX_ISP_EVENT_SENSOR_EXPO:
		if (arg)
			ret = sensor_set_expo(sd, sensor_val->value);
		break;
	case TX_ISP_EVENT_SENSOR_INT_TIME:
		//if (arg)
		//	ret = sensor_set_integration_time(sd, sensor_val->value);
		break;
	case TX_ISP_EVENT_SENSOR_AGAIN:
		//if (arg)
		//	ret = sensor_set_analog_gain(sd, sensor_val->value);
		break;
	case TX_ISP_EVENT_SENSOR_DGAIN:
		if (arg)
			ret = sensor_set_digital_gain(sd, sensor_val->value);
		break;
	case TX_ISP_EVENT_SENSOR_BLACK_LEVEL:
		if (arg)
			ret = sensor_get_black_pedestal(sd, sensor_val->value);
		break;
	case TX_ISP_EVENT_SENSOR_RESIZE:
		if (arg)
			ret = sensor_set_mode(sd, sensor_val->value);
		break;
	case TX_ISP_EVENT_SENSOR_PREPARE_CHANGE:
		ret = sensor_write_array(sd, sensor_stream_off_mipi);
		break;
	case TX_ISP_EVENT_SENSOR_FINISH_CHANGE:
		ret = sensor_write_array(sd, sensor_stream_on_mipi);
		break;
	case TX_ISP_EVENT_SENSOR_FPS:
		if (arg)
			ret = sensor_set_fps(sd, sensor_val->value);
		break;
	default:
		break;
	}

	return ret;
}

static int sensor_g_register(struct tx_isp_subdev *sd, struct tx_isp_dbg_register *reg)
{
	unsigned char val = 0;
	int len = 0;
	int ret = 0;

	len = strlen(sd->chip.name);
	if (len && strncmp(sd->chip.name, reg->name, len)) {
		return -EINVAL;
	}
	if (!private_capable(CAP_SYS_ADMIN))
		return -EPERM;
	ret = sensor_read(sd, reg->reg & 0xffff, &val);
	reg->val = val;
	reg->size = 2;

	return ret;
}

static int sensor_s_register(struct tx_isp_subdev *sd, const struct tx_isp_dbg_register *reg)
{
	int len = 0;

	len = strlen(sd->chip.name);
	if (len && strncmp(sd->chip.name, reg->name, len)) {
		return -EINVAL;
	}
	if (!private_capable(CAP_SYS_ADMIN))
		return -EPERM;
	sensor_write(sd, reg->reg & 0xffff, reg->val & 0xff);

	return 0;
}

static struct tx_isp_subdev_core_ops sensor_core_ops = {
	.g_chip_ident = sensor_g_chip_ident,
	.reset = sensor_reset,
	.init = sensor_init,
	.g_register = sensor_g_register,
	.s_register = sensor_s_register,
};

static struct tx_isp_subdev_video_ops sensor_video_ops = {
	.s_stream = sensor_s_stream,
};

static struct tx_isp_subdev_sensor_ops sensor_sensor_ops = {
	.ioctl = sensor_sensor_ops_ioctl,
};

static struct tx_isp_subdev_ops sensor_ops = {
	.core = &sensor_core_ops,
	.video = &sensor_video_ops,
	.sensor = &sensor_sensor_ops,
};

/* It's the sensor device */
static u64 tx_isp_module_dma_mask = ~(u64)0;
struct platform_device sensor_platform_device = {
	.name = SENSOR_NAME,
	.id = -1,
	.dev = {
		.dma_mask = &tx_isp_module_dma_mask,
		.coherent_dma_mask = 0xffffffff,
		.platform_data = NULL,
	},
	.num_resources = 0,
};


static int sensor_probe(struct i2c_client *client,
			const struct i2c_device_id *id)
{
	struct tx_isp_subdev *sd;
	struct tx_isp_video_in *video;
	struct tx_isp_sensor *sensor;

	sensor = (struct tx_isp_sensor *)kzalloc(sizeof(*sensor), GFP_KERNEL);
	if (!sensor) {
		ISP_ERROR("Failed to allocate sensor subdev.\n");
		return -ENOMEM;
	}
	memset(sensor, 0 ,sizeof(*sensor));

       	sd = &sensor->sd;
	video = &sensor->video;
	sensor->dev = &client->dev;
	sensor_attr.expo_fs = 1;
	sensor->video.attr = &sensor_attr;
	sensor->video.vi_max_width = wsize->width;
	sensor->video.vi_max_height = wsize->height;
	sensor->video.mbus.width = wsize->width;
	sensor->video.mbus.height = wsize->height;
	sensor->video.mbus.code = wsize->mbus_code;
	sensor->video.mbus.field = TISP_FIELD_NONE;
	sensor->video.mbus.colorspace = wsize->colorspace;
	sensor->video.fps = wsize->fps;
	tx_isp_subdev_init(&sensor_platform_device, sd, &sensor_ops);
	tx_isp_set_subdevdata(sd, client);
	tx_isp_set_subdev_hostdata(sd, sensor);
	private_i2c_set_clientdata(client, sd);

	pr_debug("probe ok ------->%s\n", SENSOR_NAME);

	return 0;
}

static int sensor_remove(struct i2c_client *client)
{
	struct tx_isp_subdev *sd = private_i2c_get_clientdata(client);
	struct tx_isp_sensor *sensor = tx_isp_get_subdev_hostdata(sd);

	if (reset_gpio != -1)
		private_gpio_free(reset_gpio);
	if (pwdn_gpio != -1)
		private_gpio_free(pwdn_gpio);

	private_clk_disable_unprepare(sensor->mclk);
	private_devm_clk_put(&client->dev, sensor->mclk);
	tx_isp_subdev_deinit(sd);
	kfree(sensor);

	return 0;
}

static const struct i2c_device_id sensor_id[] = {
	{ SENSOR_NAME, 0 },
	{ }
};
MODULE_DEVICE_TABLE(i2c, sensor_id);

static struct i2c_driver sensor_driver = {
	.driver = {
		.owner = THIS_MODULE,
		.name = SENSOR_NAME,
	},
	.probe = sensor_probe,
	.remove = sensor_remove,
	.id_table = sensor_id,
};

static __init int init_sensor(void)
{
	return private_i2c_add_driver(&sensor_driver);
}

static __exit void exit_sensor(void)
{
	private_i2c_del_driver(&sensor_driver);
}

module_init(init_sensor);
module_exit(exit_sensor);

MODULE_DESCRIPTION("A low-level driver for "SENSOR_NAME" sensor");
MODULE_LICENSE("GPL");
